'use client';

import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import OtpInput from 'react-otp-input';
// import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import CustomButton from '@/components/UI/button';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './otp.scss';

export default function OTP() {
  // const { authState, setAuthState } = useContext(AuthContext);
  const [OTP, setOTP] = useState();
  const router = useRouter();
  // const [loader, setLoader] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);

  const handleChange = (otp) => {
    setOTP(otp);
    setIsSubmit(false);
  };
  const resendOTP = async () => {
    const user = fetchFromStorage(identifiers?.USER_ID);
    let sendData;

    sendData = {
      userId: user?.id ? user?.id : '',
    };

    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.post(
        ORG_URLS.RESEND_OTP,
        sendData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
        // setLoader(false);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const verifyOTP = async () => {
    const user = fetchFromStorage(identifiers?.USER_ID);
    let sendData;

    sendData = {
      userId: user?.id ? user?.id : '',
      otp: OTP,
    };

    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.post(
        ORG_URLS.VERIFY_OTP,
        sendData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          router.push('/org/reset-password');
        } else {
          setApiMessage('error', data?.message);
        }

        // setLoader(false);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  return (
    <>
      <Box className="login-page d-flex justify-center align-center">
        <Box className="login-block d-flex justify-center">
          {/* <Box className="login-screen">
            <Image
              src={NVlogo}
              className="header-logo cursor-pointer "
              alt="img"
              onClick={() => router.push('/login')}
            />
          </Box> */}
          <Typography
            variant="h3"
            className="h3 main-heading fw700 pt16 text-align"
          >
            OTP
          </Typography>

          <Box className="pt64">
            <Box
              className={
                !OTP && isSubmit
                  ? 'otp-section otp-error'
                  : OTP?.length !== 4 && isSubmit
                    ? 'otp-section otp-error'
                    : 'otp-section'
              }
            >
              <OtpInput
                value={OTP}
                onChange={handleChange}
                autoFocus
                OTPLength={4}
                numInputs={4}
                // renderSeparator={<span>-</span>}
                renderInput={(props) => (
                  <input
                    {...props}
                    onKeyPress={(e) => {
                      if (/[^0-9,.\s]/.test(e.key)) {
                        e.preventDefault();
                      }
                    }}
                    onPaste={(e) => {
                      if (/^[0-9,.]+$/.test(e.clipboardData.getData('text'))) {
                        // setValue(pasteData);
                      } else {
                        e.preventDefault();
                      }
                    }}
                  />
                )}
                secure
              />
              {!OTP && isSubmit && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error otp-error pt4 text-align"
                >
                  This field is required
                </Typography>
              )}
              {OTP && OTP?.length !== 4 && isSubmit && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error otp-error pt4 text-align"
                >
                  OTP must be at least 4 digit
                </Typography>
              )}
            </Box>
          </Box>
          <Typography
            variant="h6"
            className="p16 forgot-content fw400 text-align  pt40"
          >
            Did you don't get code?
            <span
              className="fw700 color-red cursor-pointer "
              onClick={() => resendOTP()}
            >
              {' '}
              Resend
            </span>
          </Typography>
          <Box className="pt32 text-align">
            <CustomButton
              variant="contained"
              background="#d93434"
              backgroundhover="#FFFFFF"
              colorhover="#000000"
              className="p16 save-btn verify-button"
              fontWeight="600"
              title="Verify"
              type="submit"
              onClick={() => {
                setIsSubmit(true);
                if (OTP && OTP?.length === 4) {
                  verifyOTP();
                }
              }}
              fullWidth={false}
            />
          </Box>
        </Box>
      </Box>
    </>
  );
}
