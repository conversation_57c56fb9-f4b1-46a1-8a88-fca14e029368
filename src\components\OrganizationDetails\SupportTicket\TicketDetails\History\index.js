'use client';
import React, { useState } from 'react';
import { Box, MenuItem, Select, Typography } from '@mui/material';
import { EmptyHistorySVG } from '@/helper/common/images';
import TimeHistory from './TimeHistory';
import AttachmentHistory from './AttachmentHistory';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import SingleSelect from '@/components/UI/SelectWithSearch';
import './history.scss';

export default function History() {
  const dummyHistory = [
    {
      name: '<PERSON>',
      date: new Date('2024-12-05T10:05:00'),
      timeSpent: 3 * 60 + 38,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-06T12:15:00'),
      timeSpent: 2 * 60 + 30,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-07T14:45:00'),
      timeSpent: 4 * 60 + 15,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-08T09:00:00'),
      timeSpent: 5 * 60 + 45,
    },
  ];
  const attachmentHistory = [
    {
      name: '<PERSON>',
      date: new Date('2024-12-05T10:11:00'),
      fileStatus: 'File deleted',
      fileName:
        'canva-letter-c-trade-marketing-logo-design-template-r9VFYrbB35Y.jpg',
      attachmentType: 'Private',
    },
    {
      name: 'Jane Doe',
      date: new Date('2024-12-06T12:11:00'),
      fileStatus: 'File edited',
      fileName: 'marketing-report-final.pdf',
      attachmentType: 'Public',
    },
    {
      name: 'Mark Smith',
      date: new Date('2024-12-07T14:11:00'),
      fileStatus: 'File attached',
      fileName: 'project-proposal.docx',
      attachmentType: 'Private',
    },
    {
      name: 'Emily Clark',
      date: new Date('2024-12-08T09:11:00'),
      fileStatus: 'File attached',
      fileName: 'presentation.pptx',
      attachmentType: 'Public',
    },
  ];

  const assigneeOptions = [
    { label: 'None', value: 'none' },
    { label: 'Time Entries', value: 'time entries' },
    { label: 'Attachment', value: 'attachment' },
  ];

  const [filter, setFilter] = useState('all');
  const [assignee, setAssignee] = useState('none');

  // Handle "Time history" filter change
  const handleFilterChange = (event) => {
    const selectedFilter = event.target.value;
    setFilter(selectedFilter);
  };

  // Handle "Filter by" selection change
  const handleAssigneeChange = (selectedOption) => {
    const selectedValue = selectedOption?.value || 'none';
    setAssignee(selectedValue);
  };

  return (
    <Box className="history-wrap">
      <Box className="filter-wrap d-flex align-center justify-space-between">
        <Box className="d-flex align-center ticket-history-filter">
          <Typography
            className="ticket-history"
            component="label"
            htmlFor="history-filter"
          >
            Ticket history -
          </Typography>
          <Select
            id="history-filter"
            value={filter}
            onChange={handleFilterChange}
            displayEmpty
            className="filter-select"
          >
            <MenuItem value="all">All History</MenuItem>
            <MenuItem value="my">My History</MenuItem>
          </Select>
          <ArrowDropDownIcon />
        </Box>

        <Box className="d-flex align-center">
          <Typography
            className="filter-by-wrap"
            component="label"
            htmlFor="assignee-filter"
          >
            Filter by :
          </Typography>
          <SingleSelect
            placeholder="Select"
            className="selected-wrap filter-by-select"
            options={assigneeOptions}
            value={assigneeOptions.find((opt) => opt?.value === assignee) || ''}
            name="assignee"
            onChange={handleAssigneeChange}
          />
        </Box>
      </Box>

      {assignee === 'none' ? (
        <Box className="d-flex justify-center align-center flex-col pt16 pb32">
          <EmptyHistorySVG />
          <Typography className="no-history-wrap">
            No history entries found
          </Typography>
        </Box>
      ) : assignee === 'time entries' ? (
        <TimeHistory timeHistory={dummyHistory} />
      ) : (
        <AttachmentHistory attachmentData={attachmentHistory} />
      )}
    </Box>
  );
}
