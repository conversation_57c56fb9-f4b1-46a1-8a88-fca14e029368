import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './ServingInfoCard.scss';

const ServingInfoCard = ({ recipeData }) => {
  return (
    <div className="serving-info-card">
      <div className="serving-info-card__header">
        <p className="serving-info-card__title">
          <Icon name="Utensils" size={18} color="currentColor" />
          <span>Serving & Presentation</span>
        </p>
      </div>

      <div className="serving-info-card__content">
        <div className="serving-info-card__info">
          {recipeData?.recipe_serving_method && (
            <div className="serving-info-card__item">
              <p className="serving-info-card__label">Serving Method:</p>
              <p className="serving-info-card__text">
                {recipeData.recipe_serving_method}
              </p>
            </div>
          )}

          {recipeData?.recipe_serve_in && (
            <div className="serving-info-card__item">
              <p className="serving-info-card__label">Serve In:</p>
              <p className="serving-info-card__text">
                {recipeData.recipe_serve_in}
              </p>
            </div>
          )}

          {recipeData?.recipe_garnish && (
            <div className="serving-info-card__item">
              <p className="serving-info-card__label">Garnish:</p>
              <p className="serving-info-card__text">
                {recipeData.recipe_garnish}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServingInfoCard;
