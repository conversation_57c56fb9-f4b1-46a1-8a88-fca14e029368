import React, { useContext, useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import SplideThumbnailCarousel from '@/components/SplideCarousel/SplideThumbnailCarousel';
import MediaIconsCarousel from '@/components/UI/MediaIconsCarousel';
import AudioPlayer from '@/components/UI/AudioPlayer';
import NutritionCard from '../NutritionCard';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import Image from 'next/image';
import './RecipeHeader.scss';

const RecipeHeader = ({ recipeData }) => {
  const [selectedAudio, setSelectedAudio] = useState(null);
  const { authState } = useContext(AuthContext);
  const currency = getCurrencySymbol(authState?.currency_details);

  const handleAudioClick = (audioItem) => {
    setSelectedAudio(audioItem);
  };

  const handleCloseAudio = () => {
    setSelectedAudio(null);
  };

  // Helper function to get appropriate icon based on resource type
  const getIconForResourceType = (type) => {
    switch (type) {
      case 'youtube':
        return 'Youtube';
      case 'pdf':
        return 'FileText';
      case 'audio':
        return 'Music';
      default:
        return 'Link';
    }
  };

  // Transform resources into media data for SplideThumbnailCarousel
  const mediaData =
    recipeData?.resources?.map((resource) => resource.item_detail.item_link) ||
    [];

  // Transform resources into media icons for MediaIconsCarousel
  const mediaIcons =
    recipeData?.resources?.map((resource) => ({
      id: resource.id,
      type: resource.type,
      icon: getIconForResourceType(resource?.item_detail?.item_type),
      url: resource.item_detail.item_link,
    })) || [];

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Easy':
        return 'recipe-card__difficulty--easy';
      case 'Medium':
        return 'recipe-card__difficulty--medium';
      case 'Hard':
        return 'recipe-card__difficulty--hard';
      default:
        return 'recipe-card__difficulty--default';
    }
  };

  return (
    <div className="recipe-header">
      <div className="recipe-header__content">
        <div className="recipe-header__info">
          <SplideThumbnailCarousel media={mediaData} thumbPosition="right" />

          {selectedAudio && (
            <AudioPlayer
              audioUrl={selectedAudio.url}
              onClose={handleCloseAudio}
            />
          )}

          {/* Media Icons Carousel */}
          <MediaIconsCarousel
            mediaIcons={mediaIcons}
            onAudioClick={handleAudioClick}
          />

          <div className="recipe-header__info-content">
            <NutritionCard recipeData={recipeData} />
          </div>
        </div>

        <div className="recipe-header__stats">
          <div className="recipe-header__title-info">
            <p className="recipe-header__title">{recipeData.recipe_title}</p>
            <p className="recipe-header__subtitle">
              {recipeData.recipe_description}
            </p>
          </div>
          <div className="recipe-header__stats-grid">
            <div className="recipe-header__stat-card recipe-header__stat-card--time">
              <Icon
                name="Clock"
                size={16}
                color="var(--color-warning)"
                className="recipe-header__stat-icon"
              />
              <p className="recipe-header__stat-value">
                {recipeData.recipe_cook_time} min
              </p>
              <p className="recipe-header__stat-label">Total Time</p>
            </div>

            <div className="recipe-header__stat-card recipe-header__stat-card--portions">
              <Icon
                name="Users"
                size={16}
                color="var(--color-success)"
                className="recipe-header__stat-icon"
              />
              <p className="recipe-header__stat-value">
                {recipeData?.recipe_yield}
              </p>
              <p className="recipe-header__stat-label">Portions</p>
            </div>

            <div className="recipe-header__stat-card recipe-header__stat-card--cost">
              <Icon
                name="DollarSign"
                size={16}
                color="var(--color-danger)"
                className="recipe-header__stat-icon"
              />
              <p className="recipe-header__stat-value">
                {currency}
                {recipeData.recipe_total_portions}
              </p>
              <p className="recipe-header__stat-label">Per Portion</p>
            </div>

            <div className="recipe-header__stat-card recipe-header__stat-card--size">
              <Icon
                name="Scale"
                size={20}
                color="var(--color-warning)"
                className="recipe-header__stat-icon"
              />
              <p className="recipe-header__stat-value">
                {recipeData?.recipe_single_portion_size}g
              </p>
              <p className="recipe-header__stat-label">Portion Size</p>
            </div>
          </div>
          <div className="recipe-header__categories">
            {recipeData.categories.map((category, index) => (
              <span key={index} className="recipe-header__category-tag">
                {category?.category_name}
              </span>
            ))}
          </div>
          <div
            className={`recipe-header__difficulty ${getDifficultyColor(recipeData?.recipe_complexity_level)}`}
          >
            <Icon name="ChefHat" size={18} />
            <p className="recipe-header__difficulty-title">
              {recipeData?.recipe_complexity_level}
            </p>
          </div>
          {recipeData?.allergen_attributes?.contains &&
            recipeData?.allergen_attributes?.contains.length > 0 && (
              <div className="recipe-header__allergen-warning">
                <Icon
                  name="AlertTriangle"
                  size={20}
                  className="recipe-header__allergen-icon"
                />
                <div>
                  <p className="recipe-header__allergen-title">
                    Allergen Warning
                  </p>

                  <div className="recipe-header__allergen-icons-wrap">
                    {recipeData?.allergen_attributes?.contains.map(
                      (allergen, index) => (
                        <Image
                          key={index}
                          src={allergen.item_detail.item_link}
                          alt={allergen.attribute_title}
                          width={24}
                          height={24}
                          className="recipe-header__allergen-icon"
                          title={allergen.attribute_title}
                        />
                      )
                    )}
                  </div>
                </div>
              </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default RecipeHeader;
