@import '@/styles/variable.scss';

.transaction-history {
  .transaction-history-header {
    padding: var(--spacing-lg) var(--spacing-none);
    .header-text-wrap {
      font-size: var(--font-size-base);
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-semibold);
      @media (max-width: 575px) {
        width: 100%;
      }
    }

    .MuiButton-contained {
      font-size: 14px;
    }

    @media (max-width: 575px) {
      flex-direction: column;
      row-gap: 10px;
    }
  }

  .download-btn-wrap {
    .download-btn {
      padding: 5px 0px;
      min-width: 40px !important;
      .MuiButton-startIcon {
        margin-right: 0px;
        margin-left: 0px !important;
      }
    }
  }
  .staff-leave-search {
    transition: all 0.3s ease-in-out;
    max-width: 120px !important;

    .MuiInputBase-root {
      min-height: 30px;
      border-radius: 4px !important;
      transition: all 0.3s ease-in-out; // Add smooth transition for focus

      .MuiInputBase-input {
        padding: 2px 10px !important;

        &::placeholder {
          font-size: 14px;
        }
      }

      .MuiOutlinedInput-notchedOutline {
        border-color: $color-Dark-10 !important; // No need for "border-color: none"
      }
    }

    // Increase width on hover
    &:hover {
      max-width: 220px !important;
    }

    // Keep width increased when input is focused
    &:focus-within {
      max-width: 220px !important;
    }

    // Responsive max-width for different screen sizes
    // @media (max-width: 1199px) {
    //   max-width: 250px !important;
    // }

    @media (max-width: 575px) {
      max-width: 100% !important;
      width: 100% !important;
    }
  }
  .transaction-table-wrap {
    .table-scroll {
      border: 1px solid #e0e0e0;
      margin-top: 20px;
      border-radius: 8px;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;

      .transaction-table {
        width: 100%;
        border-collapse: unset;
        min-width: 830px;

        .payment-invoice {
          .arrow-wrap {
            height: 15px;
            width: 15px;
            margin-left: 3px;
          }
        }

        .MuiTableCell-root {
          font-size: 14px;
          padding: 8px 16px;
          border-bottom: 1px solid #e0e0e0;
        }

        .success {
          background-color: white;
          border: none;
          border-radius: 0px;
          border-bottom: 1px solid #e0e0e0;
        }

        .failed {
          border: none;
          background-color: white;
          border-bottom: 1px solid #e0e0e0;
          border-radius: 0px;
        }

        .status {
          font-weight: bold;
          text-transform: capitalize;
        }

        .MuiTableBody-root {
          .MuiTableRow-root:last-child {
            .MuiTableCell-root {
              border-bottom: none !important;
              border-radius: 8px;
            }
          }
        }
      }
    }
    .invoice-table {
      padding: var(--spacing-none) var(--spacing-none) var(--spacing-lg)
        var(--spacing-none);
      margin: var(--spacing-none);
      .MuiDataGrid-root {
        margin-top: 0px;
        .MuiDataGrid-cell {
          display: flex;
          align-items: center;
          justify-content: center;
          .table-body-text {
            font-family: var(--font-family-primary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-regular);
          }
          .transaction-date-wrap {
            font-size: var(--font-size-sm);
          }
          .table-body-status-text {
            font-size: var(--font-size-xs);
          }
          .action-icon {
            font-size: var(--font-size-base);
          }
          .action-icon-wrap {
            fill: var(--icon-color-primary);
          }
        }
        .MuiCheckbox-root {
          .MuiSvgIcon-root {
            fill: var(--icon-color-slate-gray);
            font-size: 21px;
          }
        }
        .Mui-checked {
          .MuiSvgIcon-root {
            fill: var(--icon-color-primary);
          }
        }
        .arrow-wrap {
          .arrow-icon {
            margin-left: 5px;
            height: 15px;
            width: 15px;
          }
        }
        .status-failed,
        .status-pending,
        .status-paid {
          font-family: var(--font-family-primary);
          font-size: var(--font-size-xs);
          border-radius: 50px;
          padding: var(--spacing-tiny) var(--spacing-lg);
          background-color: var(--color-light-green);
          color: var(--text-green);
          text-transform: capitalize;
        }
        .status-pending {
          background-color: var(--color-light-champagne);
          color: var(--text-muted-mustard);
        }
        .status-failed {
          background-color: var(--color-danger-background);
          color: var(--text-color-danger);
        }
      }
      .amount-text {
        display: inline-block;
        margin-left: 5px;
      }
      .wrap-header-text,
      .wrap-header-text .MuiDataGrid-columnHeaderTitle {
        white-space: normal !important;
        text-overflow: unset !important;
        overflow: visible !important;
        word-break: keep-all;
        line-height: 1.2;
        text-align: center;
      }
      @media (max-width: 899px) {
        .MuiDataGrid-topContainer {
          height: 48px !important;
        }
        .MuiDataGrid-columnHeader {
          height: 48px !important;
          min-height: 48px !important;
        }

        .MuiDataGrid-cell {
          height: 60px !important;
          line-height: 60px !important;
          // display: flex;
          // align-items: center;
        }

        .MuiDataGrid-row {
          height: 60px !important;
          min-height: 60px !important;
        }
      }
    }
    .transaction-pagination {
      border: none;
      padding-top: var(--border-radius-xl) !important;
    }
  }

  .MuiCheckbox-root {
    padding: 0;
  }

  .MuiButton-outlined {
    font-size: 12px;
    padding: 4px 8px;
    color: #1976d2;
    border-color: #1976d2;

    &:hover {
      background-color: #f0f0f0;
    }
  }

  .no-data {
    min-height: calc(100vh - 153px - var(--banner-height));
  }
}
body {
  .transaction-history-modal {
    .MuiDialog-paperScrollPaper {
      padding: var(--spacing-none);
      width: 100%;
      max-width: 600px;
      margin: 0 auto;
      max-height: calc(100vh - 30px - var(--banner-height));
      top: 30px;
      .MuiSvgIcon-root {
        font-size: var(--icon-size-md);
      }
      .dialog-title-wrap {
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .divider {
        display: none;
        margin: var(--spacing-sm) var(--spacing-none);
      }
    }
  }
}
