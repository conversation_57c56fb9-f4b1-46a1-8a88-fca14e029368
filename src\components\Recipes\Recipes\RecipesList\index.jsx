'use client';
import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import RecipeGrid from './components/RecipeGrid';
import FilterPanel from './components/FilterPanel';
import { Divider } from '@mui/material';
import { useRouter, usePathname } from 'next/navigation';
import {
  getRecipeCategoryList,
  updateBookmark,
  duplicateRecipe,
  deleteRecipe,
  // Import new utility functions
  isPublicRoute,
  getRecipeListByRouteType,
  getRouteFeatures,
  getRouteBasedClassName,
  conditionalApiCall,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomButton from '@/components/UI/CustomButton';
import Image from '@/components/UI/AppImage/AppImage';
import './recipeslisting.scss';

const RecipesList = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [recipes, setRecipes] = useState([]);
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(['all']);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [apiCallInProgress, setApiCallInProgress] = useState(false);
  const [categories, setCategories] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [publicAllergens, setPublicAllergens] = useState([]); // For public route allergens
  const [publicDietary, setPublicDietary] = useState([]); // For public route dietary info
  const [filters, setFilters] = useState({
    allergens: [],
    costRange: [0, 100],
    dietary: [],
    difficulty: '',
    recipe_cook_time: '',
    ownership: '',
  });

  // Use utility function to determine route type
  const isPublic = isPublicRoute(pathname);

  // Get route-based features and permissions
  const routeFeatures = getRouteFeatures(pathname);

  // Function to extract unique categories from public API response
  const extractCategoriesFromRecipes = (recipesList) => {
    const uniqueCategories = new Set();
    const categoryMap = new Map();

    recipesList?.forEach((recipe) => {
      recipe?.categories?.forEach((category) => {
        if (category?.id && !uniqueCategories.has(category.id)) {
          uniqueCategories.add(category.id);
          categoryMap.set(category.id, {
            id: category.id,
            label: category.category_name || category.label,
            icon_url: category.iconItem?.iconUrl || '',
          });
        }
      });
    });

    return [
      { id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' },
      ...Array.from(categoryMap.values()),
    ];
  };

  // Function to extract unique allergens from public API response
  const extractAllergensFromRecipes = (recipesList) => {
    const uniqueAllergens = new Set();
    const allergenMap = new Map();

    recipesList?.forEach((recipe) => {
      recipe?.allergens?.forEach((allergen) => {
        if (allergen?.id && !uniqueAllergens.has(allergen.id)) {
          uniqueAllergens.add(allergen.id);
          allergenMap.set(allergen.id, {
            id: allergen.id,
            value: allergen.id,
            label: allergen.attribute_title || allergen.label,
          });
        }
      });
    });

    return Array.from(allergenMap.values());
  };

  // API function to get recipe categories (only for private routes)
  const getRecipeCategoriesData = async () => {
    try {
      setCategoriesLoading(true);

      // Use conditional API call utility function
      const response = await conditionalApiCall(
        pathname,
        async () => {
          const apiResponse = await getRecipeCategoryList(
            '',
            '',
            { status: 'active' },
            '',
            { key: '', value: 'ASC' }
          );

          // Transform categories to match expected format and add "All Recipes" option
          return [
            { id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' },
            ...(apiResponse?.categories?.map((category) => ({
              id: category?.id || category?.category_name,
              label: category?.category_name || category?.label,
              icon_url: category?.iconItem?.iconUrl,
            })) || []),
          ];
        },
        // Fallback value for public routes
        [{ id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' }]
      );

      setCategories(response);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // Set default categories on error
      setCategories([
        { id: 'all', label: 'All Recipes', icon: 'ChefHat', icon_url: '' },
      ]);
    } finally {
      setCategoriesLoading(false);
    }
  };

  // API function to get recipes with filters
  const getRecipeListData = async (
    search,
    page,
    Rpp,
    apiFilters,
    showLoader = true
  ) => {
    // Prevent multiple simultaneous API calls
    if (apiCallInProgress) {
      return;
    }

    try {
      setApiCallInProgress(true);
      if (showLoader) {
        setIsLoading(true);
      }

      // Build filter object for API
      const filterParams = {
        // Categories filter
        categories: selectedCategory?.includes('all') ? [] : selectedCategory,

        // Advanced filters
        allergens: apiFilters?.allergens || [],
        dietary: apiFilters?.dietary || [],
        difficulty: apiFilters?.difficulty || '',
        status: apiFilters?.status || '',

        // Cost range filter
        cost_min: apiFilters?.costRange?.[0],
        cost_max: apiFilters?.costRange?.[1],

        // Time filters (convert minutes to seconds for API)
        cooking_time_min: (() => {
          const timeRange = getCookingTimeRange(apiFilters?.cookingTime);
          return timeRange?.min ? timeRange.min * 60 : undefined;
        })(),
        cooking_time_max: (() => {
          const timeRange = getCookingTimeRange(apiFilters?.cookingTime);
          return timeRange?.max ? timeRange.max * 60 : undefined;
        })(),

        // Ownership filter
        bookmarked: apiFilters?.ownership === 'bookmarked' ? 1 : undefined,
      };

      // Remove undefined values
      Object.keys(filterParams).forEach((key) => {
        if (
          filterParams[key] === undefined ||
          filterParams[key] === '' ||
          (Array.isArray(filterParams[key]) && filterParams[key].length === 0)
        ) {
          delete filterParams[key];
        }
      });

      // Use utility function to choose appropriate API based on route type
      const response = await getRecipeListByRouteType(
        pathname,
        search || '',
        page || 1,
        Rpp || 10,
        filterParams
      );

      setRecipes(response.recipesList);
      setTotalCount(response.totalCount);

      // For public routes, extract categories and allergens from the API response
      if (isPublic && response.recipesList) {
        // Extract and update categories from recipes
        const extractedCategories = extractCategoriesFromRecipes(
          response.recipesList
        );
        setCategories(extractedCategories);
        setCategoriesLoading(false);

        // Extract and update allergens for filter panel
        const extractedAllergens = extractAllergensFromRecipes(
          response.recipesList
        );
        setPublicAllergens(extractedAllergens);

        // For dietary info, we can extract from the same allergens data if available
        // or set empty array since public API might not have dietary info
        setPublicDietary([]);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // Set empty arrays on API error
      setRecipes([]);
      setTotalCount(0);
    } finally {
      setApiCallInProgress(false);
      if (showLoader) {
        setTimeout(() => {
          setIsLoading(false);
        }, 100);
      }
    }
  };

  // Helper function to convert cooking time filter to min/max values
  const getCookingTimeRange = (timeFilter) => {
    switch (timeFilter) {
      case 'under_30_min':
        return { min: 0, max: 29 };
      case '30_60_min':
        return { min: 30, max: 60 };
      case '1_2_hours':
        return { min: 61, max: 120 };
      case '2+_hours':
        return { min: 121, max: undefined };
      default:
        return { min: undefined, max: undefined };
    }
  };

  // useEffect to fetch categories on component mount
  useEffect(() => {
    getRecipeCategoriesData();
  }, []);

  // Single useEffect to handle all API calls and prevent duplicates
  useEffect(() => {
    // Initial load
    if (!isInitialized) {
      setIsInitialized(true);
      getRecipeListData('', 1, 10, {}, true);
      return;
    }

    // Debounce API calls for search and filter changes
    const timeoutId = setTimeout(() => {
      getRecipeListData(searchQuery, 1, 10, filters, false);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, filters, selectedCategory, isInitialized]);

  // Handle category selection with multiple selection support
  const handleCategorySelect = (categoryId) => {
    setSelectedCategory((prev) => {
      if (categoryId === 'all') {
        return ['all'];
      }

      // Remove 'all' if it exists when selecting specific categories
      const filteredPrev = prev?.filter((id) => id !== 'all') || [];

      if (filteredPrev?.includes(categoryId)) {
        // Remove category if already selected
        const newSelection = filteredPrev?.filter((id) => id !== categoryId);
        // If no categories selected, default to 'all'
        return newSelection?.length === 0 ? ['all'] : newSelection;
      } else {
        // Add category to selection
        return [...filteredPrev, categoryId];
      }
    });
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    // API call will be triggered automatically by useEffect
  };

  // Handle bookmark toggle
  const handleBookmarkToggle = async (recipeId) => {
    // Check if bookmark functionality is available for this route
    if (!routeFeatures.showBookmarkFunctionality) {
      setApiMessage(
        'info',
        'Bookmark functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await updateBookmark(recipeId);

      if (response?.status) {
        setApiMessage('success', response?.message);
        setRecipes(
          (prev) =>
            prev?.map((recipe) =>
              recipe?.id === recipeId
                ? {
                    ...recipe,
                    is_bookmarked: recipe?.is_bookmarked === 1 ? 0 : 1,
                  }
                : recipe
            ) || []
        );
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Handle recipe actions
  const handleDuplicate = async (recipe) => {
    // Check if duplicate functionality is available for this route
    if (!routeFeatures.allowDuplicate) {
      setApiMessage(
        'info',
        'Duplicate functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await duplicateRecipe(recipe?.id);

      if (response?.status) {
        setApiMessage('success', response?.message);
        // setRecipes(
        //   (prev) =>
        //     prev?.map((recipe) =>
        //       recipe?.id === recipe?.id
        //         ? {
        //             ...recipe,
        //             is_bookmarked: recipe?.is_bookmarked === 1 ? 0 : 1,
        //           }
        //         : recipe
        //     ) || []
        // );
        // getRecipeListData(searchQuery, 1, 10, filters, false);
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }

    // const duplicatedRecipe = {
    //   ...recipe,
    //   id: Date.now(), // Simple ID generation for demo
    //   recipe_title: `${recipe?.recipe_title} (Copy)`,
    //   recipe_public_title: `${recipe?.recipe_public_title} (Copy)`,
    //   updated_at: new Date().toISOString(),
    //   recipe_status: 'draft',
    //   is_bookmarked: 0,
    // };
    // setRecipes((prev) => [duplicatedRecipe, ...(prev || [])]);
  };

  const handleDelete = async (recipe) => {
    // Check if delete functionality is available for this route
    if (!routeFeatures.allowDelete) {
      setApiMessage(
        'info',
        'Delete functionality is not available on public pages'
      );
      return;
    }

    try {
      const response = await deleteRecipe(recipe?.id);

      if (response?.status) {
        setApiMessage('success', response?.message);
        setRecipes((prev) => prev?.filter((r) => r?.id !== recipe?.id) || []);
      } else {
        setApiMessage('error', response?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleShare = (recipe) => {
    // Handle share functionality for recipe
    if (recipe?.recipe_title) {
      // Implementation for sharing recipe
    }
  };

  const handleEdit = (recipe) => {
    // Check if edit functionality is available for this route
    if (!routeFeatures.allowEdit) {
      setApiMessage(
        'info',
        'Edit functionality is not available on public pages'
      );
      return;
    }

    router.push(`/recipes/recipe-update/${recipe?.recipe_slug || recipe?.id}`);
  };
  const handleViewDetails = (recipe) => {
    router.push(`/recipes/recipe-preview/${recipe?.recipe_slug || recipe?.id}`);
  };

  const handleExport = (recipe) => {
    // Handle export functionality for recipe
    if (recipe?.recipe_title) {
      // Implementation for exporting recipe
    }
  };

  return (
    <div className="recipes-listing">
      <div className="recipes-listing__header">
        <p className="sub-header-text">Recipe Dashboard</p>

        <CustomButton
          variant="contained"
          startIcon={<Icon name="Plus" size={18} />}
          onClick={() => router.push('/recipes/recipe-create')}
          title="Create Recipe"
          className="recipe-grid__empty-button"
        />
      </div>
      <Divider />
      <main
        className={getRouteBasedClassName(pathname, 'recipes-listing__main')}
      >
        <div className="recipes-listing__container">
          {/* Category Filter Chips */}
          <div className="recipes-listing__categories">
            <div className="recipes-listing__category-chips">
              {categoriesLoading ? (
                // Loading skeleton for categories
                <div className="recipes-listing__category-loading">
                  {[1, 2, 3, 4, 5].map((index) => (
                    <div
                      key={index}
                      className="recipes-listing__category-chip recipes-listing__category-chip--loading"
                    >
                      <div className="skeleton-icon"></div>
                      <div className="skeleton-text"></div>
                    </div>
                  ))}
                </div>
              ) : (
                categories?.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => handleCategorySelect(category?.id)}
                    className={`recipes-listing__category-chip ${
                      selectedCategory?.includes(category?.id)
                        ? 'recipes-listing__category-chip--active'
                        : ''
                    }`}
                  >
                    {category?.icon_url ? (
                      <Image
                        src={category?.icon_url}
                        alt={category?.category_name}
                        className="recipes-listing__category-chip-icon"
                      />
                    ) : (
                      <Icon name={category.icon} size={16} />
                    )}
                    {/* <Icon name={category.icon} size={16} /> */}
                    <span>{category?.label}</span>
                  </button>
                ))
              )}
            </div>
          </div>

          {/* Controls Bar */}
          <div className="recipes-listing__controls">
            <div className="recipes-listing__controls-left">
              {/* Search Bar */}
              <div className="recipes-listing__search-section">
                <CustomSearch
                  placeholder="Search recipes by name, ingredients, or tags..."
                  setSearchValue={setSearchQuery}
                  searchValue={searchQuery}
                  searchclass=""
                />
              </div>
            </div>

            <div className="recipes-listing__controls-right">
              {/* Results Count */}
              <span className="recipes-listing__results-count">
                {recipes?.length} of {totalCount} recipes
              </span>

              {/* Filter Toggle */}
              <button
                onClick={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
                className={`recipes-listing__filter-btn ${
                  isFilterPanelOpen ? 'recipes-listing__filter-btn--active' : ''
                }`}
              >
                <Icon name="Filter" size={16} />
                <span>Filters</span>
              </button>
            </div>
          </div>

          {/* Main Content */}
          <div className="recipes-listing__content">
            {/* Filter Panel */}
            <FilterPanel
              isOpen={isFilterPanelOpen}
              filters={filters}
              onFilterChange={handleFilterChange}
              onClose={() => setIsFilterPanelOpen(false)}
              publicAllergens={isPublicRoute ? publicAllergens : undefined}
              publicDietary={isPublicRoute ? publicDietary : undefined}
            />

            {/* Recipe Grid */}
            <div className="recipes-listing__grid-container">
              <RecipeGrid
                recipes={recipes}
                isLoading={isLoading}
                viewMode="grid"
                isPublicPage={isPublicRoute} // Pass the public route flag
                onBookmarkToggle={handleBookmarkToggle}
                onDuplicate={handleDuplicate}
                onDelete={handleDelete}
                onShare={handleShare}
                onExport={handleExport}
                onEdit={handleEdit}
                onView={handleViewDetails}
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default RecipesList;
