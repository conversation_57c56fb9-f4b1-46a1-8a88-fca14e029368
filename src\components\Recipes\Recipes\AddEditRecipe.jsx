'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import SectionNavigationSidebar from './components/SectionNavigationSidebar/SectionNavigationSidebar';
import MainFormContent from './components/MainFormContent/MainFormContent';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import AuthContext from '@/helper/authcontext';
import {
  getCurrencySymbol,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import {
  createRecipe,
  // updateRecipe
} from '@/services/recipeService';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce(
        (sum, ing) => sum + (ing.finalCost || 0),
        0
      );
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    recipeDescription: '',
    categories: [],
    dietaries: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
    complexity_level: 'low',
  },
  media: {
    mainImage: null,
    additionalImages: [],
    documents: [],
    audio: [],
    links: [],
  },
  isCookingMethod: false,
  isPreparationMethod: false,
  ingredients: [],
  instructions: [],
  nutrition: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    nutrition: [],
    commonAllergens: [],
    mayContainAllergens: [],
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
};

export default function AddEditRecipe({ isUpdate }) {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);

  const currency = getCurrencySymbol(authState?.currency_details);

  const handleRedirect = () => {
    router.back();
  };

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  const formSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: true,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: true,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: false,
    },
  ];

  const handleSectionChange = (sectionId) => {
    dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
  };

  const validateForm = () => {
    const errors = {};

    // Basic Info validation
    if (!formState.basicInfo.recipeName?.trim()) {
      errors['basic-info'] = true;
      errors.recipeName = 'Recipe name is required';
    }

    // Media validation
    if (!formState.media.mainImage) {
      errors['media'] = true;
      errors.mainImage = 'Main image is required';
    }

    // Ingredients validation
    if (formState.ingredients?.length === 0) {
      errors.ingredients = 'At least one ingredient is required';
    }

    // Instructions validation
    if (formState.instructions?.length === 0) {
      errors.instructions = 'At least one instruction step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push('/recipes/recipe-preview');
      console.error('Navigating to preview with data:', formState);
    }
  };

  // Function to prepare recipe data for API
  const prepareRecipeData = () => {
    const allMediaFiles = [
      ...formState.media.additionalImages,
      ...formState.media.documents,
      ...formState.media.audio,
    ];

    const mediaLinks = formState?.media?.links?.map((link) => {
      return {
        type: link?.type,
        item_link: link?.url,
        item_link_type: link.item_link_type,
      };
    });

    const ingredientsList = formState?.ingredients
      ?.map((ing) => {
        return {
          id: ing?.id,
          quantity: ing?.quantity,
          measure: ing?.measure_of_cost?.id,
          wastage: ing?.wastagePercentage,
          cost: ing?.cost,
          cooking_method: formState?.isCookingMethod
            ? ing?.cookingMethod
            : null,
          preparation_method: formState?.isPreparationMethod
            ? ing?.preparationMethod
            : null,
        };
      })
      ?.filter((ing) => ing?.id !== 'nutrition-container');

    const instructionsList = formState?.instructions?.map((step) => {
      return {
        order: step?.stepNumber,
        description: step?.description,
      };
    });
    const instrucationImages = formState?.instructions?.map((step) => {
      return step?.image;
    });

    const nutritionList = formState?.nutrition?.nutrition?.map((nut) => {
      return {
        id: nut?.id,
        unit: nut?.value,
        unit_of_measure: nut?.unit_of_measure,
        description: nut?.description || '',
      };
    });

    return {
      recipe_title: formState.basicInfo.recipeName,
      recipe_public_title: formState.basicInfo.publicDisplayName,
      recipe_description: formState.basicInfo.recipeDescription,
      categories:
        formState.basicInfo.categories?.map((cat) => cat.id || cat) || [],
      dietary_attributes:
        formState.basicInfo.dietaries?.map((diet) => diet.id || diet) || [],
      recipe_preparation_time: formState.basicInfo.prepTime,
      recipe_cook_time: formState.basicInfo.cookTime,
      has_recipe_public_visibility:
        formState.basicInfo.visibility.includes('public'),
      has_recipe_private_visibility:
        formState.basicInfo.visibility.includes('private'),
      recipePlaceholder: formState.media.mainImage,
      recipeFiles: allMediaFiles || [],
      resources: mediaLinks || [],
      recipe_yield: formState.serving.yield.value,
      recipe_yield_unit: formState.serving.yield.unit,
      recipe_total_portions: formState.serving.totalPortions,
      recipe_single_portion_size: formState.serving.singlePortionSize,
      recipe_serving_method: formState.serving.servingMethod,
      recipe_serve_in: formState.serving.serveIn,
      recipe_garnish: formState.serving.garnish,
      recipe_foh_tips: formState.serving.fohTips,
      recipe_head_chef_tips: formState.serving.chefTips,
      ingredients: ingredientsList || [],
      instructions: formState.instructions || [],
      is_cooking_method: formState.isCookingMethod || false,
      is_preparation_method: formState.isPreparationMethod || false,
      recipe_complexity_level: formState.basicInfo.complexity_level || 'low',
      steps: instructionsList || [],
      stepImages: instrucationImages || [],
      nutrition_attributes: nutritionList || [],
      allergen_attributes: {
        contains: formState?.nutrition?.commonAllergens || [],
        may_contain: formState?.nutrition?.mayContainAllergens || [],
      },
    };
  };

  const handleOpenSaveAndExitModal = async () => {
    if (validateForm()) {
      setShowExitModal(true);
    }
  };

  const handleCloseSaveAndExitModal = () => {
    setShowExitModal(false);
  };
  // Helper functions for FormData preparation
  const appendArrayToFormData = (formData, key, array) => {
    if (array && Array.isArray(array)) {
      array.forEach((item, index) => {
        formData.append(`${key}[${index}]`, item);
      });
    }
  };

  const appendFileToFormData = (formData, key, file) => {
    if (file instanceof File || typeof file === 'string') {
      formData.append(key, file);
    }
  };

  const appendFilesArrayToFormData = (formData, key, files) => {
    if (files && Array.isArray(files)) {
      files.forEach((file, index) => {
        appendFileToFormData(formData, `${key}[${index}]`, file);
      });
    }
  };

  // Refactored FormData preparation
  const prepareFormData = (responseData) => {
    const formData = new FormData();

    // Basic fields
    const basicFields = {
      recipe_title: responseData?.recipe_title || '',
      recipe_public_title: responseData?.recipe_public_title || '',
      recipe_description: responseData?.recipe_description || '',
      recipe_complexity_level: responseData?.recipe_complexity_level || 'low',
      recipe_preparation_time: responseData?.recipe_preparation_time || 0,
      recipe_cook_time: responseData?.recipe_cook_time || 0,
      has_recipe_public_visibility:
        responseData?.has_recipe_public_visibility || false,
      has_recipe_private_visibility:
        responseData?.has_recipe_private_visibility || false,
      recipe_yield: responseData?.recipe_yield || 0,
      recipe_yield_unit: responseData?.recipe_yield_unit || '',
      recipe_total_portions: responseData?.recipe_total_portions || 0,
      recipe_single_portion_size: responseData?.recipe_single_portion_size || 0,
      recipe_serving_method: responseData?.recipe_serving_method || '',
      recipe_serve_in: responseData?.recipe_serve_in || '',
      recipe_garnish: responseData?.recipe_garnish || '',
      recipe_foh_tips: responseData?.recipe_foh_tips || '',
      recipe_head_chef_tips: responseData?.recipe_head_chef_tips || '',
      is_cooking_method: responseData?.is_cooking_method || false,
      is_preparation_method: responseData?.is_preparation_method || false,
    };

    // Append basic fields
    Object.entries(basicFields).forEach(([key, value]) => {
      formData.append(key, value);
    });

    // Arrays
    appendArrayToFormData(formData, 'categories', data.categories);
    appendArrayToFormData(
      formData,
      'dietary_attributes',
      data.dietary_attributes
    );

    // Files
    appendFileToFormData(formData, 'recipePlaceholder', data.recipePlaceholder);
    appendFilesArrayToFormData(formData, 'recipeFiles', data.recipeFiles);
    appendFilesArrayToFormData(formData, 'stepImages', data.stepImages);

    // JSON data
    const jsonFields = {
      ingredients: data.ingredients || [],
      instructions: data.instructions || [],
      steps: data.steps || [],
      nutrition_attributes: data.nutrition_attributes || [],
      allergen_attributes: data.allergen_attributes || {},
      resources: data.resources || [],
    };

    Object.entries(jsonFields).forEach(([key, value]) => {
      formData.append(key, JSON.stringify(value));
    });

    return formData;
  };

  const recipeData = prepareRecipeData();
  console.log('recipeData', recipeData);

  const handleSaveAndExit = async () => {
    try {
      setIsSaving(true);
      const recipeData = prepareRecipeData();
      const formData = prepareFormData(recipeData); // Convert to FormData

      let response;
      if (isUpdate) {
        // Update existing recipe (you'll need to pass recipeId as prop)
        // response = await updateRecipe(recipeId, formData);
        setApiMessage('info', 'Update functionality not implemented yet');
        return;
      } else {
        // Create new recipe with FormData
        response = await createRecipe(formData);
      }
      if (response) {
        setApiMessage(
          'success',
          response?.message || 'Recipe saved successfully!'
        );
        setShowExitModal(false);
        // Navigate back to recipes list
        router.push('/recipes');
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to save recipe'
      );
    } finally {
      setIsSaving(false);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  return (
    <>
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Add'} Recipe`}
        onBackClick={handleRedirect}
      />
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator
          formState={formState}
          validationErrors={validationErrors}
        />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          <SectionNavigationSidebar
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            validationErrors={validationErrors}
            calculations={formState.calculations}
            currency={currency}
            isMobileView={isMobileView}
          />

          {/* Main Form Content */}
          <MainFormContent
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            currentSection={currentSection}
            CurrentSectionComponent={CurrentSectionComponent}
            formState={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            isMobileView={isMobileView}
            currency={currency}
            isUpdate={isUpdate}
            onPreview={handlePreview}
            onSaveAndExit={handleOpenSaveAndExitModal}
          />
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleCloseSaveAndExitModal}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={handleCloseSaveAndExitModal}
            handleConfirm={() => handleSaveAndExit()}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Continue Editing"
            confirmText="Save & Exit"
            isLoading={isSaving}
          />
        }
      />
    </>
  );
}
