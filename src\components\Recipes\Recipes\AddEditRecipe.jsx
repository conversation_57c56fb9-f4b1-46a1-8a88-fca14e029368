'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import SectionNavigationSidebar from './components/SectionNavigationSidebar/SectionNavigationSidebar';
import MainFormContent from './components/MainFormContent/MainFormContent';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import AuthContext from '@/helper/authcontext';
import {
  getCurrencySymbol,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import {
  createRecipe,
  // updateRecipe
} from '@/services/recipeService';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce(
        (sum, ing) => sum + (ing.finalCost || 0),
        0
      );
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    recipeDescription: '',
    categories: [],
    dietaries: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
    complexity_level: 'low',
  },
  media: {
    mainImage: null,
    additionalImages: [],
    documents: [],
    audio: [],
    links: [],
  },
  isCookingMethod: false,
  isPreparationMethod: false,
  ingredients: [],
  instructions: [],
  nutrition: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    nutrition: [],
    commonAllergens: [],
    mayContainAllergens: [],
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
};

export default function AddEditRecipe({ isUpdate }) {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);

  const currency = getCurrencySymbol(authState?.currency_details);

  const handleRedirect = () => {
    router.back();
  };

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  const formSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: true,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: true,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: false,
    },
  ];

  const handleSectionChange = (sectionId) => {
    dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
  };

  const validateForm = () => {
    const errors = {};

    // Basic Info validation
    if (!formState.basicInfo.recipeName?.trim()) {
      errors['basic-info'] = true;
      errors.recipeName = 'Recipe name is required';
    }

    // Media validation
    if (!formState.media.mainImage) {
      errors['media'] = true;
      errors.mainImage = 'Main image is required';
    }

    // Ingredients validation
    if (formState.ingredients?.length === 0) {
      errors.ingredients = 'At least one ingredient is required';
    }

    // Instructions validation
    if (formState.instructions?.length === 0) {
      errors.instructions = 'At least one instruction step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push('/recipes/recipe-preview');
      console.error('Navigating to preview with data:', formState);
    }
  };

  // Function to prepare recipe data for API
  const prepareRecipeData = () => {
    const allMediaFiles = [
      ...formState.media.additionalImages,
      ...formState.media.documents,
      ...formState.media.audio,
    ];

    const mediaLinks = formState?.media?.links?.map((link) => {
      return {
        type: link?.type,
        item_link: link?.url,
        item_link_type: link.item_link_type,
      };
    });

    const ingredientsList = formState?.ingredients
      ?.map((ing) => {
        return {
          id: ing?.id,
          quantity: ing?.quantity,
          measure: ing?.measure_of_cost?.id,
          wastage: ing?.wastagePercentage,
          cost: ing?.cost,
          cooking_method: formState?.isCookingMethod
            ? ing?.cookingMethod
            : null,
          preparation_method: formState?.isPreparationMethod
            ? ing?.preparationMethod
            : null,
        };
      })
      ?.filter((ing) => ing?.id !== 'nutrition-container');

    const instructionsList = formState?.instructions?.map((step) => {
      return {
        order: step?.stepNumber,
        description: step?.description,
      };
    });
    const instrucationImages = formState?.instructions?.map((step) => {
      return step?.image;
    });

    const nutritionList = formState?.nutrition?.nutrition?.map((nut) => {
      return {
        id: nut?.id,
        unit: nut?.value,
        unit_of_measure: nut?.unit_of_measure,
        description: nut?.description || '',
      };
    });

    return {
      recipe_title: formState.basicInfo.recipeName,
      recipe_public_title: formState.basicInfo.publicDisplayName,
      recipe_description: formState.basicInfo.recipeDescription,
      categories:
        formState.basicInfo.categories?.map((cat) => cat.id || cat) || [],
      dietary_attributes:
        formState.basicInfo.dietaries?.map((diet) => diet.id || diet) || [],
      recipe_preparation_time: formState.basicInfo.prepTime,
      recipe_cook_time: formState.basicInfo.cookTime,
      has_recipe_public_visibility:
        formState.basicInfo.visibility.includes('public'),
      has_recipe_private_visibility:
        formState.basicInfo.visibility.includes('private'),
      recipePlaceholder: formState.media.mainImage,
      recipeFiles: allMediaFiles || [],
      resources: mediaLinks || [],
      recipe_yield: formState.serving.yield.value,
      recipe_yield_unit: formState.serving.yield.unit,
      recipe_total_portions: formState.serving.totalPortions,
      recipe_single_portion_size: formState.serving.singlePortionSize,
      recipe_serving_method: formState.serving.servingMethod,
      recipe_serve_in: formState.serving.serveIn,
      recipe_garnish: formState.serving.garnish,
      recipe_foh_tips: formState.serving.fohTips,
      recipe_head_chef_tips: formState.serving.chefTips,
      ingredients: ingredientsList || [],
      instructions: formState.instructions || [],
      is_cooking_method: formState.isCookingMethod || false,
      is_preparation_method: formState.isPreparationMethod || false,
      recipe_complexity_level: formState.basicInfo.complexity_level || 'low',
      steps: instructionsList || [],
      stepImages: instrucationImages || [],
      nutrition_attributes: nutritionList || [],
      allergen_attributes: {
        contains: formState?.nutrition?.commonAllergens || [],
        may_contain: formState?.nutrition?.mayContainAllergens || [],
      },
    };
  };

  const handleOpenSaveAndExitModal = async () => {
    if (validateForm()) {
      setShowExitModal(true);
    }
  };

  const handleCloseSaveAndExitModal = () => {
    setShowExitModal(false);
  };
  // Function to convert recipe data to FormData
  const prepareFormData = (recipeData) => {
    const formData = new FormData();

    // Basic Information
    formData.append('recipe_title', recipeData.recipe_title || '');
    formData.append(
      'recipe_public_title',
      recipeData.recipe_public_title || ''
    );
    formData.append('recipe_description', recipeData.recipe_description || '');
    formData.append(
      'recipe_complexity_level',
      recipeData.recipe_complexity_level || 'low'
    );

    // Categories and Dietary Attributes
    if (recipeData.categories && Array.isArray(recipeData.categories)) {
      recipeData.categories.forEach((category, index) => {
        formData.append(`categories[${index}]`, category);
      });
    }

    if (
      recipeData.dietary_attributes &&
      Array.isArray(recipeData.dietary_attributes)
    ) {
      recipeData.dietary_attributes.forEach((dietary, index) => {
        formData.append(`dietary_attributes[${index}]`, dietary);
      });
    }

    // Time and Visibility
    formData.append(
      'recipe_preparation_time',
      recipeData.recipe_preparation_time || 0
    );
    formData.append('recipe_cook_time', recipeData.recipe_cook_time || 0);
    formData.append(
      'has_recipe_public_visibility',
      recipeData.has_recipe_public_visibility || false
    );
    formData.append(
      'has_recipe_private_visibility',
      recipeData.has_recipe_private_visibility || false
    );

    // Media Files
    // Main Image
    if (recipeData.recipePlaceholder) {
      if (recipeData.recipePlaceholder instanceof File) {
        formData.append('recipePlaceholder', recipeData.recipePlaceholder);
      } else if (typeof recipeData.recipePlaceholder === 'string') {
        formData.append('recipePlaceholder', recipeData.recipePlaceholder);
      }
    }

    // Recipe Files (images, documents, audio)
    if (recipeData.recipeFiles && Array.isArray(recipeData.recipeFiles)) {
      recipeData.recipeFiles.forEach((file, index) => {
        if (file instanceof File) {
          formData.append(`recipeFiles[${index}]`, file);
        } else if (typeof file === 'string') {
          formData.append(`recipeFiles[${index}]`, file);
        }
      });
    }

    // Step Images
    if (recipeData.stepImages && Array.isArray(recipeData.stepImages)) {
      recipeData.stepImages.forEach((image, index) => {
        if (image instanceof File) {
          formData.append(`stepImages[${index}]`, image);
        } else if (typeof image === 'string') {
          formData.append(`stepImages[${index}]`, image);
        }
      });
    }

    // Resources (links)
    if (recipeData.resources && Array.isArray(recipeData.resources)) {
      formData.append('resources', JSON.stringify(recipeData.resources));
    }

    // Serving Information
    formData.append('recipe_yield', recipeData.recipe_yield || 0);
    formData.append('recipe_yield_unit', recipeData.recipe_yield_unit || '');
    formData.append(
      'recipe_total_portions',
      recipeData.recipe_total_portions || 0
    );
    formData.append(
      'recipe_single_portion_size',
      recipeData.recipe_single_portion_size || 0
    );
    formData.append(
      'recipe_serving_method',
      recipeData.recipe_serving_method || ''
    );
    formData.append('recipe_serve_in', recipeData.recipe_serve_in || '');
    formData.append('recipe_garnish', recipeData.recipe_garnish || '');
    formData.append('recipe_foh_tips', recipeData.recipe_foh_tips || '');
    formData.append(
      'recipe_head_chef_tips',
      recipeData.recipe_head_chef_tips || ''
    );

    // Complex Data as JSON strings
    formData.append(
      'ingredients',
      JSON.stringify(recipeData.ingredients || [])
    );
    formData.append(
      'instructions',
      JSON.stringify(recipeData.instructions || [])
    );
    formData.append('steps', JSON.stringify(recipeData.steps || []));
    formData.append(
      'nutrition_attributes',
      JSON.stringify(recipeData.nutrition_attributes || [])
    );
    formData.append(
      'allergen_attributes',
      JSON.stringify(recipeData.allergen_attributes || {})
    );

    // Method flags
    formData.append('is_cooking_method', recipeData.is_cooking_method || false);
    formData.append(
      'is_preparation_method',
      recipeData.is_preparation_method || false
    );

    return formData;
  };

  const handleSaveAndExit = async () => {
    try {
      setIsSaving(true);
      const recipeData = prepareRecipeData();
      const formData = prepareFormData(recipeData); // Convert to FormData

      let response;
      if (isUpdate) {
        // Update existing recipe (you'll need to pass recipeId as prop)
        // response = await updateRecipe(recipeId, formData);
        setApiMessage('info', 'Update functionality not implemented yet');
        return;
      } else {
        // Create new recipe with FormData
        response = await createRecipe(formData);
      }
      if (response) {
        setApiMessage(
          'success',
          response?.message || 'Recipe saved successfully!'
        );
        setShowExitModal(false);
        // Navigate back to recipes list
        router.push('/recipes');
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to save recipe'
      );
    } finally {
      setIsSaving(false);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  return (
    <>
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Add'} Recipe`}
        onBackClick={handleRedirect}
      />
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator
          formState={formState}
          validationErrors={validationErrors}
        />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          <SectionNavigationSidebar
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            validationErrors={validationErrors}
            calculations={formState.calculations}
            currency={currency}
            isMobileView={isMobileView}
          />

          {/* Main Form Content */}
          <MainFormContent
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            currentSection={currentSection}
            CurrentSectionComponent={CurrentSectionComponent}
            formState={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            isMobileView={isMobileView}
            currency={currency}
            isUpdate={isUpdate}
            onPreview={handlePreview}
            onSaveAndExit={handleOpenSaveAndExitModal}
          />
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleCloseSaveAndExitModal}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={handleCloseSaveAndExitModal}
            handleConfirm={() => handleSaveAndExit()}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Continue Editing"
            confirmText="Save & Exit"
            isLoading={isSaving}
          />
        }
      />
    </>
  );
}
