'use client';

import React, { useContext, useEffect, useReducer, useState } from 'react';
import { Box } from '@mui/material';
import { useRouter } from 'next/navigation';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import FormProgressIndicator from './components/FormProgressIndicator/FormProgressIndicator';
import BasicInfoSection from './components/BasicInfoSection/BasicInfoSection';
import MediaUploadSection from './components/MediaUploadSection/MediaUploadSection';
import IngredientsSection from './components/IngredientsSection/IngredientsSection';
import InstructionsSection from './components/InstructionsSection/InstructionsSection';
import NutritionSection from './components/NutritionSection/NutritionSection';
import ServingDetailsSection from './components/ServingDetailsSection/ServingDetailsSection';
import SectionNavigationSidebar from './components/SectionNavigationSidebar/SectionNavigationSidebar';
import MainFormContent from './components/MainFormContent/MainFormContent';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import AuthContext from '@/helper/authcontext';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import './recipes.scss';

// Recipe form reducer
const recipeFormReducer = (state, action) => {
  switch (action.type) {
    case 'UPDATE_BASIC_INFO':
      return { ...state, basicInfo: { ...state.basicInfo, ...action.payload } };
    case 'UPDATE_MEDIA':
      return { ...state, media: { ...state.media, ...action.payload } };
    case 'UPDATE_INGREDIENTS':
      return { ...state, ingredients: action.payload };
    case 'UPDATE_INSTRUCTIONS':
      return { ...state, instructions: action.payload };
    case 'UPDATE_NUTRITION':
      return { ...state, nutrition: { ...state.nutrition, ...action.payload } };
    case 'UPDATE_SERVING':
      return { ...state, serving: { ...state.serving, ...action.payload } };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'CALCULATE_TOTALS':
      const totalCost = state.ingredients.reduce(
        (sum, ing) => sum + (ing.finalCost || 0),
        0
      );
      const totalTime =
        (state.basicInfo.prepTime || 0) + (state.basicInfo.cookTime || 0);
      const portionCost =
        state.serving.totalPortions > 0
          ? totalCost / state.serving.totalPortions
          : 0;
      return {
        ...state,
        calculations: {
          totalCost,
          totalTime,
          portionCost,
        },
      };
    default:
      return state;
  }
};

const initialState = {
  activeSection: 'basic-info',
  basicInfo: {
    recipeName: '',
    publicDisplayName: '',
    recipeDescription: '',
    categories: [],
    dietaries: [],
    prepTime: 0,
    cookTime: 0,
    visibility: ['private'],
  },
  media: {
    mainImage: null,
    additionalImages: [],
    documents: [],
    audio: [],
    links: [],
  },
  isCookingMethod: false,
  isPreparationMethod: false,
  ingredients: [],
  instructions: [],
  nutrition: {
    calories: 0,
    protein: 0,
    carbs: 0,
    fat: 0,
    fiber: 0,
    sugar: 0,
    commonAllergens: [],
    mayContainAllergens: [],
  },
  serving: {
    yield: { value: 0, unit: 'servings' },
    totalPortions: 0,
    singlePortionSize: 0,
    servingMethod: '',
    serveIn: '',
    garnish: '',
    fohTips: '',
    chefTips: '',
  },
  calculations: {
    totalCost: 0,
    totalTime: 0,
    portionCost: 0,
  },
};

export default function AddEditRecipe({ isUpdate }) {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  const [formState, dispatch] = useReducer(recipeFormReducer, initialState);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showExitModal, setShowExitModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  const currency = getCurrencySymbol(authState?.currency_details);

  const handleRedirect = () => {
    router.back();
  };

  // Check for mobile view
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);
    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Auto-calculate totals when dependencies change
  useEffect(() => {
    dispatch({ type: 'CALCULATE_TOTALS' });
  }, [
    formState.ingredients,
    formState.basicInfo.prepTime,
    formState.basicInfo.cookTime,
    formState.serving.totalPortions,
  ]);

  const formSections = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      icon: 'FileText',
      component: BasicInfoSection,
      required: true,
    },
    {
      id: 'media',
      title: 'Media & Photos',
      icon: 'Camera',
      component: MediaUploadSection,
      required: true,
    },
    {
      id: 'ingredients',
      title: 'Ingredients',
      icon: 'ShoppingCart',
      component: IngredientsSection,
      required: true,
    },
    {
      id: 'instructions',
      title: 'Instructions',
      icon: 'List',
      component: InstructionsSection,
      required: true,
    },
    {
      id: 'nutrition',
      title: 'Nutrition & Allergens',
      icon: 'Heart',
      component: NutritionSection,
      required: false,
    },
    {
      id: 'serving',
      title: 'Serving Details',
      icon: 'Users',
      component: ServingDetailsSection,
      required: false,
    },
  ];

  const handleSectionChange = (sectionId) => {
    dispatch({ type: 'SET_ACTIVE_SECTION', payload: sectionId });
  };

  const handleSaveAndExit = () => {
    // Simulate save operation
    console.error('Saving recipe...', formState);
    setShowExitModal(false);
  };

  const validateForm = () => {
    const errors = {};

    // Basic Info validation
    if (!formState.basicInfo.recipeName?.trim()) {
      errors['basic-info'] = true;
      errors.recipeName = 'Recipe name is required';
    }

    // Media validation
    if (!formState.media.mainImage) {
      errors.mainImage = 'Main image is required';
    }

    // Ingredients validation
    if (formState.ingredients?.length === 0) {
      errors.ingredients = 'At least one ingredient is required';
    }

    // Instructions validation
    if (formState.instructions?.length === 0) {
      errors.instructions = 'At least one instruction step is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePreview = () => {
    if (validateForm()) {
      // Navigate to preview with form data
      // router.push({
      //   pathname: '/recipes/recipe-preview',
      //   query: { previewDetails: JSON.stringify(formState) },
      // });
      router.push('/recipes/recipe-preview');
      console.error('Navigating to preview with data:', formState);
    }
  };

  const currentSection = formSections.find(
    (section) => section.id === formState.activeSection
  );
  const CurrentSectionComponent = currentSection?.component;

  // const sendData = {
  //   recipe_title: formState.basicInfo.recipeName,
  //   recipe_public_title: formState.basicInfo.publicDisplayName,
  //   recipe_description: formState.basicInfo.recipeDescription,
  //   categories: formState.basicInfo.categories,
  //   dietary_attributes: formState.basicInfo.dietaries,
  //   recipe_preparation_time: formState.basicInfo.prepTime,
  //   recipe_cook_time: formState.basicInfo.cookTime,
  //   has_recipe_public_visibility:
  //     formState.basicInfo.visibility.includes('public'),
  //   has_recipe_private_visibility:
  //     formState.basicInfo.visibility.includes('private'),
  //   recipePlaceholder: formState.media.mainImage,
  //   recipeFiles: formState.media.images,
  //   recipe_yield: formState.serving.yield.value,
  //   recipe_yield_unit: formState.serving.yield.unit,
  //   recipe_total_portions: formState.serving.totalPortions,
  //   recipe_single_portion_size: formState.serving.singlePortionSize,
  //   recipe_serving_method: formState.serving.servingMethod,
  //   recipe_serve_in: formState.serving.serveIn,
  //   recipe_garnish: formState.serving.garnish,
  //   recipe_foh_tips: formState.serving.fohTips,
  //   recipe_head_chef_tips: formState.serving.chefTips,
  // };
  // console.log('formStateformState', formState, sendData);

  return (
    <>
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Add'} Recipe`}
        onBackClick={handleRedirect}
      />
      {/* Form Progress Indicator */}
      <Box className="section-right-content add-edit-recipe-container">
        <FormProgressIndicator
          formState={formState}
          validationErrors={validationErrors}
        />

        {/* Main Content */}
        <div className="add-edit-recipe__layout">
          {/* Desktop: Section Navigation Sidebar */}
          <SectionNavigationSidebar
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            validationErrors={validationErrors}
            calculations={formState.calculations}
            currency={currency}
            isMobileView={isMobileView}
          />

          {/* Main Form Content */}
          <MainFormContent
            formSections={formSections}
            activeSection={formState.activeSection}
            onSectionChange={handleSectionChange}
            currentSection={currentSection}
            CurrentSectionComponent={CurrentSectionComponent}
            formState={formState}
            dispatch={dispatch}
            validationErrors={validationErrors}
            isMobileView={isMobileView}
            currency={currency}
            isUpdate={isUpdate}
            onPreview={handlePreview}
            onSaveAndExit={() => setShowExitModal(true)}
          />
        </div>
      </Box>
      <DialogBox
        open={showExitModal}
        handleClose={handleSaveAndExit}
        title="Confirmation"
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <ConfirmationModal
            handleCancel={handleSaveAndExit}
            handleConfirm={handleSaveAndExit}
            text="Do you want to save your recipe before exiting? Your progress will be preserved."
            cancelText="Save & Exit"
            confirmText="Continue Editing"
          />
        }
      />
    </>
  );
}
