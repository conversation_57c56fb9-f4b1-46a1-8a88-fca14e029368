'use client';

import React, { useEffect, useState, useRef } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { staticOptions } from '@/helper/common/staticOptions';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { identifiers } from '@/helper/constants/identifier';
import CancelIcon from '@mui/icons-material/Cancel';
import {
  Dragdropicon,
  Numver123Icon,
  StringABCIcon,
} from '@/helper/common/images';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';

export default function SubCategory({
  AddDSR,
  AddSubPayment,
  ReorderSubPayment,
  deletesubCategory,
}) {
  const formikRef = useRef(null);
  const [categoryData, setCatData] = useState([
    {
      name: '',
      type: '',
      limit: '',
    },
  ]);
  const [categoryList, setCatList] = useState([]);
  const [random, setRandom] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleConfirmDelete = () => {
    deletesubCategory(deleteId);
    const categories = categoryList?.filter((f) => f?.id !== deleteId);
    setCatList(categories);
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = categoryList;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setCatList(newData);
    const CuurectCat = categoryList[destination?.index];
    ReorderSubPayment(destination?.index, CuurectCat?.id);
  };
  useEffect(() => {
    setCatData(categoryData);
  }, [random]);
  useEffect(() => {
    if (
      AddDSR?.list?.payment_type_category_field &&
      AddDSR?.list?.payment_type_category_field?.length > 0
    ) {
      const catetype = AddDSR?.list?.payment_type_category_field?.map(
        (item) => {
          return {
            id: item?.id,
            name: item?.field_name,
            type: item?.field_type === 'integer' ? 'number' : item?.field_type,
            limit: item?.field_limit,
          };
        }
      );
      setCatData(catetype);
    } else if (
      AddDSR?.list?.payment_type_category_field &&
      AddDSR?.list?.payment_type_category_field?.length === 0
    ) {
      setCatData([
        {
          name: '',
          type: '',
          limit: '',
        },
      ]);
    } else {
      setCatData([
        {
          name: '',
          type: '',
          limit: '',
        },
      ]);
    }
  }, [AddDSR?.list?.payment_type_category_field]);

  useEffect(() => {
    if (AddDSR && AddDSR?.CategoryList && AddDSR?.CategoryList?.length > 0) {
      setCatList(AddDSR?.CategoryList);
    } else {
      setCatList([]);
    }
  }, [AddDSR?.CategoryList]);
  useEffect(() => {
    if (formikRef && formikRef?.current && !AddDSR?.isUpdate) {
      formikRef.current.resetForm();
    }
  }, [AddDSR?.id]);
  return (
    <Box className="category-level-1-section">
      {AddDSR?.reorder ? (
        <Box>
          {' '}
          <Typography className="body-text fw600 text-underline color-dark-blue">
            {`Manage ${AddDSR?.payment_type_title} Items`}
          </Typography>
          <Box className="category-list-details">
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="droppable">
                {(provided) => (
                  <Box {...provided.droppableProps} ref={provided.innerRef}>
                    {categoryList?.map((item, index) => (
                      <Draggable
                        key={item?.id}
                        draggableId={item?.id.toString()}
                        index={index}
                      >
                        {(provided) => (
                          <Box
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            hover
                            //   role="checkbox"
                            className="main-level-cards-screen"
                            tabIndex={-1}
                            key={item?.id}
                          >
                            <Box className="card-details cursor-pointer">
                              <Box className="card-left">
                                <Box>
                                  <Box className="d-flex align-center">
                                    <Typography className="title-sm fw400 d-flex align-center">
                                      <span className="title-sm text-capital">
                                        {item?.payment_type_title}
                                      </span>
                                    </Typography>
                                  </Box>
                                  <Box className="d-flex align-start">
                                    {item?.payment_type_category_pattern ===
                                    'multiple' ? (
                                      <span className="title-text cards-fileds">
                                        {item?.payment_type_category_field &&
                                        item?.payment_type_category_field
                                          ?.length > 0
                                          ? item?.payment_type_category_field?.map(
                                              (f, index) => {
                                                return (
                                                  <>
                                                    <span>{f?.field_name}</span>
                                                    <span className="title-text d-flex align-start">
                                                      &nbsp;
                                                      {' ( '}
                                                      {f?.field_type ===
                                                      'string' ? (
                                                        <Tooltip
                                                          arrow
                                                          classes={{
                                                            tooltip:
                                                              'info-tooltip-container',
                                                          }}
                                                          title={
                                                            <Typography>
                                                              String
                                                            </Typography>
                                                          }
                                                        >
                                                          <Box className="d-flex">
                                                            <StringABCIcon />
                                                          </Box>
                                                        </Tooltip>
                                                      ) : (
                                                        <Tooltip
                                                          arrow
                                                          classes={{
                                                            tooltip:
                                                              'info-tooltip-container',
                                                          }}
                                                          title={
                                                            <Typography>
                                                              Number
                                                            </Typography>
                                                          }
                                                        >
                                                          <Box className="d-flex">
                                                            <Numver123Icon />
                                                          </Box>
                                                        </Tooltip>
                                                      )}
                                                      {' )'}
                                                    </span>
                                                    {index !==
                                                      item
                                                        ?.payment_type_category_field
                                                        ?.length -
                                                        1 && (
                                                      <span>
                                                        {' '}
                                                        &nbsp;, &nbsp;{' '}
                                                      </span>
                                                    )}
                                                  </>
                                                );
                                              }
                                            )
                                          : ''}
                                      </span>
                                    ) : (
                                      <Typography className="title-text">
                                        {item?.payment_type_category_data_type ===
                                        'string' ? (
                                          <Tooltip
                                            title={
                                              <Typography>String</Typography>
                                            }
                                            classes={{
                                              tooltip: 'info-tooltip-container',
                                            }}
                                          >
                                            <Box className="d-flex">
                                              <StringABCIcon />
                                            </Box>
                                          </Tooltip>
                                        ) : (
                                          <Tooltip
                                            arrow
                                            classes={{
                                              tooltip: 'info-tooltip-container',
                                            }}
                                            title={
                                              <Typography>Number</Typography>
                                            }
                                          >
                                            <Box className="d-flex">
                                              <Numver123Icon />
                                            </Box>
                                          </Tooltip>
                                        )}
                                      </Typography>
                                    )}
                                  </Box>
                                </Box>
                              </Box>
                              <Box className="card-right">
                                {item?.payment_type_status === 'active' ? (
                                  <Typography className="sub-title-text success text-capital fw600">
                                    {item?.payment_type_status}
                                  </Typography>
                                ) : item?.payment_type_status === 'inactive' ? (
                                  <Typography className="sub-title-text failed text-capital fw600">
                                    {' '}
                                    {'In-Active'}
                                  </Typography>
                                ) : (
                                  <Typography className="sub-title-text draft text-capital fw600">
                                    {' '}
                                    {item?.payment_type_status}
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                            <Box>
                              <Box
                                {...provided.dragHandleProps}
                                className="drag-icon"
                              >
                                {Dragdropicon()}
                              </Box>
                            </Box>
                            <CancelIcon
                              className="close-icon"
                              onClick={() => {
                                handleOpenDeleteDialog(item?.id);
                              }}
                            />
                          </Box>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </Box>
                )}
              </Droppable>
            </DragDropContext>
          </Box>
        </Box>
      ) : (
        <Box className="">
          <Typography className="body-text fw600 text-underline color-dark-blue">
            {AddDSR?.isUpdate
              ? `Update ${AddDSR?.list?.payment_type_title}`
              : `Add items into ${AddDSR?.payment_type_title}`}
          </Typography>
          <Formik
            innerRef={formikRef}
            initialValues={{
              fieldLabel: AddDSR?.list?.payment_type_title || '',
              fieldtype: AddDSR?.list?.payment_type_category_pattern || '',
              fieldstatus: AddDSR?.list?.payment_type_status || 'active',
              fieldRemark: AddDSR?.list?.payment_type_category_remarks || '',
              fieldArray:
                AddDSR?.isUpdate &&
                AddDSR?.list?.payment_type_category_field &&
                categoryData &&
                categoryData?.length > 0
                  ? categoryData
                  : [
                      {
                        name: '',
                        type: '',
                        limit: '',
                      },
                    ],
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              fieldLabel: Yup.string()
                .trim()
                .required('This field is required'),
              fieldtype: Yup.string().trim().required('This field is required'),
              fieldstatus: Yup.string()
                .trim()
                .required('This field is required'),
              fieldArray: Yup.lazy((value, context) => {
                return context.parent.fieldtype === 'multiple'
                  ? Yup.array()
                      .of(
                        Yup.object().shape({
                          name: Yup.string().required('This field is required'),
                          type: Yup.string().required('This field is required'),
                          limit: Yup.lazy(
                            (value) =>
                              value === null || value === ''
                                ? Yup.number().nullable() // If empty, allow it to be null
                                : Yup.number().typeError('Must be a number') // Otherwise enforce number type
                          ),
                        })
                      )
                      .min(1, 'At least one item is required')
                  : Yup.array().nullable();
              }),
            })}
            onSubmit={async (requestData) => {
              AddSubPayment(requestData, AddDSR?.isUpdate, AddDSR?.id);
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              setFieldValue,
              dirty,
              isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="pt16">
                  <CustomTextField
                    fullWidth
                    id="fieldLabel"
                    name="fieldLabel"
                    label="Field label"
                    placeholder="Enter Field label"
                    value={values?.fieldLabel}
                    error={Boolean(touched.fieldLabel && errors.fieldLabel)}
                    helperText={touched.fieldLabel && errors.fieldLabel}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    required
                  />
                </Box>
                <Box className="pt16">
                  <CustomSelect
                    name="fieldtype"
                    label="Field type"
                    placeholder="Field type"
                    options={staticOptions?.DSR_SUB_CATEGORY_TYPE}
                    value={
                      staticOptions?.DSR_SUB_CATEGORY_TYPE?.find((opt) => {
                        return opt?.value === values?.fieldtype;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFieldValue('fieldtype', e?.value);
                    }}
                    error={Boolean(touched.fieldtype && errors.fieldtype)}
                    helperText={touched.fieldtype && errors.fieldtype}
                    required
                    isDisabled={AddDSR?.isUpdate}
                    isClearable={false}
                  />
                </Box>
                {values?.fieldtype && (
                  <Box>
                    {values?.fieldtype === 'multiple' ? (
                      <>
                        <Box className="d-flex justify-end align-center mt32">
                          <CustomButton
                            title="Add Field"
                            startIcon={<AddIcon />}
                            onClick={() => {
                              let list = categoryData;
                              list.push({
                                name: '',
                                type: '',
                                limit: '',
                                status: 'active',
                              });
                              setCatData(list);
                              setFieldValue('fieldArray', list);
                              setRandom(Math.random());
                            }}
                          />
                        </Box>
                        {values?.fieldArray &&
                          values?.fieldArray?.length > 0 &&
                          values?.fieldArray?.map((item, index) => {
                            return (
                              <Box className="dsr-settings-category-data-grid">
                                <Box className="">
                                  <CustomTextField
                                    fullWidth
                                    id="name"
                                    name="name"
                                    label="Field label"
                                    placeholder="Enter Field label"
                                    value={item?.name}
                                    error={Boolean(
                                      touched.fieldArray?.[index]?.name &&
                                        errors.fieldArray?.[index]?.name
                                    )}
                                    helperText={
                                      touched.fieldArray?.[index]?.name &&
                                      errors.fieldArray?.[index]?.name
                                    }
                                    onChange={(e) => {
                                      let list = categoryData;
                                      list[index].name = e.target.value;
                                      setCatData(list);
                                      setFieldValue('fieldArray', list);
                                    }}
                                    required
                                  />
                                </Box>
                                <Box className="">
                                  <CustomSelect
                                    name="catType"
                                    placeholder="Field Value Datatype"
                                    options={staticOptions?.DSR_VALUE_TYPE}
                                    // value={item?.type}
                                    value={
                                      staticOptions?.DSR_VALUE_TYPE?.find(
                                        (opt) => {
                                          return opt?.value === item?.type;
                                        }
                                      ) || ''
                                    }
                                    onChange={(e) => {
                                      let list = categoryData;
                                      list[index].type = e.value;
                                      setCatData(list);
                                      setFieldValue('fieldArray', list);
                                    }}
                                    error={Boolean(
                                      touched.fieldArray?.[index]?.type &&
                                        errors.fieldArray?.[index]?.type
                                    )}
                                    helperText={
                                      touched.fieldArray?.[index]?.type &&
                                      errors.fieldArray?.[index]?.type
                                    }
                                    label="Field Value Datatype"
                                    required
                                    isClearable={false}
                                  />
                                </Box>
                                <Box className="d-flex align-center gap-sm">
                                  <CustomTextField
                                    fullWidth
                                    id="limit"
                                    name="limit"
                                    label="Field Value Limit"
                                    placeholder="Enter Field Value Limit"
                                    value={item?.limit}
                                    error={Boolean(
                                      touched.fieldArray?.[index]?.limit &&
                                        errors.fieldArray?.[index]?.limit
                                    )}
                                    helperText={
                                      touched.fieldArray?.[index]?.limit &&
                                      errors.fieldArray?.[index]?.limit
                                    }
                                    onChange={(e) => {
                                      let list = categoryData;
                                      list[index].limit =
                                        e.target.value === ''
                                          ? e.target.value
                                          : parseFloat(e.target.value);
                                      setCatData(list);
                                      setFieldValue('fieldArray', list);
                                    }}
                                    onInput={(e) => {
                                      e.target.value = e.target.value.replace(
                                        /[^0-9]/g,
                                        ''
                                      );
                                    }}
                                  />
                                  <Box className="d-flex align-center h100 mt16">
                                    {(index !== 0 ||
                                      categoryData?.length > 1) && (
                                      <CancelIcon
                                        className="cursor-pointer"
                                        onClick={() => {
                                          if (
                                            index !== 0 ||
                                            categoryData?.length > 1
                                          ) {
                                            let list = categoryData?.filter(
                                              (i, f) => f !== index
                                            );
                                            setCatData(list);
                                            setFieldValue('fieldArray', list);
                                          }
                                        }}
                                      />
                                    )}
                                  </Box>
                                </Box>
                              </Box>
                            );
                          })}
                      </>
                    ) : (
                      <></>
                    )}
                  </Box>
                )}

                <Box className="pt16">
                  <CustomSelect
                    name="fieldstatus"
                    label="Field status"
                    placeholder="Field status"
                    options={identifiers?.CARD_STATUS}
                    value={
                      staticOptions?.CARD_STATUS?.find((opt) => {
                        return opt?.value === values?.fieldstatus;
                      }) || ''
                    }
                    onChange={(e) => {
                      setFieldValue('fieldstatus', e?.value);
                    }}
                    error={Boolean(touched.fieldstatus && errors.fieldstatus)}
                    helperText={touched.fieldstatus && errors.fieldstatus}
                    required
                    isClearable={false}
                  />
                </Box>
                <Box className="pt16">
                  <CustomTextField
                    fullWidth
                    id="fieldRemark"
                    name="fieldRemark"
                    label="Field remark"
                    placeholder="Enter Field remark"
                    className="additional-textfeild"
                    value={values?.fieldRemark}
                    multiline
                    rows={3}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>

                <CustomButton
                  className="mt16"
                  title={AddDSR?.isUpdate ? 'Update category' : 'Add category'}
                  type="submit"
                  disabled={!dirty || !isValid}
                />
              </Form>
            )}
          </Formik>
        </Box>
      )}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        className="delete-modal"
        dividerClass="delete-modal-divider"
        title="Confirmation"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete?"
          />
        }
      />
    </Box>
  );
}
