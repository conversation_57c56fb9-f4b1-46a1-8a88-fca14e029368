'use client';
import { Box, Typography } from '@mui/material';
import React from 'react';
import CustomButton from '@/components/UI/button';
import { NotFoundIcon } from '@/helper/common/images';
import './notfound.scss';
import { fetchFromStorage } from '@/helper/context';
import { usePathname, useRouter } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';

export default function NotFound() {
  const router = useRouter();
  const authData =
    typeof window !== 'undefined' && fetchFromStorage(identifiers?.AUTH_DATA);
  const pathName = usePathname();

  const handleRedirect = () => {
    // If user has organization access
    if (authData?.organizationStatus && authData?.organizationId) {
      router.push('/org/organization');
      return;
    }

    // If user is in organization section
    if (pathName.includes('/org')) {
      router.push('/org/login');
      return;
    }

    // If user is in non-organization section
    if (!pathName.includes('/org') && pathName.includes('/login')) {
      router.push('/login');
      return;
    }
    if (
      !authData?.organizationStatus &&
      !authData?.organizationId &&
      !pathName.includes('/org')
    ) {
      router.push('/myprofile');
      return;
    }
  };

  return (
    <Box className="not-found-page-sec">
      <Box className="not-found-container">
        <Box className="not-found-page">
          <Box className="not-found-icon-wrap">
            <NotFoundIcon className="not-found-icon" />
          </Box>
          <Typography className="heading-text text-align">
            The page you are looking for can't be found
          </Typography>
          <Typography className="not-found-text">
            You may have mistyped the address or the page may have moved.
          </Typography>
          <Box className="log-in-btn-wrap" textAlign="center">
            <CustomButton
              fullWidth
              className="log-in-btn"
              type="submit"
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title={
                authData?.organizationStatus && authData?.organizationId
                  ? 'Back to Organization'
                  : pathName.includes('/org')
                    ? 'Back to Organization Login'
                    : !pathName.includes('/org') && pathName.includes('/login')
                      ? 'Back to Login'
                      : 'Back to My Profile'
              }
              onClick={handleRedirect}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
