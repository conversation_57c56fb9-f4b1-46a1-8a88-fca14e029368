@import '@/styles/variable.scss';

.history-wrap {
    .filter-wrap {
        padding: 5px 50px;
        border-bottom: 1px solid #ededed;

        .ticket-history {
            font-size: 15px;
            color: $color-Dark-30;
        }

        .filter-select {
            .MuiInputBase-root {
                font-size: 14px;
            }

            .MuiSelect-select {
                padding: 0px 0px 0px 5px;
                width: 22px;
                text-overflow: clip;
            }

            fieldset {
                width: 50px;
                border: none;
            }

            .MuiSvgIcon-root {
                display: none;
            }

            .down-arrow-wrap {
                height: 18px;
                width: 18px;
            }
        }

        .filter-by-wrap {
            font-size: 15px;
            color: $color-Dark-30;
        }

        .filter-by-select {

            .select__control {
                border: none;
                padding: 0px;

                .select__value-container {
                    padding: 0px;

                    .select__single-value {
                        font-size: 15px;
                        margin-left: 10px;
                        margin-top: 2px;
                    }

                    .select__input-container {
                        margin: 0px 0px 0px 14px;
                    }

                    .select__placeholder {
                        padding: 2px 0px 0px 14px;
                        font-family: Inter, sans-serif;
                    }
                }

                .select__indicators {
                    .select__indicator {
                        svg {
                            color: $color-Dark-90;
                        }
                    }
                }
            }
        }
    }
}