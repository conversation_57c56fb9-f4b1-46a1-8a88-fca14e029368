'use client';
import React, { useState } from 'react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { Box, Checkbox, FormControlLabel, Typography } from '@mui/material';
import CustomButton from '@/components/UI/button';
import { CustomTextField } from '@/components/UI/CommonField';
import CustomEditor from '@/components/UI/CustomEditor';
import CustomSelect from '@/components/UI/selectbox';
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import { useDropzone } from 'react-dropzone';
import PreviewModal from './PreviewModal';
import CloseIcon from '@mui/icons-material/Close';
import Image from 'next/image';
import './createticket.scss';

export default function CreateTicket() {
  const [previewMedia, setPreviewMedia] = useState(null);
  const [mediaFiles, setMediaFiles] = useState([]);
  const [openModal, setOpenModal] = useState(false);

  const categoryOptions = [
    { label: 'HRMS', value: 'hrms' },
    { label: 'PMS', value: 'pms' },
    { label: 'Other', value: 'other' },
  ];

  const issueTypeOptions = [
    { label: 'Bug', value: 'bug' },
    { label: 'Feature Request', value: 'feature_request' },
    { label: 'General Query', value: 'general_query' },
  ];

  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length) {
        const newMedia = acceptedFiles.map((file) => ({
          file,
          preview: URL.createObjectURL(file),
          type: file.type.split('/')[0],
        }));
        setMediaFiles((prevFiles) => [...prevFiles, ...newMedia]);
      }
    },
    accept: 'image/*,video/*',
    multiple: true,
  });

  const handlePreviewClick = (preview) => {
    setPreviewMedia(preview);
    setOpenModal(true);
  };
  const removeMedia = (index) => {
    setMediaFiles((prevFiles) => prevFiles?.filter((file, i) => i !== index));
  };

  const handleSubmitForm = (values) => {
    // Generate unique ID based on current timestamp
    const uniqueId = `ticket-${Date.now()}`;

    // Get current date and time
    const createdAt = new Date().toLocaleString();

    // Add unique ID and created date/time to values
    const ticketData = {
      ...values,
      uniqueId,
      createdAt,
    };

    // Simulate submitting the form (e.g., sending ticket data to API)
    console.log('Form submitted with:', ticketData);
  };

  return (
    <Formik
      initialValues={{
        Subject: '',
        category: '',
        issueType: '',
        description: '',
        Name: '',
        Email: '',
        PhoneNumber: '',
        term_condition: false,
      }}
      validationSchema={Yup.object({
        Subject: Yup.string().required('Subject is required'),
        category: Yup.string().required('Category is required'),
        issueType: Yup.string().required('Issue Type is required'),
        description: Yup.string().required('Description is required'),
        Name: Yup.string().required('Name is required'),
        Email: Yup.string()
          .email('Invalid email')
          .required('Email is required'),
        PhoneNumber: Yup.string().required('Phone Number is required'),
        term_condition: Yup.bool().oneOf([true], 'You must agree to the terms'),
      })}
      onSubmit={(values) => handleSubmitForm(values)}
    >
      {({
        values,
        errors,
        touched,
        handleBlur,
        handleChange,
        setFieldValue,
        handleSubmit,
      }) => (
        <Form onSubmit={handleSubmit}>
          <Box className="support-ticket-wrap">
            <Typography className="new-ticket-wrap" variant="h5">
              New Ticket
            </Typography>
            <Box className="pt32">
              <CustomTextField
                InputLabelProps={{ shrink: true }}
                id="Subject"
                name="Subject"
                label="Subject*"
                placeholder="Enter subject"
                value={values.Subject}
                error={Boolean(touched?.Subject && errors?.Subject)}
                helperText={touched?.Subject && errors?.Subject}
                className="w100"
                variant="filled"
                onBlur={handleBlur}
                onChange={handleChange}
              />
            </Box>

            <Box className="select-field-wrap">
              <Box className="pt32">
                <CustomSelect
                  className="slected-wrap"
                  placeholder="Select Category"
                  options={categoryOptions}
                  value={values?.category}
                  name="category"
                  onChange={handleChange}
                  label={<span>Category*</span>}
                />
                {touched?.category && errors?.category && (
                  <div className="error">{errors?.category}</div>
                )}
              </Box>

              <Box className="pt32">
                <CustomSelect
                  className="slected-wrap"
                  placeholder="Select Issue Type"
                  options={issueTypeOptions}
                  value={values?.issueType}
                  name="issueType"
                  onChange={handleChange}
                  label={<span>Issue Type*</span>}
                />
                {touched?.issueType && errors?.issueType && (
                  <div className="error">{errors?.issueType}</div>
                )}
              </Box>
            </Box>

            <Box className="pt16">
              <Typography className="Inter12 discription-text" variant="h6">
                Description*
              </Typography>
              <CustomEditor
                content={values.description}
                setContent={(content) => setFieldValue('description', content)}
                height="400px"
              />
              {touched?.description && errors?.description && (
                <div className="error">{errors?.description}</div>
              )}
            </Box>

            <Box className="pt16">
              <Typography className="Inter12 discription-text" variant="h6">
                Attachment
              </Typography>
              <Box className="upload-sec">
                <Box {...getRootProps()} className="upload-area">
                  <CloudUploadOutlinedIcon />
                  <input {...getInputProps()} />
                  <Typography className="p14 upload-text">
                    Drop your images or videos here or click to upload
                  </Typography>
                </Box>
              </Box>

              {/* Display Image and Video Previews */}
              <Box className="media-previews">
                {mediaFiles?.map((media, index) => (
                  <Box key={index} className="preview-container">
                    {media?.type === 'image' ? (
                      <Box className="image-container">
                        <Image
                          src={media?.preview}
                          alt={`preview-${index}`}
                          className="preview-img"
                          width={100}
                          height={100}
                          onClick={() => handlePreviewClick(media?.preview)}
                        />
                      </Box>
                    ) : media.type === 'video' ? (
                      <Box
                        className="video-container"
                        sx={{ cursor: 'pointer' }}
                        onClick={() => handlePreviewClick(media)}
                      >
                        <video
                          className="preview-video"
                          width="100"
                          height="100"
                          onClick={() => handlePreviewClick(media)}
                        >
                          <source src={media?.preview} />
                          Your browser does not support the video tag.
                        </video>
                      </Box>
                    ) : null}
                    <Box className="close-icon-wrap">
                      <CloseIcon
                        onClick={() => removeMedia(index)}
                        sx={{ cursor: 'pointer' }}
                        className="close-icon"
                      />
                    </Box>
                  </Box>
                ))}
              </Box>
            </Box>

            {/* Other Input Fields */}
            <Box className="personal-info-wrap">
              <Typography className="pt16" component="p">
                Please provide your personal details for further communication.
              </Typography>
              <Box className="text-input-wrap pt32">
                <CustomTextField
                  InputLabelProps={{ shrink: true }}
                  id="Name"
                  name="Name"
                  label="Name*"
                  placeholder="Enter your name"
                  value={values?.Name}
                  error={Boolean(touched?.Name && errors?.Name)}
                  helperText={touched?.Name && errors?.Name}
                  className="w100"
                  variant="filled"
                  onBlur={handleBlur}
                  onChange={handleChange}
                />

                <CustomTextField
                  InputLabelProps={{ shrink: true }}
                  id="Email"
                  name="Email"
                  label="Email*"
                  placeholder="Enter your email"
                  value={values?.Email}
                  error={Boolean(touched?.Email && errors?.Email)}
                  helperText={touched?.Email && errors?.Email}
                  className="w100"
                  variant="filled"
                  onBlur={handleBlur}
                  onChange={handleChange}
                />

                <CustomTextField
                  InputLabelProps={{ shrink: true }}
                  id="PhoneNumber"
                  name="PhoneNumber"
                  label="Phone Number*"
                  placeholder="Enter your phone number"
                  value={values?.PhoneNumber}
                  error={Boolean(touched?.PhoneNumber && errors?.PhoneNumber)}
                  helperText={touched?.PhoneNumber && errors?.PhoneNumber}
                  className="w100"
                  variant="filled"
                  onBlur={handleBlur}
                  onChange={handleChange}
                />
              </Box>
            </Box>

            {/* Terms and Conditions */}
            <Box className="pt16">
              <FormControlLabel
                control={
                  <Checkbox
                    checked={values?.term_condition}
                    onChange={handleChange}
                    name="term_condition"
                    color="primary"
                  />
                }
                label="I agree to the terms and conditions."
              />
              {touched?.term_condition && errors?.term_condition && (
                <div className="error">{errors?.term_condition}</div>
              )}
            </Box>

            <Box className="pt16 d-flex justify-end create-ticket-wrap gap-5">
              <CustomButton
                className="p16 cancel-ticket-btn"
                type="submit"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title="Cancel"
              />
              <CustomButton
                className="p16 create-ticket-btn"
                type="submit"
                fontWeight="600"
                variant="contained"
                background="#39596e"
                backgroundhover="#39596e"
                colorhover="#FFFFFF"
                title="Create Ticket"
              />
            </Box>
          </Box>
          <PreviewModal
            open={openModal}
            setOpen={setOpenModal}
            previewMedia={previewMedia}
          />
        </Form>
      )}
    </Formik>
  );
}
