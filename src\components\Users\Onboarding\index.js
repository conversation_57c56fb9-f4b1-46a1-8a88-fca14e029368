'use client';

import React, { useState, useContext, useEffect } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
} from '@mui/material';
import FolderZipIcon from '@mui/icons-material/FolderZip';
import CustomButton from '@/components/UI/CustomButton';
import RightToWorkCList from './right-to-work';
import HmrcNewstarter from './hmrc-newstarter';
import EmploymentContract from '@/components/Users/<USER>/emp-contract';
import HealthandSafety from '@/components/Users/<USER>/health-safety';
import NewStarterFormPDF from '@/components/PDFGeneration/NewStarter';
import HMRCFormPDF from '@/components/PDFGeneration/HMRC';
import HealthAndSafetyPDF from '@/components/PDFGeneration/HealthAndSafety';
import VerifyDocument from '@/components/Users/<USER>/verify-document';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import UserActivity from '@/components/Users/<USER>';
import InsertDriveFileOutlinedIcon from '@mui/icons-material/InsertDriveFileOutlined';
import AuthContext from '@/helper/authcontext';
import HeaderImage from '@/components/UI/ImageSecurity';
import EmploymentContractHistory from '@/components/Users/<USER>/emp-contract-history/index';
import SideLetter from '@/components/Users/<USER>/index';
import WorkSchedule from '@/components/WorkSchedule';
import MyLeave from '@/components/Leave/MyLeave';
import HolidayList from '@/components/Leave/Holiday/HolidatLIst';
import moment from 'moment';
import { ReloadIcon } from '@/helper/common/images';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import CustomTabs from '@/components/UI/CustomTabs';
import { ClipboardCheckIcon, ClipboardListIcon } from '@/helper/common/images';
import NoDataView from '@/components/UI/NoDataView';

const Onboarding = ({
  UserId,
  getCheckList,
  formRightToWork,
  formNewStarterHMRC,
  formHealthSafety,
  UserDetails,
  isMyProfile,
  RequestToAdmin,
  formEmpContract,
  getOnboardingChecklist,
  ViewAccessOnly,
  setLoaderMain,
  IsMainLoader,
  loader,
  countryList,
}) => {
  const currentYear = moment().year();

  const { authState } = useContext(AuthContext);
  const [leaveBalanceDetails, setLeaveBalanceDetails] = useState([]);
  const [yearFilterdata, setYearFilterData] = useState(currentYear);
  const [userHolidayList, setUserHolidayList] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [resetConfirmation, setResetConfirmation] = useState(false);
  const [selectedCheckList, setSelectedCheckList] = useState('');
  const userId = UserId ? UserId : authState?.id;

  const ISSubmit = getCheckList?.find(
    (l) => l?.status !== 'completed' && l?.id !== 3
  );
  const [expanded, setExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState(1);

  const handleChange = (panel) => {
    expanded === panel ? setExpanded() : setExpanded(panel);
  };
  const admin_tabs = [
    { id: 1, name: 'Onboarding process' },
    { id: 3, name: 'Employee Contract History' },
    { id: 7, name: 'Side Letter' },
    { id: 5, name: 'Leave Balance' },
    { id: 6, name: 'Holiday List' },
    { id: 4, name: 'Work Schedule' },
    { id: 2, name: 'Activity' },
  ];
  const user_tabs = [
    { id: 1, name: 'Onboarding process' },
    { id: 3, name: 'Employee Contract History' },
    { id: 7, name: 'Side Letter' },
    { id: 5, name: 'Leave Balance' },
    { id: 6, name: 'Holiday List' },
    { id: 4, name: 'Work Schedule' },
  ];

  const user_tabs_Myprofile = [
    { id: 1, name: 'Onboarding process' },
    { id: 3, name: 'Employee Contract History' },
    { id: 7, name: 'Side Letter' },
    { id: 5, name: 'Leave Balance' },
    { id: 6, name: 'Holiday List' },
    { id: 4, name: 'Work Schedule' },
  ];
  const tabView = isMyProfile
    ? user_tabs_Myprofile
    : authState?.UserPermission?.activity_log
      ? admin_tabs
      : user_tabs;

  const handleTabChange = (newValue) => {
    // const selectedTab = tabView[newValue];
    // setActiveTab(selectedTab?.id);
    setActiveTab(newValue);
  };

  const getCurrentTabs = () => {
    return tabView?.map((tab) => ({
      id: tab?.id,
      label: tab?.name,
    }));
  };

  const UserReject = async () => {
    const sendData = {
      user_id: UserId,
      verification_status: 'rejected',
      checklist_ids: [3],
    };

    try {
      const { status, data } = await axiosInstance.post(
        URLS.USER_VERIFICATION,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', 'User onboarding reset successfully.');
          getOnboardingChecklist(UserId);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleResetCloseModal = () => {
    setResetConfirmation(false);
  };

  const IsViewable = (index) => {
    if (
      getCheckList &&
      getCheckList?.length > 0 &&
      getCheckList?.[index] &&
      getCheckList?.[index]?.status === 'completed'
    ) {
      return true;
    }
    return false;
  };
  const IsCompleted = (index) => {
    if (
      getCheckList &&
      getCheckList?.length > 0 &&
      getCheckList?.[index] &&
      getCheckList?.[index]?.status === 'completed'
    ) {
      return true;
    }
    return false;
  };
  const DownloadAll = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.DOWNLOAD_ZIP +
          `?user_id=${UserDetails?.id ? UserDetails?.id : UserId}`,
        {
          responseType: 'blob',
        }
      );
      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'download-file.zip');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        // setLoader(false);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getSingleUserBalance = async (id) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.LEAVE_BALANCE + `${id}?year=${yearFilterdata}&leave_period_type=`
      );
      if (status === 200) {
        setLeaveBalanceDetails(data?.data);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const userOnboardingReset = async (chcekListId) => {
    let params = {
      checklist_ids: [chcekListId?.id],
      user_id: UserId,
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.USER_ONBOARDING_RESET,
        params
      );

      if (status === 200) {
        setApiMessage('success', data?.message);
        getOnboardingChecklist(UserId);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleOnboardingReset = () => {
    if (selectedCheckList?.id === 3) {
      UserReject();
    } else {
      userOnboardingReset(selectedCheckList);
    }
    setResetConfirmation(false);
  };

  useEffect(() => {
    if (userId && yearFilterdata) {
      getSingleUserBalance(userId);
    }
  }, [userId, yearFilterdata]);

  const getUserHolidayList = async (searchValue, id) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${URLS?.GET_USER_HOLIDAY_POLICY}?user_id=${id}&search=${searchValue}&page=&size=`
      );
      if (status === 200) {
        setUserHolidayList(data?.data || []);
        setTotalCount(data?.total || 0);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const getRightToWorkCheckList = () => {
    // console.log('getRightToWorkCheckList', data);//data
  };

  useEffect(() => {
    if (userId) {
      getUserHolidayList('', userId);
    }
  }, [userId]);

  const handleAccordionChange = (panel) => {
    if (!isMyProfile || UserDetails?.profile_status) {
      handleChange(panel);
    } else {
      setApiMessage(
        'error',
        'Your profile is incomplete! Please update it to start onboarding.'
      );
    }
  };

  const isUserVerificationVisible =
    !isMyProfile &&
    (authState?.user_status === 'verified' ||
      authState?.web_user_active_role_id === 1) &&
    (((authState?.web_user_active_role_id === 19 ||
      authState?.web_user_active_role_id <= 4) &&
      authState?.id !== UserDetails?.id) ||
      (authState?.web_user_active_role_id <= 4 &&
        UserDetails?.web_user_active_role_id === 19)) &&
    (ISSubmit === undefined || UserDetails?.user_status === 'rejected') &&
    (UserDetails?.user_status === 'completed' ||
      UserDetails?.user_status === 'rejected' ||
      UserDetails?.user_status === 'verified');

  const getCurrentContent = () => {
    switch (activeTab) {
      case 1:
        return (
          <>
            <Box className="onboarding-process-section">
              {getCheckList &&
                getCheckList?.length > 0 &&
                getCheckList?.map((item) => {
                  return (
                    <Box className="d-flex align-center">
                      <Box
                        className={`onboarding-form-details ${
                          item?.status === 'completed'
                            ? 'onboarding-complete'
                            : ''
                        }`}
                      >
                        <Box>
                          <Box className="d-flex align-center">
                            {item?.status === 'completed' ? (
                              <ClipboardCheckIcon />
                            ) : (
                              <ClipboardListIcon />
                            )}
                            <Typography className="title-text">
                              {item?.checkList_name}
                            </Typography>
                          </Box>

                          <Box
                            className={`${
                              item?.status === 'completed' &&
                              [1, 2, 3].includes(item?.id)
                                ? 'pt16'
                                : ''
                            }`}
                          >
                            {item?.status === 'completed' && (
                              <>
                                {item?.id === 2 &&
                                formNewStarterHMRC?.has_student_or_pg_loan ===
                                  false &&
                                formNewStarterHMRC?.has_p45_form === true &&
                                formNewStarterHMRC?.hmrc_p45_form_link ? (
                                  <HeaderImage
                                    type="url"
                                    imageUrl={
                                      formNewStarterHMRC?.hmrc_p45_form_link
                                    }
                                    Content={
                                      <Button
                                        className="pdf-download-btn"
                                        disableRipple
                                      >
                                        <span className="title-text onboarding-form-name">
                                          P45
                                        </span>
                                        <InsertDriveFileOutlinedIcon />
                                        <span className="title-text pl4 text-underline">
                                          View PDF
                                        </span>
                                      </Button>
                                    }
                                  />
                                ) : item?.id === 2 ? (
                                  <>
                                    <NewStarterFormPDF
                                      userDetails={UserDetails}
                                      formDetails={formNewStarterHMRC}
                                    />
                                    <HMRCFormPDF
                                      userDetails={UserDetails}
                                      formDetails={formNewStarterHMRC}
                                    />
                                  </>
                                ) : item?.id === 3 ? (
                                  <HealthAndSafetyPDF
                                    userDetails={UserDetails}
                                    formDetails={formHealthSafety}
                                  />
                                ) : item?.id === 1 ? (
                                  <Button
                                    onClick={DownloadAll}
                                    className="pdf-download-btn"
                                    disableRipple
                                  >
                                    <FolderZipIcon className="mb2" />
                                    <span className="title-text pl4 text-underline">
                                      Download All Documents
                                    </span>
                                  </Button>
                                ) : null}
                              </>
                            )}
                          </Box>
                        </Box>
                        <Box
                          className={`onboarding-status-details ${
                            item?.status === 'completed'
                              ? 'check-green'
                              : 'check-red'
                          }`}
                        >
                          <Typography className="sub-title-text onboarding-status">
                            {item?.status}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  );
                })}
              {isMyProfile ? (
                <Box className="pt16 d-flex align-center gap-sm">
                  <Box>
                    <Typography className="title-text">
                      Kindly submit the onboarding form once you’ve completed
                      all the required sections{' '}
                    </Typography>
                  </Box>
                  <CustomButton
                    variant="contained"
                    className={`body-text red-button ${ISSubmit !== undefined || UserDetails?.user_status !== 'ongoing' ? 'disabled-button' : ''}`}
                    title={`${loader ? 'Submit...' : 'Submit'}`}
                    disabled={
                      ISSubmit !== undefined ||
                      UserDetails?.user_status !== 'ongoing'
                    }
                    onClick={() =>
                      ISSubmit === undefined &&
                      UserDetails?.user_status === 'ongoing' &&
                      RequestToAdmin()
                    }
                  />
                </Box>
              ) : (
                <></>
              )}
            </Box>
            {getCheckList && getCheckList?.length === 0 && (
              <Box className="no-data d-flex align-center justify-center">
                <NoDataView
                  title="No Onboarding Data Found"
                  description="There is no onboarding data available at the moment."
                />
              </Box>
            )}
            <Accordion
              elevation={0}
              className="onboarding-accordion"
              expanded={expanded === 2}
              onChange={() => {
                if (!isMyProfile || UserDetails?.profile_status) {
                  handleChange(2);
                } else {
                  setApiMessage(
                    'error',
                    'Your profile is incomplete! Please update it to start onboarding.'
                  );
                }
              }}
            >
              <AccordionSummary
                expandIcon={<KeyboardArrowDownIcon />}
                className=""
              >
                <Box className="d-flex align-center justify-space-between w100">
                  <Typography className="title-sm">
                    Right to Work Checklist
                  </Typography>
                  {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT
                  HISTORY. */}
                  {/* {authState?.web_user_active_role_id === 1 ||
                  (authState?.web_user_active_role_id === 2 && !isMyProfile) ? (
                    <Box
                      className="pr16 d-flex justify-end"
                      onClick={() => {
                        setResetConfirmation(true);
                        setSelectedCheckList(getCheckList?.[0]);
                      }}
                    >
                      <Typography className="title-text pr8 d-flex align-center justify-end gap-5 reset-wrap cursor-pointer active-onboarding reset-button-wrap">
                        <ReloadIcon />
                        Reset
                      </Typography>
                    </Box>
                  ) : (
                    <></>
                  )} */}
                </Box>
              </AccordionSummary>
              <AccordionDetails className="">
                <RightToWorkCList
                  isUserScreen={true}
                  isMyProfile={isMyProfile}
                  UserDetails={UserDetails}
                  UserId={UserId}
                  formRightToWork={formRightToWork}
                  getOnboardingChecklist={getOnboardingChecklist}
                  ViewAccessOnly={
                    ViewAccessOnly ||
                    (isMyProfile && getCheckList?.[0]?.status === 'completed')
                  }
                  getCheckListId={getCheckList?.[0]}
                  getRightToWorkCheckList={() => getRightToWorkCheckList()}
                />
              </AccordionDetails>
            </Accordion>
            <Accordion
              elevation={0}
              className="onboarding-accordion"
              expanded={expanded === 3}
              onChange={() => {
                if (!isMyProfile || UserDetails?.profile_status) {
                  (IsViewable(0) || IsCompleted(2)) && handleChange(3);
                } else {
                  setApiMessage(
                    'error',
                    'Your profile is incomplete! Please update it to start onboarding.'
                  );
                }
              }}
            >
              <AccordionSummary
                expandIcon={<KeyboardArrowDownIcon />}
                className=""
              >
                <Box className="d-flex align-center justify-space-between w100">
                  <Typography className="title-sm">
                    HMRC & New starter form
                  </Typography>
                  {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT
                  HISTORY. */}
                  {/* {authState?.web_user_active_role_id === 1 ||
                  (authState?.web_user_active_role_id === 2 && !isMyProfile) ? (
                    <Box
                      className="pr16 d-flex justify-end"
                      onClick={() => {
                        setResetConfirmation(true);
                        setSelectedCheckList(getCheckList?.[1]);
                      }}
                    >
                      <Typography className="title-text pr8 d-flex align-center justify-end gap-5 reset-wrap cursor-pointer active-onboarding reset-button-wrap">
                        <ReloadIcon />
                        Reset
                      </Typography>
                    </Box>
                  ) : (
                    <></>
                  )} */}
                </Box>
              </AccordionSummary>
              <AccordionDetails className="">
                <HmrcNewstarter
                  isUserScreen={true}
                  UserDetails={UserDetails}
                  UserId={UserId}
                  isMyProfile={isMyProfile}
                  formNewStarterHMRC={formNewStarterHMRC}
                  getOnboardingChecklist={getOnboardingChecklist}
                  countryList={countryList}
                  ViewAccessOnly={
                    ViewAccessOnly ||
                    (isMyProfile && getCheckList?.[1]?.status === 'completed')
                  }
                  getCheckListId={getCheckList?.[1]}
                />
              </AccordionDetails>
            </Accordion>
            <Accordion
              elevation={0}
              className="onboarding-accordion"
              expanded={expanded === 5}
              onChange={() => {
                if (!isMyProfile || UserDetails?.profile_status) {
                  (IsViewable(1) || IsCompleted(3)) && handleChange(5);
                } else {
                  setApiMessage(
                    'error',
                    'Your profile is incomplete! Please update it to start onboarding.'
                  );
                }
              }}
            >
              <AccordionSummary
                expandIcon={<KeyboardArrowDownIcon />}
                className=""
              >
                <Box className="d-flex align-center justify-space-between w100">
                  <Typography className="title-sm">
                    Employment contract
                  </Typography>
                  {/* TODO: WE NEED TO HIDE THEM UNTIL WE COMPLETE THE DOCUMENT
                  HISTORY. */}
                  {/* {authState?.web_user_active_role_id === 1 ||
                  (authState?.web_user_active_role_id === 2 && !isMyProfile) ? (
                    <Box
                      className="pr16 d-flex justify-end"
                      onClick={() => {
                        setResetConfirmation(true);
                        setSelectedCheckList(getCheckList?.[2]);
                      }}
                    >
                      <Typography className="title-text pr8 d-flex align-center justify-end gap-5 reset-wrap cursor-pointer active-onboarding reset-button-wrap">
                        <ReloadIcon />
                        Reset
                      </Typography>
                    </Box>
                  ) : (
                    <></>
                  )} */}
                </Box>
              </AccordionSummary>
              <AccordionDetails className="">
                <EmploymentContract
                  isUserScreen={true}
                  UserDetails={UserDetails}
                  UserId={UserId}
                  isMyProfile={isMyProfile}
                  formEmpContract={formEmpContract}
                  getOnboardingChecklist={getOnboardingChecklist}
                  setLoaderMain={setLoaderMain}
                  IsMainLoader={IsMainLoader}
                  ViewAccessOnly={ViewAccessOnly}
                />
              </AccordionDetails>
            </Accordion>
            {isUserVerificationVisible && (
              <Accordion
                elevation={0}
                className="onboarding-accordion"
                expanded={expanded === 6}
                onChange={() => handleAccordionChange(6)}
              >
                <AccordionSummary expandIcon={<KeyboardArrowDownIcon />}>
                  <Typography className="title-sm">
                    User Verification
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <VerifyDocument
                    isUserScreen={true}
                    UserDetails={UserDetails}
                    UserId={UserId}
                    isMyProfile={isMyProfile}
                    formEmpContract={formEmpContract}
                    getOnboardingChecklist={getOnboardingChecklist}
                    setLoaderMain={setLoaderMain}
                    IsMainLoader={IsMainLoader}
                    ViewAccessOnly={
                      UserDetails?.user_status === 'deleted' ||
                      authState?.UserPermission?.user_verification === 1
                    }
                    getCheckList={getCheckList}
                  />
                </AccordionDetails>
              </Accordion>
            )}
            <Accordion
              elevation={0}
              className="onboarding-accordion"
              expanded={expanded === 4}
              onChange={() => {
                if (!isMyProfile || UserDetails?.profile_status) {
                  handleChange(4);
                } else {
                  setApiMessage(
                    'error',
                    'Your profile is incomplete! Please update it to start onboarding.'
                  );
                }
              }}
            >
              <AccordionSummary
                expandIcon={<KeyboardArrowDownIcon />}
                className=""
              >
                <Box className="d-flex align-center justify-space-between w100">
                  <Typography className="title-sm">
                    Health & safety induction
                  </Typography>
                  {authState?.web_user_active_role_id === 1 ||
                  (authState?.web_user_active_role_id === 2 && !isMyProfile) ? (
                    <Box
                      className="pr16 d-flex justify-end"
                      onClick={() => {
                        setResetConfirmation(true);
                        setSelectedCheckList(getCheckList?.[3]);
                      }}
                    >
                      <Typography className="title-text pr8 d-flex align-center justify-end gap-5 reset-wrap cursor-pointer active-onboarding reset-button-wrap">
                        <ReloadIcon />
                        Reset
                      </Typography>
                    </Box>
                  ) : (
                    <></>
                  )}
                </Box>
              </AccordionSummary>
              <AccordionDetails className="">
                <HealthandSafety
                  isUserScreen={true}
                  UserDetails={UserDetails}
                  UserId={UserId}
                  isMyProfile={isMyProfile}
                  formHealthSafety={formHealthSafety}
                  getOnboardingChecklist={getOnboardingChecklist}
                  IsPending={getCheckList?.[3]?.status === 'pending'}
                  ViewAccessOnly={
                    ViewAccessOnly ||
                    (isMyProfile && getCheckList?.[3]?.status === 'completed')
                  }
                />
              </AccordionDetails>
            </Accordion>
          </>
        );
      case 2:
        return <UserActivity userId={isMyProfile ? UserDetails?.id : UserId} />;
      case 3:
        return (
          <Box>
            <EmploymentContractHistory
              UserId={isMyProfile ? UserDetails?.id : UserId}
            />
          </Box>
        );
      case 4:
        return (
          <Box>
            <WorkSchedule UserDetails={UserDetails} />
          </Box>
        );
      case 5:
        return (
          <Box>
            <MyLeave
              UserDetails={UserDetails}
              userDetails={leaveBalanceDetails}
              setYearFilterData={setYearFilterData}
              yearFilterdata={yearFilterdata}
            />
          </Box>
        );
      case 6:
        return (
          <Box>
            <HolidayList
              getUserHolidayList={getUserHolidayList}
              userId={userId}
              userHolidayList={userHolidayList}
              setUserHolidayList={setUserHolidayList}
              searchValue={searchValue}
              setSearchValue={setSearchValue}
            />
          </Box>
        );
      case 7:
        return (
          <Box>
            <SideLetter
              UserId={isMyProfile ? UserDetails?.id : UserId}
              isMyProfile={isMyProfile}
            />
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box>
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <CustomTabs
            tabs={getCurrentTabs()}
            initialTab={activeTab}
            onTabChange={handleTabChange}
          />
        </Box>
        <Box className="section-right-content pt32">{getCurrentContent()}</Box>
      </Box>
      <DialogBox
        open={resetConfirmation}
        handleClose={() => {
          handleResetCloseModal();
        }}
        title={'Confirmation'}
        className="confirmation-modal"
        dividerClass="confirmation-modal-divider"
        content={
          <>
            <ConfirmationModal
              handleCancel={handleResetCloseModal}
              handleConfirm={() => {
                handleOnboardingReset();
              }}
              text="Are you sure you want to Reset?"
              confirmText="Reset"
            />
          </>
        }
      />
    </Box>
  );
};

export default Onboarding;
