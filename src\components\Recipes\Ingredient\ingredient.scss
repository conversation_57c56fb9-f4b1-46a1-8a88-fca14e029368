body {
  .ingredient-form-container {
    .ingredient-grid-container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: var(--spacing-lg);
      @media (max-width: 1260px) {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
      }
      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }
    .ingredient-nutrition-main-grid-container {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
      width: 100%;
    }
    .ingredient-nutrition-grid-container {
      gap: var(--spacing-sm);
      display: flex;
      width: 24%;
      @media (max-width: 1260px) {
        width: 32%;
      }
      @media (max-width: 768px) {
        width: 49%;
      }
      @media (max-width: 480px) {
        width: 100%;
      }
      .nutrition-value-input {
        width: 130px;
        @media (max-width: 768px) {
          width: 100%;
        }
      }
      .nutrition-measure-select {
        width: 90px;
        @media (max-width: 768px) {
          width: 100%;
        }
      }
    }
    .ingredient-conversion-main-grid-container {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
    }
    .ingredient-conversion-grid-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-sm);
      @media (max-width: 560px) {
        grid-template-columns: 1fr;
      }
    }
    .ingredient-allergens-grid-container {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
      gap: var(--spacing-sm);
      @media (max-width: 768px) {
        grid-template-columns: 1fr 1fr 1fr;
      }
      @media (max-width: 480px) {
        grid-template-columns: 1fr 1fr;
      }
    }
    .ingredient-textarea-container {
      width: 66%;
      @media (max-width: 1260px) {
        width: 100%;
      }
    }
    .ingredient-half-field {
      width: 50%;
    }
  }
  .category-name-text {
    color: var(--text-color-slate-gray);
  }
}
