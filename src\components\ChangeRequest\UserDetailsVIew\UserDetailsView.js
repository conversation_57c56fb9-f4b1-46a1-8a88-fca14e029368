import React from 'react';
import { Box, Typography } from '@mui/material';
import UserAvatar from '../../UI/Avatar/UserAvatar';
import { DateFormat } from '@/helper/common/commonFunctions';
import BusinessIcon from '@mui/icons-material/Business';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import './userdetailview.scss';

const UserDetailsView = ({
  userData,
  handleRequestStatus,
  showAvatar = true,
  showBranch = true,
  showDate = true,
  showStatus = true,
  createdAt,
  status,
}) => {
  if (!userData?.user_full_name) return null;

  return (
    <Box className="user-details-view d-flex pt4">
      {showAvatar && (
        <UserAvatar
          name={userData?.user_full_name}
          src={userData?.user_avatar_link}
          classname="details-user-icon"
        />
      )}
      <Box className="pl16">
        <Box className="d-flex align-center gap-sm">
          <Typography className="sub-header-text text-capital">
            <span>{userData?.user_full_name}</span>
          </Typography>
          {showStatus && status && (
            <Box className="d-flex align-center">
              <Typography className="title-text fw400">
                <span>{handleRequestStatus(status)}</span>
              </Typography>
            </Box>
          )}
        </Box>
        {showBranch && userData?.branch?.branch_name && (
          <Box className="d-flex pt4 align-center">
            <BusinessIcon className="user-details-icon" />
            <Typography className="sub-title-text bg-branch-transparent text-ellipsis">
              <span className="cr-branch-name">
                {userData?.branch?.branch_name}
              </span>
            </Typography>
          </Box>
        )}
        {showDate && createdAt && (
          <Box className="d-flex align-center pt4">
            <CalendarTodayIcon className="user-details-icon" />
            <Typography className="sub-title-text fw400">
              <span>{DateFormat(createdAt, 'datesWithhour')}</span>
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default UserDetailsView;
