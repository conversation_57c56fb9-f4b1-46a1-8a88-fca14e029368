import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './AutoSaveIndicator.scss';

const AutoSaveIndicator = () => {
  const [saveStatus, setSaveStatus] = useState('saved'); // 'saving', 'saved', 'error'
  const [lastSaved, setLastSaved] = useState(new Date());
  const [isVisible, setIsVisible] = useState(false);

  // Simulate auto-save functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setSaveStatus('saving');
      setIsVisible(true);

      // Simulate save operation
      setTimeout(() => {
        setSaveStatus('saved');
        setLastSaved(new Date());

        // Hide on mobile after showing save confirmation
        setTimeout(() => {
          setIsVisible(false);
        }, 2000);
      }, 1000);
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const getStatusConfig = () => {
    switch (saveStatus) {
      case 'saving':
        return {
          icon: 'Loader2',
          text: 'Saving...',
          iconClass: 'auto-save-indicator__icon--spinning',
        };
      case 'saved':
        return {
          icon: 'CheckCircle',
          text: 'Saved',
          iconClass: '',
        };
      case 'error':
        return {
          icon: 'AlertCircle',
          text: 'Save failed',
          iconClass: '',
        };
      default:
        return {
          icon: 'CheckCircle',
          text: 'Saved',
          iconClass: '',
        };
    }
  };

  const formatLastSaved = () => {
    const now = new Date();
    const diffInMinutes = Math.floor((now - lastSaved) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes === 1) return '1 minute ago';
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours === 1) return '1 hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    return lastSaved.toLocaleDateString();
  };

  const config = getStatusConfig();

  return (
    <div className={`auto-save-indicator auto-save-indicator--${saveStatus}`}>
      {/* Desktop: Always visible indicator */}
      <div className="auto-save-indicator__desktop">
        <div className="auto-save-indicator__status">
          <Icon
            name={config.icon}
            size={14}
            color="currentColor"
            className={`auto-save-indicator__icon ${config.iconClass}`}
          />
          <span className="auto-save-indicator__text">{config.text}</span>
        </div>

        {saveStatus === 'saved' && (
          <div className="auto-save-indicator__timestamp">
            <Icon name="Clock" size={12} color="currentColor" />
            <span className="auto-save-indicator__timestamp-text">
              {formatLastSaved()}
            </span>
          </div>
        )}
      </div>

      {/* Mobile: Temporary indicator that appears during save operations */}
      <div
        className={`auto-save-indicator__mobile ${
          isVisible || saveStatus === 'saving'
            ? 'auto-save-indicator__mobile--visible'
            : ''
        }`}
      >
        <div className="auto-save-indicator__status">
          <Icon
            name={config.icon}
            size={16}
            color="currentColor"
            className={`auto-save-indicator__icon ${config.iconClass}`}
          />
          <div className="auto-save-indicator__mobile-content">
            <span className="auto-save-indicator__text">{config.text}</span>
            {saveStatus === 'saved' && (
              <span className="auto-save-indicator__timestamp-text">
                {formatLastSaved()}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoSaveIndicator;
