import React, { useState } from 'react';
import {
  Typography,
  Box,
  InputAdornment,
  FormControlLabel,
  Checkbox,
  Tooltip,
} from '@mui/material';
import HelpIcon from '@mui/icons-material/Help';
import SearchIcon from '@mui/icons-material/Search';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import PreLoader from '@/components/UI/Loader';
import './rotaMenumodal.scss';

const CopyRangeModal = ({
  copyRangeStaffList,
  setIsCopyModal,
  getRotaShiftList,
}) => {
  const [copyStartDate, setCopyStartDate] = useState(null);
  const [copyEndDate, setCopyEndDate] = useState(null);
  const [pasteStartDate, setPasteStartDate] = useState(null);
  const [pasteEndDate, setPasteEndDate] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [copyDaysOff, setCopyDaysOff] = useState(false);
  const [shiftClashes, setShiftClashes] = useState('clear');
  const [selectedEmployees, setSelectedEmployees] = useState({});
  const [isLoader, setIsLoader] = useState(false);

  const shiftClashesOptions = [
    { value: 'clear', label: 'Clear shifts before pasting' },
    { value: 'overwrite', label: 'Overwrite clashing shifts' },
    { value: 'openshift', label: `Move copied shifts to 'Open Shifts'` },
    { value: 'skip', label: `Don't copy shifts that clash` },
  ];

  const handleEmployeeSelect = (employeeId) => {
    setSelectedEmployees((prev) => ({
      ...prev,
      [employeeId]: !prev[employeeId],
    }));
  };

  const handleSelectAll = () => {
    const allSelected = copyRangeStaffList?.every(
      (emp) => selectedEmployees[emp?.id]
    );
    const newSelection = {};
    copyRangeStaffList.forEach((emp) => {
      newSelection[emp?.id] = !allSelected;
    });
    setSelectedEmployees(newSelection);
  };

  const handleDeselectAll = () => {
    setSelectedEmployees({});
  };

  const filteredEmployees = copyRangeStaffList?.filter((emp) =>
    emp?.user_full_name?.toLowerCase()?.includes(searchTerm?.toLowerCase())
  );

  const handleCopyRangeShift = async () => {
    // Collect selected employee IDs
    const selectedEmpIds = Object.keys(selectedEmployees).filter(
      (key) => selectedEmployees[key]
    );

    const copyStart = copyStartDate
      ? dayjs(copyStartDate).format('YYYY-MM-DD')
      : null;
    const copyEnd = copyEndDate
      ? dayjs(copyEndDate).format('YYYY-MM-DD')
      : null;
    const pasteStart = pasteStartDate
      ? dayjs(pasteStartDate).format('YYYY-MM-DD')
      : null;
    const pasteEnd = pasteEndDate
      ? dayjs(pasteEndDate).format('YYYY-MM-DD')
      : null;

    // Validation
    if (!copyStart || !copyEnd || !pasteStart || !pasteEnd) {
      return setApiMessage('error', 'Please select all date fields.');
    }

    if (copyStart > copyEnd) {
      return setApiMessage('error', 'Copy end date must be after start date.');
    }

    if (pasteStart > pasteEnd) {
      return setApiMessage('error', 'Paste end date must be after start date.');
    }

    if (selectedEmpIds.length === 0) {
      return setApiMessage('error', 'Please select at least one employee.');
    }

    // Check for overlap
    if (pasteStart <= copyEnd && pasteEnd >= copyStart) {
      return setApiMessage(
        'error',
        'Copy and paste date ranges must not overlap.'
      );
    }

    const sendData = {
      fromStartDate: dayjs(copyStartDate).format('YYYY-MM-DD'),
      fromEndDate: dayjs(copyEndDate).format('YYYY-MM-DD'),
      toStartDate: dayjs(pasteStartDate).format('YYYY-MM-DD'),
      toEndDate: dayjs(pasteEndDate).format('YYYY-MM-DD'),
      selectedEmployees: Object.keys(selectedEmployees)
        .filter((id) => selectedEmployees[id])
        .map((id) => Number(id)),
      copyDaysOff: copyDaysOff,
      shiftClashStrategy: shiftClashes,
    };

    setIsLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.put(
        ROTA_URLS?.COPY_SHIFT_RANGE,
        sendData
      );
      if (status === 200 || status === 201) {
        setApiMessage('success', data?.message);
        getRotaShiftList();
        setIsLoader(false);
      }
    } catch (err) {
      setApiMessage('error', err?.response?.data?.message);
      setIsLoader(false);
    }
  };

  return (
    <Box className="copy-range-modal">
      {isLoader && <PreLoader />}
      <Box className="description-section">
        <Typography className="copy-range-modal-body-text">
          Copy a custom range of shifts by choosing start and end dates from
          which to copy from and paste to. The copied shifts will loop / repeat
          to fill the entire pasted date range.
        </Typography>
        <Typography className="copy-range-modal-body-sm">
          E.g. Copying 1-7 Jan (7 days) into 8-28 Jan (21 days) would duplicate
          the copied week 3 times.
        </Typography>
      </Box>

      <Box className="copy-paste-section">
        <Box className="copy-section">
          <Typography variant="h6" className="section-title">
            Copy{' '}
            <Tooltip
              title={
                <div>
                  <p className="p12">Select the date range of shifts to copy</p>
                </div>
              }
              placement="top"
              arrow
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <HelpIcon className="help-icon" />
            </Tooltip>
          </Typography>
          <CustomDatePicker
            label="Start Date"
            name="copy_start_date"
            placeholder="Start Date"
            value={copyStartDate}
            onChange={setCopyStartDate}
          />

          <CustomDatePicker
            label="End Date"
            name="copy_end_date"
            placeholder="End Date"
            value={copyEndDate}
            onChange={setCopyEndDate}
          />

          <Tooltip
            title={
              <div>
                <p className="p12">Days will update automatically</p>
              </div>
            }
            placement="bottom"
            arrow
            classes={{
              tooltip:
                'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
            }}
          >
            <Box>
              <CustomTextField
                name="copy_days"
                label="Days"
                value={
                  copyStartDate &&
                  copyEndDate &&
                  copyEndDate.diff(copyStartDate, 'day') + 1
                }
                disabled
              />
            </Box>
          </Tooltip>
        </Box>

        <Box className="arrow-section">
          <ArrowForwardIosIcon />
          {/* <img src="https://app.rotacloud.com/assets/img/icons/copyCustomArrow.png" /> */}
        </Box>

        <Box className="paste-section">
          <Typography variant="h6" className="section-title">
            Paste{' '}
            <Tooltip
              title={
                <div>
                  <p className="p12">
                    Select where you'd like to paste the copied range (shifts
                    will loop / repeat to fill the selection if this date range
                    is longer than the copied range)
                  </p>
                </div>
              }
              placement="top"
              arrow
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <HelpIcon className="help-icon" />
            </Tooltip>
          </Typography>
          <CustomDatePicker
            label="Start Date"
            name="paste_start_date"
            placeholder="Start Date"
            value={pasteStartDate}
            onChange={setPasteStartDate}
          />

          <CustomDatePicker
            label="End Date"
            name="paste_end_date"
            placeholder="End Date"
            value={pasteEndDate}
            onChange={setPasteEndDate}
          />

          <Tooltip
            title={
              <div>
                <p className="p12">Days will update automatically</p>
              </div>
            }
            placement="bottom"
            arrow
            classes={{
              tooltip:
                'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
            }}
          >
            <Box>
              <CustomTextField
                name="paste_days"
                label="Days"
                value={
                  pasteStartDate &&
                  pasteEndDate &&
                  pasteEndDate.diff(pasteStartDate, 'day') + 1
                }
                disabled
              />
            </Box>
          </Tooltip>
        </Box>
      </Box>

      <Box className="employees-section">
        <Typography variant="h6" className="section-title">
          Employees{' '}
          <Tooltip
            title={
              <div>
                <p className="p12">
                  Select the employees whose shifts you wish to copy
                </p>
              </div>
            }
            placement="top"
            arrow
            classes={{
              tooltip:
                'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
            }}
          >
            <HelpIcon className="help-icon" />
          </Tooltip>
        </Typography>
        <Box className="search-box">
          <CustomTextField
            name="search_emp"
            label=""
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          <Box className="select-actions">
            <Typography
              component="span"
              className="action-link"
              onClick={handleSelectAll}
            >
              Select All
            </Typography>
            {' • '}
            <Typography
              component="span"
              className="action-link"
              onClick={handleDeselectAll}
            >
              Deselect All
            </Typography>
          </Box>
        </Box>
        {filteredEmployees?.length !== 0 ? (
          <Box className="employees-grid">
            {filteredEmployees?.map((employee) => (
              <Box key={employee?.id} className="employee-item">
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={!!selectedEmployees[employee?.id]}
                      onChange={() => handleEmployeeSelect(employee?.id)}
                    />
                  }
                  label={employee?.user_full_name}
                />
              </Box>
            ))}
          </Box>
        ) : (
          <p className="text-align h6">No data found</p>
        )}
      </Box>

      <Box className="options-section">
        <Box className="shift-clashes">
          <Typography variant="h6" className="section-title">
            Shift Clashes{' '}
            <Tooltip
              title={
                <div>
                  <p className="p12">
                    When pasting your range of shifts, some of the new shifts
                    may clash with shifts you have already assigned. Use the
                    drop-down to select which action to take when the clashes
                    occur
                  </p>
                </div>
              }
              placement="top"
              arrow
              classes={{
                tooltip:
                  'calendar-event-list-tooltip-custom calendar-event-other-details-tooltip',
              }}
            >
              <HelpIcon className="help-icon" />
            </Tooltip>
          </Typography>
          <CustomSelect
            label=""
            name="select_shift_clashes"
            placeholder="Select Shift Clashes"
            options={shiftClashesOptions}
            value={
              shiftClashesOptions?.find((opt) => opt?.value === shiftClashes) ||
              'clear'
            }
            onChange={(selectedOption) =>
              setShiftClashes(selectedOption?.value || 'clear')
            }
            menuPosition="fixed"
          />
        </Box>

        <Box className="copy-options">
          <Typography variant="h6" className="section-title">
            Options
          </Typography>
          <FormControlLabel
            control={
              <Checkbox
                checked={copyDaysOff}
                onChange={(e) => setCopyDaysOff(e.target.checked)}
              />
            }
            label="Copy days off?"
          />
        </Box>
      </Box>

      <Box className="copy-action-section">
        <CustomButton
          variant="contained"
          className=""
          title="Copy Shifts"
          onClick={handleCopyRangeShift}
        />
        <CustomButton
          variant="outlined"
          className=""
          title="Cancel"
          onClick={() => setIsCopyModal({ isCopyRangeModal: false })}
        />
      </Box>
    </Box>
  );
};

export default CopyRangeModal;
