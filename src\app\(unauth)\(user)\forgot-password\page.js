'use client';

import React, { useContext } from 'react';
import { Box, Typography } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { Formik, Form } from 'formik';
import { CustomTextField } from '@/components/UI/CommonField/index';
import CustomButton from '@/components/UI/button';
import { saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import * as Yup from 'yup';
import './forgotpw.scss';
import AppBrandLogo from '@/components/UI/AppBrandLogo';

export default function Forgotpassword() {
  const { authState, setAuthState } = useContext(AuthContext);
  const router = useRouter();
  // const [loader, setLoader] = useState(false);
  return (
    <>
      <Box className="login-page d-flex justify-center align-center">
        <Box className="login-block d-flex justify-center">
          <AppBrandLogo
            className="text-align"
            onClick={() => router.push('/login')}
          />
          {/* <Box className="login-screen">
            <Image
              src={TTHLogo}
              height={72}
              width={200}
              className="header-logo cursor-pointer "
              alt="img"
              onClick={() => router.push('/login')}
            />
          </Box> */}
          <Typography
            variant="h3"
            className="h3 main-heading fw700 pt16 text-align"
          >
            Forgot Password
          </Typography>
          <Typography variant="h6" className="p16 forgot-content fw400  pt40">
            Please enter your email Id. We will send a code to your email
            address to reset your password.
          </Typography>
          <Formik
            initialValues={{
              email: authState?.Email
                ? authState?.Email
                : // : fetchFromStorage(identifiers?.EMAIL)
                  // ? fetchFromStorage(identifiers?.EMAIL)
                  '',
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              email: Yup.string()
                .required('This field is required')
                .matches(
                  /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
                  'Please enter valid email'
                ),
            })}
            onSubmit={async (requestData) => {
              let sendData = {
                user_email: requestData?.email,
              };

              try {
                // setLoader(true);
                const { status, data } = await axiosInstance.post(
                  URLS.FORGOT_PASSWORD,
                  sendData
                );
                if (status === 200) {
                  if (data.status) {
                    setApiMessage('success', data?.message);
                    saveToStorage(identifiers?.EMAIL, requestData?.email);
                    setAuthState({ Email: requestData?.email });
                    router.push(`/otp`);
                  } else {
                    setApiMessage('error', data?.message);
                  }
                  // setLoader(false);
                }
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
                // setLoader(false);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              // dirty,
              // isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="pt-32">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    id="email"
                    name="email"
                    value={values.email}
                    className="w100 "
                    label="Email address"
                    placeholder="Enter your email address"
                    variant="filled"
                    error={Boolean(touched.email && errors.email)}
                    helperText={touched.email && errors.email}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
                <Box className="pt24">
                  <Box textAlign="center" className="signin-btn ">
                    <CustomButton
                      // disabled={!isValid || !dirty}
                      fullWidth
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#39596e"
                      className="p16"
                      colorhover="#FFFFFF"
                      fontWeight="600"
                      type="submit"
                      isLogin={true}
                      title="Get OTP"
                    />
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
        </Box>
      </Box>
    </>
  );
}
