import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';

export const changeRequestService = {
  getChangeRequestList: async (
    search = '',
    pageNo = 1,
    statusValue = '',
    date = '',
    rowsPerPage = 10
  ) => {
    try {
      const datef = date ? dayjs(date)?.format('YYYY-MM-DD') : '';
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CR_LIST +
          `?search=${search}&page=${pageNo}&size=${rowsPerPage}&change_request_status=${statusValue}&change_request_date=${datef}`
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getOwnChangeRequestList: async (
    search = '',
    pageNo = 1,
    statusValue = '',
    date = '',
    rowsPerPage = 10
  ) => {
    try {
      const datef = date ? dayjs(date)?.format('YYYY-MM-DD') : '';
      const { status, data } = await axiosInstance.get(
        URLS?.GET_OWN_CR_LIST +
          `?search=${search}&page=${pageNo}&size=${rowsPerPage}&change_request_status=${statusValue}&change_request_date=${datef}`
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  deleteChangeRequest: async (id) => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_CHANGE_REQUEST + id
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  // Get change request by ID
  getCRById: async (crId) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CR_BY_ID + `${crId}`
      );
      return { status, data };
    } catch (error) {
      throw error;
    }
  },

  // Approve/Reject change request
  updateCRStatus: async (crId, requestData) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_CR + `${crId}`,
        requestData
      );
      return { status, data };
    } catch (error) {
      throw error;
    }
  },
};

export const addChangeRequest = async (requestData, files) => {
  const body = new FormData();

  if (requestData?.subject) {
    body.append('change_request_subject', requestData.subject);
  }
  if (requestData?.old_value) {
    body.append('old_data', requestData.old_value);
  }
  if (requestData?.new_value) {
    body.append('new_data', requestData.new_value);
  }

  if (files && files.length > 0) {
    files.forEach((file) => {
      body.append('change_request_files', file);
    });
  }

  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };

  const response = await axiosInstance.post(URLS.ADD_CR, body, config);
  return response;
};

export const updateChangeRequest = async (requestData, files, crId) => {
  const body = new FormData();

  requestData?.subject &&
    body.append('change_request_subject', requestData?.subject);
  requestData?.old_value && body.append('old_data', requestData?.old_value);
  requestData?.new_value && body.append('new_data', requestData?.new_value);

  files &&
    files?.length > 0 &&
    files.map((file) => {
      body.append(`change_request_files`, file);
    });

  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };

  const response = await axiosInstance.post(
    URLS.ADD_CR + `/${crId}`,
    body,
    config
  );

  return response;
};

export const getChangeRequestById = async (crId) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_CR_BY_ID + `${crId}`
    );
    if (status === 200) {
      return { success: true, data: data?.data };
    }
    return { success: false, data: null };
  } catch (error) {
    return {
      success: false,
      data: null,
      error: error?.response?.data?.message,
    };
  }
};
