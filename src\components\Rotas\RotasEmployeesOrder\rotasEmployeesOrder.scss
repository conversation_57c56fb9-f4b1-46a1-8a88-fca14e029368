.rota-order-shifts-container {
  .rota-order-filter-heading {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    .rota-order-filter-title {
      font-family: var(--font-family-primary);
      color: var(--text-color-black);
      font-size: var(--font-size-lg);
      line-height: var(--line-height-lg);
      font-weight: var(--font-weight-semibold);
    }
  }
  .rota-order-info-section {
    .rota-order-info-text {
      font-family: var(--font-family-primary);
      color: var(--text-color-primary);
      font-size: var(--font-size-sm);
      line-height: var(--line-height-sm);
      border: var(--field-border-primary);
      background: var(--color-primary-opacity);
      padding: var(--spacing-md);
      border-radius: var(--field-radius);
      width: max-content;
    }
  }
  .rota-order-shifts-table {
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    padding: var(--spacing-lg);
    margin-top: var(--spacing-sm);
    .table-container {
      margin: var(--spacing-none);
      padding: var(--spacing-none);
      background-color: none;
      box-shadow: none;
      border-radius: none;
    }
  }
}

.employee-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  font-weight: var(--font-weight-semibold);

  .header-cell {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }
}

.employee-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--color-white);
  border-bottom: 1px solid var(--border-color-light-gray);
  cursor: grab;
  transition: all 0.2s ease;
  font-family: var(--font-family-primary);
  &:hover {
    background-color: var(--color-primary-opacity);
  }

  &:active {
    cursor: grabbing;
  }

  &.is-dragging {
    background-color: var(--color-primary-opacity);
    box-shadow: var(--box-shadow-xs);
  }
}
