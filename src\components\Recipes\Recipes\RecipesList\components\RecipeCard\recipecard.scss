.recipe-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-card);
  border: 1px solid var(--border-color-light-gray);
  position: relative;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--box-shadow-xs);
    // transform: translateY(-2px);
  }

  &--selected {
    border-color: var(--color-primary);
    background-color: var(--color-primary-opacity);
  }

  // Grid view styles
  &--grid {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  // List view styles
  &--list {
    display: flex;
    align-items: center;
  }

  // Image container
  &__image-container {
    position: relative;
    width: 100%;
    height: 192px;
    background-color: var(--color-light-gray);
    overflow: hidden;
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  }

  &__image {
    width: 100%;
    height: 100%;
    object-fit: scale-down;
  }

  // Status badge
  &__status-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
  }

  &__status {
    background: var(--color-secondary);
    padding: var(--spacing-xxs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-primary);

    &--published {
      color: var(--color-success);
    }

    &--draft {
      color: var(--color-warning);
    }

    &--default {
      color: var(--text-color-slate-gray);
    }
  }

  // Bookmark button
  &__bookmark-btn {
    position: absolute;
    bottom: var(--spacing-sm);
    right: var(--spacing-sm);
    padding: var(--spacing-xs);
    border-radius: 50%;
    background-color: var(--color-white);
    color: var(--text-color-slate-gray);
    border: none;
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    display: flex;
    transition: all 0.2s ease;

    &:hover {
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
      &:hover {
        color: var(--color-white);
      }
    }
  }

  // Allergens overlay
  &__allergens-overlay {
    position: absolute;
    bottom: var(--spacing-sm);
    left: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
  }

  &__allergen {
    // padding: var(--spacing-xxs);
    // background-color: rgba(255, 255, 255, 0.9);
    color: var(--color-warning);
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-sm);
    font-family: var(--font-family-primary);

    &--overlay {
      padding: var(--spacing-xxs);
    }
  }

  &__allergen-icon {
    width: 16px;
    height: 16px;
  }
  // Content area
  &__content {
    padding: var(--spacing-md);
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: visible;
  }

  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-xs);
  }

  &__title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    font-size: var(--font-size-base);
    margin: 0;
    padding-right: var(--spacing-xs);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-family: var(--font-family-primary);
  }

  &__difficulty {
    padding: var(--spacing-xxs) var(--spacing-xsm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    font-family: var(--font-family-primary);
    flex-shrink: 0;

    &--low {
      color: var(--color-success);
      background-color: var(--color-success-opacity);
    }

    &--medium {
      color: var(--color-warning);
      background-color: var(--color-warning-opacity);
    }

    &--hard {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
    }

    &--default {
      color: var(--text-color-slate-gray);
      background-color: var(--color-light-gray);
    }
  }

  &__description {
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    font-family: var(--font-family-primary);
  }

  &__meta-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
  }

  &__meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__meta-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  &__price {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    font-family: var(--font-family-primary);
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xxs);
    margin-bottom: var(--spacing-sm);
  }

  &__tag {
    padding: var(--spacing-xxs) var(--spacing-xsm);
    background-color: var(--color-primary-opacity);
    color: var(--text-color-primary);
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-full);
    font-family: var(--font-family-primary);
  }

  &__actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color-light-gray);
    position: relative;
    overflow: visible;
  }

  &__likes {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__action-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    position: relative;
    overflow: visible;
  }

  &__action-btn {
    padding: var(--spacing-xsm);
    color: var(--text-color-slate-gray);
    border-radius: var(--border-radius-md);
    background: transparent;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--bookmarked {
      color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }
  }

  &__updated {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  // List view specific styles
  &__list-content {
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    width: 100%;
  }

  &__checkbox {
    width: 24px;
    height: 24px;
    border-radius: var(--border-radius-sm);
    border: 2px solid var(--border-color-light-gray);
    background-color: var(--color-white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &--selected {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
      color: var(--color-white);
    }
  }

  &__list-image {
    width: 64px;
    height: 64px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    background-color: var(--color-light-gray);
    flex-shrink: 0;
  }

  &__list-info {
    flex: 1;
    min-width: 0;
  }

  &__list-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-xxs);
  }

  &__list-badges {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-shrink: 0;
  }

  &__allergens {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxs);
    margin-top: var(--spacing-xs);
  }

  &__list-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-shrink: 0;
  }
}
