import { Box, IconButton, Popover, Typography, Divider } from '@mui/material';
import React, { useState } from 'react';
import NoTickets from './NoTickets';
import CreateTicket from './CreateTicket';
import Ticket from './Ticket';
import TicketDetails from './TicketDetails';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import CustomButton from '@/components/UI/button';
import './supportticket.scss';
import RightDrawer from '@/components/UI/RightDrawer';
import TicketFilter from './TicketFilter';
import SearchBar from '@/components/UI/SearchBar';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';

export default function SupportTicket() {
  const [showCreateTicket, setShowCreateTicket] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('open');
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [openTicketFilter, setOpenTicketFilter] = useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null);

  const open = Boolean(anchorEl);

  const handleStatusChange = (event) => {
    setSelectedStatus(event?.target?.value);
  };

  const toggleDrawer = () => {
    setIsDrawerOpen((prev) => !prev);
  };

  const handleAddTicketClick = () => {
    setShowCreateTicket(true);
  };

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
  };

  const handleOpenFilterDrawer = () => {
    setOpenTicketFilter(true);
  };

  const handleCloseFilterDrawer = () => {
    setOpenTicketFilter(false);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };
  return (
    <>
      <Box className="show-tickets-wrap">
        <Box className="filter-wrap d-flex justify-space-between align-center">
          <Box className="filter-btn-wrap">
            <CustomButton
              variant="contained"
              background="white"
              backgroundhover="black"
              className="filter-btn"
              onClick={handleOpenFilterDrawer}
              startIcon={<FilterAltOutlinedIcon />}
              title={'Filters'}
            />
          </Box>
          <Box className="filter-search-wrap d-flex gap-20 align-center">
            <Box className="d-flex align-center gap-5 search-filter-wrap">
              <SearchBar searchclass="filter-search" />
              <CustomButton
                variant="contained"
                background="white"
                backgroundhover="black"
                className="filter-btn search-btn"
                title={'Search'}
              />
            </Box>
            <Box className="sorting-wrap">
              <MoreHorizIcon
                onClick={handleClick}
                sx={{ cursor: 'pointer' }}
                className="sorting-icon-wrap"
              />
              <Popover
                className="sorting-popover"
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'left',
                }}
              >
                <Box className="sort-option">
                  <Typography variant="h6">Sort by</Typography>

                  <Typography component="p" className="options">
                    Due Date
                  </Typography>
                  <Typography component="p" className="options">
                    Recent Thread
                  </Typography>
                  <Typography component="p" className="options">
                    Created Time
                  </Typography>
                  <Divider />
                  <Typography component="p" className="options">
                    Show Oldest First
                  </Typography>
                  <Typography component="p" className="options">
                    Show Latest First
                  </Typography>
                </Box>
              </Popover>
            </Box>
          </Box>
        </Box>
        <Box className="ticket-container pt32">
          <Box className="tickets">
            <Ticket
              selectedStatus={selectedStatus}
              setSelectedStatus={setSelectedStatus}
              handleStatusChange={handleStatusChange}
              onTicketClick={handleTicketClick}
            />
          </Box>
          <Box className="menu-icon-wrap">
            <MenuIcon onClick={toggleDrawer} className="drawer-toggle-icon" />
          </Box>
          <Box className="tickets-details">
            {selectedTicket ? <TicketDetails ticket={selectedTicket} /> : null}
          </Box>
        </Box>
        <Box className="support-ticket-container">
          <Box className={`drawer ${isDrawerOpen ? 'open' : ''}`}>
            <Box className="drawer-content">
              <IconButton className="close-icon" onClick={toggleDrawer}>
                <CloseIcon />
              </IconButton>
              <Ticket
                selectedStatus={selectedStatus}
                setSelectedStatus={setSelectedStatus}
                handleStatusChange={handleStatusChange}
                onTicketClick={handleTicketClick}
              />
            </Box>
          </Box>
        </Box>
        <Box className="drawer-wrap">
          <RightDrawer
            className="ticket-filter-drawer"
            anchor="right"
            open={openTicketFilter}
            onClose={handleCloseFilterDrawer}
            title="Ticket Filter"
            content={<TicketFilter />}
          />
        </Box>
      </Box>
    </>
  );
}
