.audio-player {
  background-color: var(--color-primary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
  position: relative;

  &__content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__controls {
    flex: 1;
    height: 40px;
    
    &::-webkit-media-controls-panel {
      background-color: var(--color-primary);
    }
    
    &::-webkit-media-controls-current-time-display,
    &::-webkit-media-controls-time-remaining-display {
      color: white;
    }
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.8;
    }
  }
} 