'use client';
import React, { useState } from 'react';
import { Splide, SplideSlide } from '@splidejs/react-splide';
import Icon from '@/components/UI/AppIcon/AppIcon';
import LinkIcon from '@mui/icons-material/Link';
import DialogBox from '@/components/UI/Modalbox';
import '@splidejs/splide/css';
import './MediaIconsCarousel.scss';

const MediaIconsCarousel = ({ mediaIcons = [], onAudioClick }) => {
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [showDocumentModal, setShowDocumentModal] = useState(false);

  const handleIconClick = (mediaItem) => {
    if (mediaItem.type === 'audio') {
      onAudioClick?.(mediaItem);
    } else if (mediaItem.type === 'document') {
      setSelectedDocument(mediaItem);
      setShowDocumentModal(true);
    } else if (mediaItem.type === 'link' && mediaItem.url) {
      window.open(mediaItem.url, '_blank', 'noopener,noreferrer');
    }
  };

  const handleCloseDocumentModal = () => {
    setShowDocumentModal(false);
    setSelectedDocument(null);
  };

  return (
    <div className="media-icons-carousel-container">
      <Splide
        options={{
          perPage: 13,
          gap: '15px',
          rewind: false,
          pagination: false,
          arrows: true,
          perMove: 1,
          breakpoints: {
            767: {
              perPage: 10,
              gap: '15px',
            },
            575: {
              perPage: 6,
              gap: '15px',
            },
            319: {
              perPage: 4,
              gap: '15px',
            },
          },
        }}
        className="media-icons-carousel"
      >
        {mediaIcons.map((mediaItem) => (
          <SplideSlide key={mediaItem.id}>
            <div
              className="media-icon cursor-pointer"
              onClick={() => handleIconClick(mediaItem)}
              title={mediaItem.type === 'link' ? mediaItem.url : ''}
            >
              {mediaItem.type === 'link' ? (
                <LinkIcon
                  style={{
                    fontSize: 30,
                    color: 'black',
                  }}
                  className={`media-icon__${mediaItem.type}`}
                />
              ) : (
                <Icon
                  name={mediaItem.icon}
                  size={30}
                  color="black"
                  className={`media-icon__${mediaItem.type}`}
                />
              )}
            </div>
          </SplideSlide>
        ))}
      </Splide>

      <DialogBox
        open={showDocumentModal}
        handleClose={handleCloseDocumentModal}
        className="dialog-box-container"
        content={
          <div className="document-preview-content">
            {selectedDocument && (
              <iframe
                src={selectedDocument.url}
                title="Document Preview"
                width="100%"
                height="100%"
                style={{ border: 'none' }}
              />
            )}
          </div>
        }
      />
    </div>
  );
};

export default MediaIconsCarousel;
