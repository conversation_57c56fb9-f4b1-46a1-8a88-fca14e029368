'use client';
import React, { useContext, useState } from 'react';
import { usePathname } from 'next/navigation';
import Icon from '@/components/UI/AppIcon/AppIcon';
import Image from '@/components/UI/AppImage/AppImage';
import RecipeMenu from '../RecipeMenu';
import Link from 'next/link';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import { Tooltip } from '@mui/material';
import { getRouteFeatures } from '@/services/recipeService';
import './recipecard.scss';

const RecipeCard = ({
  recipe,
  // viewMode = 'grid',
  isSelected = false,
  isPublicPage = false, // New prop to determine if this is a public page
  onBookmarkToggle,
  onDuplicate,
  onDelete,
  onShare,
  onExport,
  onEdit,
  onView,
}) => {
  const { authState } = useContext(AuthContext);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const currency = getCurrencySymbol(authState?.currency_details);
  const pathname = usePathname();

  // Get route-based features and permissions
  const routeFeatures = getRouteFeatures(pathname);

  // Use route features to determine visibility (fallback to isPublicPage prop for backward compatibility)
  const showPrice = !routeFeatures.isPublicRoute && !isPublicPage;
  const showRecipeStatus = !routeFeatures.isPublicRoute && !isPublicPage;
  const showPrivateFeatures = !routeFeatures.isPublicRoute && !isPublicPage;

  // Handle recipe actions with optional chaining
  const handleDuplicate = () => {
    onDuplicate?.(recipe);
  };

  const handleDelete = () => {
    onDelete?.(recipe);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: recipe?.recipe_title || 'Recipe',
        text: recipe?.recipe_description || '',
        url: window.location.origin + `/recipe/${recipe?.id}`,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(
        window.location.origin + `/recipe/${recipe?.id}`
      );
      alert('Recipe link copied to clipboard!');
    }
    onShare?.(recipe);
  };

  const handleExport = () => {
    // Create a simple text export of the recipe
    const recipeText = `
${recipe?.recipe_title || 'Untitled Recipe'}
${'='.repeat((recipe?.recipe_title || 'Untitled Recipe').length)}

Description: ${recipe?.recipe_description || 'N/A'}
Difficulty: ${recipe?.recipe_complexity_level || 'Not specified'}
Total Cost: ${currency}${recipe?.total_cost?.toFixed(2) || '0.00'}
Cost per Portion: ${currency}${recipe?.cost_per_portion?.toFixed(2) || '0.00'}
Total Portions: ${recipe?.total_portions || 'Not specified'}

Categories: ${recipe?.categories?.join(', ') || 'None'}
Allergens: ${recipe?.allergens?.join(', ') || 'None'}
Status: ${recipe?.recipe_status || 'Unknown'}
    `;

    const blob = new Blob([recipeText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${(recipe?.recipe_title || 'recipe').replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    a.click();
    URL.revokeObjectURL(url);

    onExport?.(recipe);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'low':
        return 'recipe-card__difficulty--low';
      case 'medium':
        return 'recipe-card__difficulty--medium';
      case 'hard':
        return 'recipe-card__difficulty--hard';
      default:
        return 'recipe-card__difficulty--default';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'publish':
        return 'recipe-card__status--published';
      case 'draft':
        return 'recipe-card__status--draft';
      default:
        return 'recipe-card__status--default';
    }
  };

  // if (viewMode === 'list') {
  //   return (
  //     <div
  //       className={`recipe-card recipe-card--list ${
  //         isSelected ? 'recipe-card--selected' : ''
  //       }`}
  //     >
  //       <div className="recipe-card__list-content">
  //         {/* Selection Checkbox */}
  //         <button
  //           onClick={() => onRecipeSelect?.(recipe?.id)}
  //           className={`recipe-card__checkbox ${
  //             isSelected ? 'recipe-card__checkbox--selected' : ''
  //           }`}
  //         >
  //           {isSelected && <Icon name="Check" size={12} />}
  //         </button>

  //         {/* Recipe Image */}
  //         <div className="recipe-card__list-image">
  //           <Image
  //             src={recipe?.recipe_placeholder?.item_link}
  //             alt={recipe?.recipe_title || 'Recipe'}
  //             className="recipe-card__image"
  //           />
  //         </div>

  //         {/* Recipe Info */}
  //         <div className="recipe-card__list-info">
  //           <div className="recipe-card__list-header">
  //             <h3 className="recipe-card__title">
  //               {recipe?.recipe_title || 'Untitled Recipe'}
  //             </h3>
  //             <div className="recipe-card__list-badges">
  //               <span
  //                 className={`recipe-card__status ${getStatusColor(recipe?.recipe_status)}`}
  //               >
  //                 {recipe?.recipe_status || 'draft'}
  //               </span>
  //             </div>
  //           </div>

  //           <p className="recipe-card__description">
  //             {recipe?.recipe_description || ''}
  //           </p>

  //           <div className="recipe-card__meta">
  //             <span className="recipe-card__meta-item">
  //               <Icon name="DollarSign" size={12} />
  //               <span>
  //                 {currency}
  //                 {recipe?.total_cost?.toFixed(2) || '0.00'}
  //               </span>
  //             </span>
  //             <span className="recipe-card__meta-item">
  //               <Icon name="Users" size={12} />
  //               <span>{recipe?.total_portions || 0} portions</span>
  //             </span>
  //             <span className="recipe-card__meta-item">
  //               <Icon name="Eye" size={12} />
  //               <span>{recipe?.recipe_impression}</span>
  //             </span>
  //             <span
  //               className={`recipe-card__difficulty ${getDifficultyColor(recipe?.recipe_complexity_level)}`}
  //             >
  //               {recipe?.recipe_complexity_level || 'N/A'}
  //             </span>
  //           </div>

  //           {/* Allergen Icons */}
  //           {recipe?.allergens?.length > 0 && (
  //             <div className="recipe-card__allergens">
  //               {recipe?.allergens?.map((allergen, index) => (
  //                 <span key={index} className="recipe-card__allergen">
  //                   <Image
  //                     src={allergen?.icon}
  //                     alt={allergen?.title}
  //                     className="recipe-card__allergen-icon"
  //                   />
  //                 </span>
  //               ))}
  //             </div>
  //           )}
  //         </div>

  //         {/* Actions */}
  //         <div className="recipe-card__list-actions">
  //           <button
  //             onClick={() => onBookmarkToggle?.(recipe?.id)}
  //             className={`recipe-card__action-btn ${
  //               recipe?.is_bookmarked === 1
  //                 ? 'recipe-card__action-btn--bookmarked'
  //                 : ''
  //             }`}
  //           >
  //             <Icon
  //               name={recipe?.is_bookmarked === 1 ? 'Bookmark' : 'BookmarkPlus'}
  //               size={16}
  //             />
  //           </button>

  //           <Link
  //             href={`/recipes/recipe-update/${recipe?.slug || recipe?.id}`}
  //             className="recipe-card__action-btn"
  //           >
  //             <Icon name="Edit3" size={16} />
  //           </Link>

  //           <RecipeMenu
  //             recipe={recipe}
  //             onDuplicate={handleDuplicate}
  //             onDelete={() => setDeleteDialogOpen(true)}
  //             onBookmark={() => onBookmarkToggle?.(recipe?.id)}
  //             onShare={handleShare}
  //             onExport={handleExport}
  //             onEdit={onEdit}
  //             onView={onView}
  //             position="right"
  //           />
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  // Grid view
  return (
    <div
      className={`recipe-card recipe-card--grid ${
        isSelected ? 'recipe-card--selected' : ''
      }`}
    >
      {/* Recipe Image */}
      <div className="recipe-card__image-container">
        <Image
          src={recipe?.recipe_placeholder?.item_link}
          alt={recipe?.recipe_title || 'Recipe'}
          className="recipe-card__image"
        />

        {/* Status Badge - Hidden on public pages */}
        {showRecipeStatus && (
          <div className="recipe-card__status-badge">
            <span
              className={`recipe-card__status ${getStatusColor(recipe?.recipe_status)}`}
            >
              {recipe?.recipe_status || 'draft'}
            </span>
          </div>
        )}

        {/* Bookmark Button - Hidden on public pages */}
        {showPrivateFeatures && (
          <button
            onClick={() => onBookmarkToggle?.(recipe?.id)}
            className={`recipe-card__bookmark-btn ${
              recipe?.is_bookmarked === 1
                ? 'recipe-card__bookmark-btn--active'
                : ''
            }`}
          >
            <Icon
              name={recipe?.is_bookmarked === 1 ? 'Bookmark' : 'BookmarkPlus'}
              size={16}
            />
          </button>
        )}

        {/* Allergen Icons */}
        {recipe?.allergens?.length > 0 && (
          <div className="recipe-card__allergens-overlay">
            {recipe.allergens.slice(0, 3).map((allergen, index) => (
              <Tooltip
                title={
                  <p className="sub-title-text">Contains {allergen?.title}</p>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <span
                  key={index}
                  className="recipe-card__allergen recipe-card__allergen--overlay"
                >
                  <Image
                    src={allergen?.icon}
                    alt={allergen?.title}
                    className="recipe-card__allergen-icon"
                  />
                </span>
              </Tooltip>
            ))}
            {recipe?.allergens?.length > 3 && (
              <span className="recipe-card__tag">
                +{recipe?.allergens?.length - 3}
              </span>
            )}
          </div>
        )}
      </div>

      {/* Recipe Content */}
      <div className="recipe-card__content">
        <div className="recipe-card__header">
          <h3 className="recipe-card__title">
            {recipe?.recipe_title || 'Untitled Recipe'}
          </h3>
          {recipe?.recipe_complexity_level && (
            <span
              className={`recipe-card__difficulty ${getDifficultyColor(recipe?.recipe_complexity_level)}`}
            >
              {recipe?.recipe_complexity_level || 'N/A'}
            </span>
          )}
        </div>

        <p className="recipe-card__description">
          {recipe?.recipe_description || ''}
        </p>

        {/* Recipe Meta */}
        <div className="recipe-card__meta-container">
          <div className="recipe-card__meta">
            <span className="recipe-card__meta-item">
              <Icon name="Clock" size={12} />
              <span>
                {recipe?.recipe_cook_time
                  ? `${Math.round(recipe.recipe_cook_time / 60)} min`
                  : 'N/A'}
              </span>
            </span>
            {recipe?.recipe_impression ? (
              <span className="recipe-card__meta-item">
                <Icon name="Eye" size={12} />
                <span>{recipe?.recipe_impression || 0}</span>
              </span>
            ) : null}
          </div>
          {/* Price - Hidden on public pages */}
          {showPrice && (
            <span className="recipe-card__price">
              {currency}
              {recipe?.total_cost?.toFixed(2) || '0.00'}
            </span>
          )}
        </div>

        {/* Categories */}
        <div className="recipe-card__tags">
          {recipe?.categories?.slice(0, 3).map((category, index) => (
            <span key={index} className="recipe-card__tag">
              {category}
            </span>
          ))}
          {recipe?.categories?.length > 3 && (
            <span className="recipe-card__tag">
              +{recipe.categories.length - 3}
            </span>
          )}
        </div>

        {/* Actions */}
        <div className="recipe-card__actions">
          {/* Visibility Icons - Hidden on public pages */}
          {showPrivateFeatures && (
            <div className="recipe-card__likes">
              {recipe?.has_recipe_private_visibility === 1 && (
                <Tooltip
                  title={<p className="sub-title-text">Private</p>}
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span>
                    <Icon name="Lock" size={14} />
                  </span>
                </Tooltip>
              )}
              {recipe?.has_recipe_public_visibility === 1 && (
                <Tooltip
                  title={<p className="sub-title-text">Public</p>}
                  arrow
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                >
                  <span>
                    <Icon name="Unlock" size={14} />
                  </span>
                </Tooltip>
              )}
            </div>
          )}

          <div className="recipe-card__action-buttons">
            {/* Edit Link - Hidden on public pages */}
            {showPrivateFeatures && (
              <Link
                href={`/recipes/recipe-update/${recipe?.slug || recipe?.id}`}
                className="recipe-card__action-btn"
              >
                <Icon name="Edit3" size={14} />
              </Link>
            )}
            {/* Recipe Menu - Hidden on public pages */}
            {showPrivateFeatures && (
              <RecipeMenu
                recipe={recipe}
                onDuplicate={handleDuplicate}
                onDelete={() => setDeleteDialogOpen(true)}
                onBookmark={() => onBookmarkToggle?.(recipe?.id)}
                onShare={handleShare}
                onExport={handleExport}
                onEdit={onEdit}
                onView={onView}
                position="right"
              />
            )}
          </div>
        </div>

        {/* Last Updated */}
        <div className="recipe-card__updated">
          Updated{' '}
          {new Date(recipe?.updated_at).toLocaleDateString() || 'Unknown'}
        </div>
      </div>
      <DialogBox
        open={deleteDialogOpen}
        handleClose={() => setDeleteDialogOpen(false)}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={() => setDeleteDialogOpen(false)}
            handleConfirm={() => handleDelete()}
            text={`Are you sure you want to delete "${recipe?.recipe_title || 'this recipe'}"?`}
          />
        }
      />
    </div>
  );
};

export default RecipeCard;
