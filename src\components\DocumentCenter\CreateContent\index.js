'use client';

import React, { useState, useRef, useEffect, useContext } from 'react';
import {
  Box,
  Divider,
  Typography,
  Checkbox,
  FormControlLabel,
  FormGroup,
} from '@mui/material';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { Formik, Form } from 'formik';
import { useRouter, useSearchParams } from 'next/navigation';
import * as Yup from 'yup';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomEditor from '@/components/UI/CustomEditor';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useDropzone } from 'react-dropzone';
import CollectionsIcon from '@mui/icons-material/Collections';
import PermMediaIcon from '@mui/icons-material/PermMedia';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import CancelIcon from '@mui/icons-material/Cancel';
import AddIcon from '@mui/icons-material/Add';
import ImageIcon from '@mui/icons-material/Image';
import Multiselect from '@/components/UI/CustomMultiSelect';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import LibraryMusicIcon from '@mui/icons-material/LibraryMusic';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import HeaderImage from '@/components/UI/ImageSecurity';
import _ from 'lodash';
import './createcategory.scss';

export default function CreateContent() {
  const { setfolderdata } = useContext(AuthContext);
  const router = useRouter();
  const formikRef = useRef(null);
  const searchParams = useSearchParams();
  const redirectId = searchParams.get('id');
  const UpdateId = searchParams.get('UpdateId');
  const fromDetails = searchParams.get('fromDetails');
  const CategoryType = searchParams.get('type');
  const [departmentList, setDepartmentList] = useState([]);
  const [branchList, setBranchList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [editioContent, setEditorContent] = useState('');
  const [exlink, setExlink] = useState([{ link: '', ltype: '' }]);
  const [acceptedMedia, setAcceptedMedia] = useState([]);
  const [acceptCatFile, setAcceptedCatFiles] = useState([]);
  // const [setSubmit] = useState(false);
  const [error, setError] = useState('');
  const [emptymedia, setEmptymedia] = useState(false);
  const [CategoryDetails, setCategoryDetails] = useState('');
  const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;
  const youtubePattern =
    /^(https?:\/\/)?(www\.)?(youtube\.com\/(watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})$/;

  const Linktype = [
    { label: 'IMAGE', value: 'image' },
    { label: 'YOUTUBE VIDEO', value: 'youtube' },
    { label: 'VIDEO', value: 'video' },
    { label: 'AUDIO', value: 'audio' },
    { label: 'PDF', value: 'pdf' },
  ];
  const {
    getRootProps: getRootPropsSingle,
    getInputProps: getInputPropsSingle,
  } = useDropzone({
    accept: {
      'image/*': [],
    },
    multiple: false,
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload image only.');
        setAcceptedCatFiles([]);
        // setSubmit(true);
        formikRef.current.setFieldValue('filename', '');
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        setError(null);
        setAcceptedCatFiles(acceptedFile);
        formikRef.current.setFieldValue('filename', acceptedFile?.[0]);
      }
    },
  });
  const {
    getRootProps: getRootPropsMultiple,
    getInputProps: getInputPropsMultiplegetRootProps,
  } = useDropzone({
    accept: {
      'application/pdf': [],
      'video/*': [],
      'audio/mpeg': [],
      'image/*': [],
    },
    multiple: true,
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 200 * 1024 * 1024; // 200MB in bytes
      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      var totalFiles = _.concat(acceptedMedia, acceptedFile);
      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload image only.');
        setAcceptedMedia([]);
        formikRef.current.setFieldValue('uploadedMedia', []);
        // setSubmit(true);
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 200MB.');
      } else if (totalFiles.length > 11) {
        setApiMessage('error', 'Please upload less than 10 files');
      } else {
        // setSubmit(false);
        setAcceptedMedia(totalFiles);
        formikRef.current.setFieldValue('uploadedMedia', totalFiles);
      }
    },
  });
  // List of Category details
  const getDocumentCategory = async (ID) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_CATEGORY + `${ID}`
      );
      if (status === 200) {
        setLoader(false);
        const branchList = data?.data?.branches?.map((i) => ({
          value: i?.id,
          label: i?.branch_name,
        }));
        const depList = data?.data?.departments?.map((i) => ({
          value: i?.id,
          label: i?.department_name,
        }));
        setCategoryDetails({
          ...data?.data,
          branches: branchList,
          departments: depList,
        });
        data?.data?.category_description &&
          data?.data?.category_description !== 'null' &&
          setEditorContent(data?.data?.category_description);
        if (data?.data?.category_image_url && data?.data?.category_image) {
          const filedata = [];
          filedata.push({
            type: 'image',
            link: data?.data?.category_image_url,
            name: data?.data?.category_image,
            isUpdated: true,
          });
          setAcceptedCatFiles(filedata);
          setEmptymedia(false);
          formikRef.current.setFieldValue(
            'filename',
            data?.data?.category_image
          );
        }
        if (
          data?.data?.categoryItemDetails &&
          data?.data?.categoryItemDetails?.length > 0
        ) {
          const LinkData = data?.data?.categoryItemDetails?.filter(
            (f) => !f?.item_id && f?.document_category_item_link
          );
          const links = LinkData?.map((m) => {
            return {
              link: m?.document_category_item_link,
              ltype: m?.document_category_item_type,
            };
          });
          const MediaData = data?.data?.categoryItemDetails?.filter(
            (f) => f?.item_id
          );
          let filedata = [];
          MediaData &&
            MediaData?.length > 0 &&
            MediaData?.map((m) => {
              filedata.push({
                type: m?.document_category_item_type
                  ? m?.document_category_item_type
                  : 'image',
                link: m?.item_image_url,
                name: m?.item_image_name,
                id: m?.item_id,
                isUpdated: true,
              });
            });
          setAcceptedMedia(filedata);
          setEmptymedia(false);
          links && links?.length > 0 && setExlink(links);
          links &&
            links?.length > 0 &&
            formikRef.current.setFieldValue('exlink', links);
        }
      }
    } catch (error) {
      setLoader(false);
      setCategoryDetails();
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const uploadedmedia = (type, name) => {
    const filename = name;
    if (type.startsWith('image')) {
      return (
        <Box className="image-sec">
          <ImageIcon />
          <Typography className="sub-title-text file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.startsWith('video')) {
      return (
        <Box className="image-sec">
          <VideoLibraryIcon />
          <Typography className="sub-title-text file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.includes('pdf') || type.includes('doc')) {
      return (
        <Box className="image-sec">
          <PictureAsPdfIcon />
          <Typography className="sub-title-text file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.startsWith('audio')) {
      return (
        <Box className="image-sec">
          <LibraryMusicIcon />
          <Typography className="sub-title-text file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    }
  };
  // LIST OF DEPARTMENT
  const getDepartmentList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_DEPARTMENT_LIST +
          `?search=${''}&page=${1}&size=${''}&departmentStatus=active`
      );

      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.department_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setDepartmentList(mergeList);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of branches
  const getBranchList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${''}&page=${1}&size=${''}&branchStatus=active`
      );
      if (status === 200) {
        setLoader(false);
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.data?.map((user) => ({
          label: user?.branch_name,
          value: user?.id,
          color: user?.branch_color,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setBranchList(mergeList);
      }
    } catch {
      setLoader(false);
      setBranchList([]);
    }
  };
  const descriptionValue = (value) => {
    setEditorContent(value);
    formikRef.current.setFieldValue('description', value);
  };
  useEffect(() => {
    if (CategoryDetails?.branches && CategoryDetails?.branches?.length > 0) {
      const isall = branchList?.filter((b) => b?.value === 'all');
      const allbranch = branchList?.filter((b) => b?.value !== 'all');
      if (
        CategoryDetails?.branches?.length === allbranch?.length &&
        isall?.length > 0
      ) {
        const filterall = branchList?.filter((b) => b?.value !== 'all');
        setBranchList(filterall);
      } else if (
        CategoryDetails?.branches?.length !== allbranch?.length &&
        isall?.length === 0
      ) {
        const alloption = [{ label: 'Select all', value: 'all' }];

        let mergeList = _.concat(alloption, branchList);
        setBranchList(mergeList);
      }
    }
  }, [CategoryDetails?.branches?.length]);
  useEffect(() => {
    if (
      CategoryDetails?.departments &&
      CategoryDetails?.departments?.length > 0
    ) {
      const isall = departmentList?.filter((b) => b?.value === 'all');
      const alldepart = departmentList?.filter((b) => b?.value !== 'all');
      if (
        CategoryDetails?.departments?.length === alldepart?.length &&
        isall?.length > 0
      ) {
        const filterall = departmentList?.filter((b) => b?.value !== 'all');
        setDepartmentList(filterall);
      } else if (
        CategoryDetails?.departments?.length !== alldepart?.length &&
        isall?.length === 0
      ) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let mergeList = _.concat(alloption, departmentList);
        setDepartmentList(mergeList);
      }
    }
  }, [CategoryDetails?.departments?.length]);
  useEffect(() => {
    getDepartmentList();
    getBranchList();
    setfolderdata();
  }, []);
  useEffect(() => {
    if (UpdateId) {
      getDocumentCategory(UpdateId);
    }
  }, [UpdateId]);

  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };
  return (
    <>
      <Box>
        <Box className="create-document-page">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer mt4"
              onClick={() => {
                if (fromDetails) {
                  router.push(
                    `/document-staff/${redirectId}?details=true&subId=${UpdateId}`
                  );
                } else {
                  router.push(`/document-staff/${redirectId}`);
                }
              }}
            />
            <Typography className="title-sm fw600 pr8">
              {UpdateId ? 'Update File' : 'Create File'}
            </Typography>
          </Box>
          <Divider className="mb16 mt16" />

          <Formik
            innerRef={formikRef}
            initialValues={{
              categoryname:
                UpdateId && CategoryDetails?.category_name
                  ? CategoryDetails?.category_name
                  : '',
              description:
                UpdateId &&
                CategoryDetails?.category_description &&
                CategoryDetails?.category_description !== 'null'
                  ? CategoryDetails?.category_description
                  : '',
              exlink:
                UpdateId && exlink && exlink?.length > 0
                  ? exlink
                  : [{ link: '', ltype: '' }],
              depname:
                UpdateId && CategoryDetails?.departments
                  ? CategoryDetails?.departments
                  : [],
              branchname:
                UpdateId && CategoryDetails?.branches
                  ? CategoryDetails?.branches
                  : [],
              status:
                UpdateId && CategoryDetails?.category_status
                  ? CategoryDetails?.category_status
                  : 'active',
              uploadedMedia:
                UpdateId &&
                CategoryDetails &&
                acceptedMedia &&
                acceptedMedia?.length > 0
                  ? acceptedMedia
                  : [],
              isNotify: false,
              tracker:
                UpdateId && CategoryDetails?.has_agreement_required
                  ? true
                  : false,
              videocontrol:
                UpdateId && CategoryDetails?.has_video_control ? true : false,
              filename:
                UpdateId && CategoryDetails?.category_image
                  ? CategoryDetails?.category_image
                  : '',
            }}
            enableReinitialize={true}
            validationSchema={Yup.object()
              .shape({
                categoryname: Yup.string()
                  .trim()
                  .required('This field is required'),

                depname: Yup.array().min(
                  1,
                  'At least one department must be selected'
                ),
                branchname: Yup.array().min(
                  1,
                  'At least one branch must be selected'
                ),
                filename: Yup.string()
                  .trim()
                  .required('This field is required'),
                status: Yup.string().trim().required('This field is required'),
                notify_msg: Yup.string().when('isNotify', {
                  is: true,
                  then: (schema) =>
                    schema.trim().required('This field is required'),
                }),
              })
              .test(
                'exlink-or-uploadedMedia',
                'Either upload media or provide an external link with type',
                function (values) {
                  const { exlink, uploadedMedia } = values;
                  const hasValidExlink =
                    exlink && exlink.every((link) => link?.link && link?.ltype);
                  const hasValidUploadedMedia =
                    uploadedMedia && uploadedMedia.length > 0;
                  // const allLinksValid = exlink.every(
                  //   (item) => item.link && urlPattern.test(item.link)
                  // );
                  const allLinksValid = exlink.every((item) => {
                    if (item?.ltype === 'youtube') {
                      return youtubePattern.test(item.link);
                    } else {
                      return urlPattern.test(item.link);
                    }
                  });

                  if (!hasValidExlink && !hasValidUploadedMedia) {
                    return this.createError({
                      path: 'exlink', // Assign the error to 'exlink'
                      message:
                        'Either upload media or provide an external link with type',
                    });
                  }
                  if (!allLinksValid && hasValidExlink) {
                    return this.createError({
                      path: 'exlink', // Assign the error to 'exlink'
                      message: 'Enter a valid URL',
                    });
                  }

                  return true;
                }
              )}
            onSubmit={async (requestData) => {
              setLoader(true);
              const body = new FormData();
              const bdata = requestData?.branchname?.map((m) => m?.value);
              const ddata = requestData?.depname?.map((m) => m?.value);
              requestData?.categoryname &&
                body.append('category_name', requestData?.categoryname);

              body.append(
                'category_description',
                requestData?.description ? requestData?.description : null
              );
              requestData?.branchname &&
                bdata &&
                bdata?.length > 0 &&
                bdata?.map((r, index) => {
                  body.append(`branch_ids[${index}]`, r);
                });
              requestData?.depname &&
                ddata &&
                ddata?.length > 0 &&
                ddata?.map((r, index) => {
                  body.append(`department_ids[${index}]`, r);
                });
              body.append(
                'category_status',
                requestData?.status ? requestData?.status : ''
              );
              acceptCatFile &&
              acceptCatFile?.[0] &&
              acceptCatFile?.[0]?.isUpdated
                ? body.append('category_image', '') //acceptedMedia?.[0]?.name
                : body.append('category_image', acceptCatFile?.[0]);

              requestData?.uploadedMedia &&
                requestData?.uploadedMedia?.length > 0 &&
                requestData?.uploadedMedia?.map((media, index) => {
                  if (media?.id) {
                    body.append(`existing_item[${index}]`, media?.id);
                  } else {
                    body.append(`item_list`, media);
                  }
                });
              // requestData?.uploadedMedia && [${index}]
              //   requestData?.uploadedMedia?.length > 0 &&
              //   body.append(`item_list`, requestData?.uploadedMedia);

              requestData?.exlink &&
                requestData?.exlink?.length > 0 &&
                requestData?.exlink?.map((media, index) => {
                  if (media?.link && media?.ltype) {
                    body.append(
                      `external_links[${index}][document_category_item_link]`,
                      media?.link
                    );
                    body.append(
                      `external_links[${index}][document_category_item_type]`,
                      media?.ltype
                    );
                  }
                });
              const hasValidExlink =
                exlink && exlink.some((link) => link?.link && link?.ltype);
              hasValidExlink &&
                requestData?.exlink &&
                requestData?.exlink?.length > 0 &&
                body.append('is_external_link', true);
              body.append('is_notify', requestData?.isNotify);
              requestData?.isNotify &&
                requestData?.notify_msg &&
                body.append('notification_content', requestData?.notify_msg);
              body.append('has_agreement_required', requestData?.tracker);
              body.append('has_video_control', requestData?.videocontrol);
              body.append('category_type', 'file');
              CategoryType && body.append('category_use', CategoryType);
              redirectId !== 'all' && body.append('parent_id', redirectId);
              const config = {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              };

              const ApiUrl = UpdateId
                ? URLS.UPDATE_CAT_DOCUMENT + UpdateId
                : URLS.CREATE_CAT_DOCUMENT;
              const method = UpdateId ? 'put' : 'post';
              try {
                const { status, data } = await axiosInstance[method](
                  ApiUrl,
                  body,
                  config
                );
                if (status === 200 || status === 201) {
                  if (data?.status) {
                    setApiMessage('success', data?.message);
                    setTimeout(() => {
                      if (fromDetails) {
                        router.push(
                          `/document-staff/${redirectId}?details=true&subId=${UpdateId}`
                        );
                      } else {
                        router.push(`/document-staff/${redirectId}`);
                      }
                    }, 2000);
                  } else {
                    setApiMessage('error', data?.message);
                  }
                  setLoader(false);
                }
              } catch (error) {
                setLoader(false);
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              setFieldValue,
              handleSubmit,
              handleChange,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box className="create-content-grid">
                  <Box className="content-left">
                    <Box className="display-grid pb8">
                      <Box>
                        <CustomTextField
                          fullWidth
                          id="categoryname"
                          name="categoryname"
                          value={values?.categoryname}
                          label="File Title"
                          required
                          placeholder="File Title"
                          error={Boolean(
                            touched.categoryname && errors.categoryname
                          )}
                          helperText={
                            touched.categoryname && errors.categoryname
                          }
                          onBlur={handleBlur}
                          onChange={(e) => {
                            handleChange(e);
                            // setSubmit(false);
                          }}
                        />
                      </Box>
                    </Box>

                    <Box>
                      <Typography className="field-label pt8 pb4" id="filename">
                        Upload Placeholder Image
                        <span className="required"> *</span>
                      </Typography>
                      {acceptCatFile && acceptCatFile?.length === 1 ? (
                        <Box className="logo-section">
                          <HeaderImage
                            imageUrl={
                              acceptCatFile &&
                              acceptCatFile?.length === 1 &&
                              acceptCatFile?.[0]?.isUpdated === true
                                ? acceptCatFile[0]?.link
                                : acceptCatFile?.[0]?.type?.includes('image')
                                  ? URL.createObjectURL(acceptCatFile[0])
                                  : null
                            }
                            alt="not found"
                            className="media"
                            type="lazyload"
                          />
                          <CancelIcon
                            className="cancel-icon cursor-pointer"
                            onClick={() => {
                              setAcceptedCatFiles();
                              setEmptymedia(true);
                              formikRef.current.setFieldValue('filename', '');
                            }}
                          />
                        </Box>
                      ) : (
                        <Box className="upload-sec cursor-pointer text-align upload-category-sec">
                          <Box
                            {...getRootPropsSingle({ className: 'dropzone' })}
                            className="upload-area"
                          >
                            <CollectionsIcon />
                            <input {...getInputPropsSingle()} />
                            <Typography className="title-text upload-text">
                              Drop your image here
                            </Typography>
                          </Box>
                        </Box>
                      )}
                      {/* {acceptCatFile &&
                      acceptCatFile?.length === 1 &&
                      acceptCatFile?.[0]?.isUpdated !== true ? (
                        <Box className="uploaded-media-sec">
                          {uploadedmedia(
                            acceptCatFile?.[0]?.type,
                            acceptCatFile?.[0]?.name
                          )}
                          <DeleteOutlineIcon
                            className="cursor-pointer"
                            onClick={() => {
                              setAcceptedCatFiles();
                              setEmptymedia(true);
                              formikRef.current.setFieldValue('filename', '');
                            }}
                          />
                        </Box>
                      ) : acceptCatFile && acceptCatFile?.length === 1 ? (
                        <Box className="uploaded-media-sec">
                          {uploadedmedia(
                            acceptCatFile?.[0]?.type,
                            acceptCatFile?.[0]?.name
                          )}
                          <DeleteOutlineIcon
                            className="cursor-pointer"
                            onClick={() => {
                              setAcceptedCatFiles();
                              setEmptymedia(true);
                              formikRef.current.setFieldValue('filename', '');
                            }}
                          />
                        </Box>
                      ) : (
                        <></>
                      )} */}
                      {error && (
                        <Typography
                          variant="body2"
                          color="error"
                          className="other-field-error-text"
                        >
                          {error}
                        </Typography>
                      )}{' '}
                      {emptymedia &&
                        (!acceptCatFile ||
                          (acceptCatFile && acceptCatFile?.length === 0)) && (
                          <Typography
                            variant="body2"
                            color="error"
                            className="other-field-error-text"
                          >
                            This field is required
                          </Typography>
                        )}
                    </Box>
                    <Typography className="field-label pt8 pb4" id="exlink">
                      Upload Media
                      <span className="required"> *</span>
                    </Typography>
                    {/* {exlink && exlink.some((s) => s?.link) ? (
                  <Box className="upload-sec cursor-pointer w100 text-align">
                    <Box>
                      <PermMediaIcon />
                      <Typography className="title-text upload-text">
                        Drop your media here
                      </Typography>
                    </Box>
                  </Box>
                ) : ( */}
                    <Box className="upload-sec cursor-pointer text-align upload-category-sec">
                      <Box
                        {...getRootPropsMultiple({ className: 'dropzone' })}
                        className="upload-area"
                      >
                        <PermMediaIcon />
                        <input {...getInputPropsMultiplegetRootProps()} />
                        <Typography className="title-text upload-text">
                          Drop your media here
                        </Typography>
                      </Box>
                    </Box>
                    {/* )} */}
                    {acceptedMedia && acceptedMedia?.length > 0 ? (
                      <Box className="">
                        {acceptedMedia?.map((item, i) => (
                          <Box className="uploaded-media-sec">
                            {uploadedmedia(item?.type, item?.name)}
                            <DeleteOutlineIcon
                              className="cursor-pointer"
                              onClick={() => {
                                const files = acceptedMedia?.filter(
                                  (id, index) => i !== index
                                );
                                setAcceptedMedia(files);
                                formikRef.current.setFieldValue(
                                  'uploadedMedia',
                                  files
                                );
                              }}
                            />
                          </Box>
                        ))}
                      </Box>
                    ) : (
                      <></>
                    )}
                    {/* {((touched.uploadedMedia && errors.uploadedMedia) ||
                  (!exlink &&
                    exlink?.length > 0 &&
                    acceptedFiles?.length === 0 &&
                    submit)) && (
                  <Typography
                    variant="body2"
                    color="error"
                    className="other-field-error-text "
                  >
                    This field is required
                  </Typography>
                )} */}
                    <Typography className="sub-title-text text-align fw600 pt8">
                      OR
                    </Typography>
                    <Box className="text-align-end pb8">
                      <CustomButton
                        variant="contained"
                        title={'Add link'}
                        startIcon={<AddIcon />}
                        onClick={() => {
                          const newExlink = [
                            ...exlink,
                            { link: '', ltype: '' },
                          ];
                          setExlink(newExlink);
                          setFieldValue('exlink', newExlink);
                        }}
                      />
                    </Box>
                    <Box
                      className={
                        touched.exlink && errors.exlink
                          ? 'pb8 external-link-section error'
                          : 'external-link-section'
                      }
                    >
                      {values?.exlink?.map((elink, index) => (
                        <Box
                          className={
                            touched.exlink &&
                            touched.exlink[index] &&
                            errors.exlink &&
                            errors.exlink[index]?.link &&
                            index === values.exlink?.length - 1
                              ? 'external-link-sec align-start'
                              : touched.exlink &&
                                  touched.exlink[index] &&
                                  errors.exlink &&
                                  errors.exlink[index]?.link
                                ? 'pb8 external-link-sec align-start'
                                : 'pb8 external-link-sec'
                          }
                          key={index}
                        >
                          {' '}
                          <CustomTextField
                            fullWidth
                            id={`exlink${index}.link`}
                            name={`exlink${index}.link`}
                            value={elink?.link}
                            label={`External Link ${index + 1} *`}
                            placeholder="Enter External Link"
                            error={Boolean(
                              !elink?.link &&
                                touched.exlink &&
                                touched.exlink[index] &&
                                errors.exlink &&
                                errors.exlink[index]?.link
                            )}
                            helperText={
                              !elink?.link &&
                              touched.exlink &&
                              touched.exlink[index] &&
                              errors.exlink &&
                              errors.exlink[index]?.link
                            }
                            onBlur={handleBlur}
                            onChange={(e) => {
                              const newExlink = [...exlink];

                              newExlink[index].link = e.target.value.toString();
                              setExlink(newExlink);
                              setFieldValue('exlink', newExlink);
                              // setSubmit(false);
                            }}
                          />
                          <Box className="">
                            {' '}
                            <CustomSelect
                              placeholder="Link Type"
                              options={Linktype}
                              name={`exlink${index}.ltype`}
                              value={
                                Linktype?.find((opt) => {
                                  return opt?.value === elink?.ltype;
                                }) || ''
                              }
                              error={Boolean(
                                !elink?.ltype &&
                                  touched.exlink &&
                                  touched.exlink[index] &&
                                  errors.exlink &&
                                  errors.exlink[index]?.ltype
                              )}
                              // helperText={
                              //   !elink?.ltype &&
                              //   touched.exlink &&
                              //   touched.exlink[index] &&
                              //   errors.exlink &&
                              //   errors.exlink[index]?.ltype
                              // }
                              onChange={(e) => {
                                const newExlink = [...exlink];

                                newExlink[index].ltype = e?.value;
                                setExlink(newExlink);
                                setFieldValue('exlink', newExlink);
                                // setSubmit(false);
                              }}
                              label={<span>Link Type </span>}
                            />
                          </Box>
                          <CancelIcon
                            className={`cancel-icon cursor-pointer ${
                              touched.exlink &&
                              touched.exlink[index] &&
                              errors.exlink &&
                              errors.exlink[index]?.link &&
                              'mt24'
                            } `}
                            disabled={values.exlink.length === 1}
                            onClick={() => {
                              if (exlink?.length !== 1) {
                                // const newExlink = [...exlink];
                                const newExlink = exlink?.filter(
                                  (_, i) => i !== index
                                );
                                setExlink(newExlink);
                                setFieldValue('exlink', newExlink);
                                // setFieldValue(
                                //   'exlink',
                                //   values.exlink.filter((_, i) => i !== index)
                                // );
                              } else {
                                setExlink([{ link: '', ltype: '' }]);
                                setFieldValue('exlink', [
                                  { link: '', ltype: '' },
                                ]);
                              }
                            }}
                          />
                        </Box>
                      ))}
                      {touched.exlink && errors.exlink && (
                        <Typography
                          variant="body2"
                          color="error"
                          className="other-field-error-text "
                        >
                          {errors.exlink
                            ? errors.exlink
                            : 'This field is required'}
                        </Typography>
                      )}
                    </Box>
                    <Box className="display-grid pb8 pt8">
                      <Box>
                        <Multiselect
                          placeholder="Branch name"
                          options={branchList}
                          name="branchname"
                          showDot={true}
                          isOptionWithColor={true}
                          value={values?.branchname}
                          error={touched.branchname && errors.branchname}
                          helperText={touched.branchname && errors.branchname}
                          onChange={(e) => {
                            const filterValue = e?.find(
                              (f) => f?.value === 'all'
                            );
                            const branchValue = branchList?.filter(
                              (f) => f?.value !== 'all'
                            );
                            if (
                              filterValue ||
                              e?.length === branchValue?.length
                            ) {
                              const filterall = branchList?.filter(
                                (b) => b?.value !== 'all'
                              );
                              setBranchList(filterall);
                              const selectedValue = filterValue ? filterall : e;
                              setFieldValue('branchname', selectedValue);
                            }
                            if (!filterValue) {
                              const isAll = branchList?.find(
                                (f) => f?.value === 'all'
                              );
                              if (!isAll) {
                                const alloption = [
                                  { label: 'Select all', value: 'all' },
                                ];
                                let mergeList = _.concat(alloption, branchList);
                                setBranchList(mergeList);
                              }
                              setFieldValue('branchname', e);
                            }
                          }}
                          label={<span>Branch name</span>}
                          required
                        />
                      </Box>
                      <Box>
                        <Multiselect
                          placeholder="Department name"
                          options={departmentList}
                          name="depname"
                          value={values?.depname}
                          error={touched.depname && errors.depname}
                          helperText={touched.depname && errors.depname}
                          onChange={(e) => {
                            const filterValue = e?.find(
                              (f) => f?.value === 'all'
                            );
                            const depValue = departmentList?.filter(
                              (f) => f?.value !== 'all'
                            );
                            if (filterValue || e?.length === depValue?.length) {
                              const filterall = departmentList?.filter(
                                (b) => b?.value !== 'all'
                              );
                              setDepartmentList(filterall);
                              const selectedValue = filterValue ? filterall : e;
                              setFieldValue('depname', selectedValue);
                            }
                            if (!filterValue) {
                              const isAll = departmentList?.find(
                                (f) => f?.value === 'all'
                              );
                              if (!isAll) {
                                const alloption = [
                                  { label: 'Select all', value: 'all' },
                                ];
                                let mergeList = _.concat(
                                  alloption,
                                  departmentList
                                );
                                setDepartmentList(mergeList);
                              }
                              setFieldValue('depname', e);
                            }
                          }}
                          label={<span>Department name</span>}
                          required
                        />
                      </Box>
                      <Box>
                        <CustomSelect
                          placeholder="Status"
                          options={identifiers?.STATUS}
                          name="status"
                          error={touched.status && errors.status}
                          helperText={touched.status && errors.status}
                          value={
                            identifiers?.STATUS?.find((opt) => {
                              return opt?.value === values?.status;
                            }) || ''
                          }
                          onChange={(e) => {
                            setFieldValue('status', e?.value);
                            // setSubmit(false);
                          }}
                          label={<span>Status</span>}
                          required
                        />
                      </Box>
                    </Box>
                  </Box>
                  <Box>
                    <Box className="">
                      <Typography className="field-label">
                        Description
                      </Typography>
                      <CustomEditor
                        content={editioContent}
                        setContent={descriptionValue}
                        height="400px"
                      />
                    </Box>
                    <FormGroup className="form-checkbox">
                      <FormControlLabel
                        control={
                          <Checkbox
                            className="check-box "
                            icon={
                              <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                            }
                            checkedIcon={
                              <CheckBoxIcon className="check-icon" />
                            }
                            // onChange={handleChange}
                            disableRipple
                            // disabled={isUpdate}
                          />
                        }
                        name="tracker"
                        className="check-box-form form-row max-content sub-title-text"
                        // name="checkedRolesIds"
                        checked={values?.tracker}
                        onChange={() => {
                          setFieldValue('tracker', !values?.tracker);
                        }}
                        label="Confirmation for tracker"
                      />
                    </FormGroup>
                    <FormGroup className="form-checkbox">
                      <FormControlLabel
                        control={
                          <Checkbox
                            className="check-box "
                            icon={
                              <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                            }
                            checkedIcon={
                              <CheckBoxIcon className="check-icon" />
                            }
                            // onChange={handleChange}
                            disableRipple
                            // disabled={isUpdate}
                          />
                        }
                        name="videocontrol"
                        className="check-box-form form-row max-content sub-title-text"
                        // name="checkedRolesIds"
                        checked={values?.videocontrol}
                        onChange={() => {
                          setFieldValue('videocontrol', !values?.videocontrol);
                        }}
                        label="Video control"
                      />
                    </FormGroup>
                    <Box className="">
                      <FormGroup
                        className={
                          values?.isNotify ? 'form-checkbox' : 'form-checkbox'
                        }
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              className="check-box "
                              icon={
                                <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                              }
                              checkedIcon={
                                <CheckBoxIcon className="check-icon" />
                              }
                              // onChange={handleChange}
                              disableRipple
                              // disabled={isUpdate}
                            />
                          }
                          name="isNotify"
                          className="check-box-form form-row max-content sub-title-text"
                          // name="checkedRolesIds"
                          checked={values?.isNotify}
                          onChange={() => {
                            setFieldValue('isNotify', !values?.isNotify);
                            setFieldValue('notify_msg', '');
                          }}
                          label="Notify"
                        />
                      </FormGroup>
                      {values?.isNotify && (
                        <Box className="">
                          <CustomTextField
                            id="notify_msg"
                            name="notify_msg"
                            multiline
                            rows={2}
                            onChange={(e) => {
                              handleChange(e);
                            }}
                            fullWidth
                            placeholder="Message"
                            value={values?.notify_msg}
                            error={Boolean(
                              touched?.notify_msg && errors?.notify_msg
                            )}
                            helperText={
                              touched?.notify_msg && errors?.notify_msg
                            }
                            className="additional-textfeild"
                            // label="Message"
                            label={<span>Message</span>}
                            required
                            // disabled={isUpdate}
                          />
                        </Box>
                      )}
                    </Box>
                  </Box>
                </Box>

                <Box className="form-actions-btn">
                  {' '}
                  <CustomButton
                    variant="contained"
                    onClick={async () => {
                      if (formikRef?.current) {
                        const errors = await formikRef.current.validateForm();
                        if (Object.keys(errors).length > 0) {
                          scrollToError(errors);
                          formikRef.current.setSubmitting(false);
                        }
                        setEmptymedia(true);
                      }
                    }}
                    type="submit"
                    disabled={loader}
                    title={
                      UpdateId
                        ? `${loader ? 'Updating...' : 'Update File'}`
                        : `${loader ? 'Creating...' : 'Create File'}`
                    }
                  />
                </Box>
              </Form>
            )}
          </Formik>
        </Box>
      </Box>
    </>
  );
}
