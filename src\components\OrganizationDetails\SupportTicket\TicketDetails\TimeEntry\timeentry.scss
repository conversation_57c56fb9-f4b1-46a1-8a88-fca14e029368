    @import '@/styles/variable.scss';

    .time-entry-wrap {
        padding: 20px 0px;
        font-family: "Poppins", sans-serif !important;

        .no-time-entry {
            font-family: "Poppins", sans-serif !important;
        }

        .add-time-wrap {
            font-family: "Poppins", sans-serif !important;
        }
    }

    .show-time-wrap {
        padding: 10px 20px 0px 20px;

        .time-show-wrap {
            padding-bottom: 10px;
            justify-content: space-between;

            .info-wrap {
                line-height: 0px;

                .preview-img {
                    border-radius: 50px;

                    @media(max-width:767px) {
                        height: 35px;
                        width: 35px;
                    }
                }

                .profile-name {
                    font-family: "Poppins", sans-serif !important;

                    @media(max-width:767px) {
                        font-size: 15px;
                    }
                }

                @media(max-width:767px) {
                    gap: 10px;
                }

            }

            .time-wrap {
                font-family: "Poppins", sans-serif !important;
                display: flex;
                align-items: center;
                gap: 50px;

                .edit-time-wrap {
                    .MuiInputBase-input {
                        padding: 4px 10px !important;
                    }

                    .MuiInputBase-root {
                        min-height: 30px;
                        min-width: 94px;
                    }
                }

                .icons-wrap {

                    .save-icon,
                    .edit-icon,
                    .delete-icon {
                        fill: $color-primary;
                        height: 21px;
                        width: 21px;
                        cursor: pointer;
                    }
                }

                @media(max-width:767px) {
                    font-size: 15px;
                    padding-left: 2px;
                }

                @media(max-width:424px) {
                    justify-content: space-between;
                    width: 100%;
                }
            }

            @media(max-width: 575px) {
                flex-direction: column;
                row-gap: 5px;
                justify-content: center;
                align-items: flex-start !important;
                padding-left: 20px;
            }

            @media(max-width: 374px) {
                padding: 10px 0px;

            }
        }

        @media(max-width:767px) {
            padding: 5px 0px;
        }
    }