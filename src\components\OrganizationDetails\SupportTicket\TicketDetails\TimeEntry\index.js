import { EmptyTimeEntryIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Profile from '../../../../../../public/images/Companylogo.png';
import CreateOutlinedIcon from '@mui/icons-material/CreateOutlined';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import SaveIcon from '@mui/icons-material/Save';
import './timeentry.scss';
import { CustomTextField } from '@/components/UI/CommonField';

export default function TimeEntry({ savedTimes: initialTimes }) {
  const [savedTimes, setSavedTimes] = useState([]);
  const [editIndex, setEditIndex] = useState(null);
  const [editedTime, setEditedTime] = useState('');

  useEffect(() => {
    if (initialTimes?.length) {
      setSavedTimes(initialTimes);
    }
  }, [initialTimes]);

  const handleDelete = (index) => {
    const updatedTimes = savedTimes?.filter((_, i) => i !== index);
    setSavedTimes(updatedTimes);
  };

  const handleEdit = (index) => {
    setEditIndex(index);
    setEditedTime(savedTimes[index]);
  };

  const handleSave = (index) => {
    const updatedTimes = [...savedTimes];
    updatedTimes[index] = editedTime;
    setSavedTimes(updatedTimes);
    setEditIndex(null);
    setEditedTime('');
  };

  return (
    <>
      {savedTimes.length === 0 ? (
        <Box className="time-entry-wrap d-flex flex-col align-center justify-center text-align pb32">
          <EmptyTimeEntryIcon />
          <Box className="time-text-wrap text-align">
            <Typography className="no-time-entry" component="p">
              No Time Entry available
            </Typography>
            <Typography className="add-time-wrap" component="p">
              Add a Time Entry to track your time spent on this Ticket.
            </Typography>
          </Box>
        </Box>
      ) : (
        <Box className="show-time-wrap">
          {savedTimes?.map((time, index) => (
            <Box key={index} className="time-show-wrap d-flex align-center">
              <Box className="info-wrap d-flex align-center gap-20">
                <Image
                  src={Profile}
                  alt="profile photo"
                  className="preview-img"
                  width={40}
                  height={40}
                />
                <Typography className="profile-name">
                  Yagnik Siddhapra
                </Typography>
              </Box>

              <Box className="time-wrap">
                {editIndex === index ? (
                  <CustomTextField
                    InputLabelProps={{ shrink: true }}
                    value={editedTime}
                    className="edit-time-wrap"
                    variant="filled"
                    onChange={(e) => setEditedTime(e?.target?.value)}
                    size="small"
                  />
                ) : (
                  time
                )}

                <Box className="icons-wrap d-flex gap-sm">
                  {editIndex === index ? (
                    <SaveIcon
                      className="save-icon"
                      onClick={() => handleSave(index)}
                    />
                  ) : (
                    <CreateOutlinedIcon
                      className="edit-icon"
                      onClick={() => handleEdit(index)}
                    />
                  )}
                  <DeleteOutlineOutlinedIcon
                    className="delete-icon"
                    onClick={() => handleDelete(index)}
                  />
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
      )}
    </>
  );
}
