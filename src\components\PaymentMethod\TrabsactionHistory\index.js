import React, { useEffect, useState } from 'react';
import {
  // Table,
  // TableHead,
  // TableRow,
  // TableCell,
  // TableBody,
  // Checkbox,
  Typography,
  Box,
  Tooltip,
  CircularProgress,
  // Tooltip,
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
// import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
// import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
// import { CustomTextField } from '@/components/UI/CommonField';
import { setApiMessage, getAuthBlobUrl } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import moment from 'moment';
// import noDataImage from '../../../../public/images/Companylogo.png';
import NoDataView from '@/components/UI/NoDataView';
import { DataGrid, gridClasses } from '@mui/x-data-grid';
import CustomButton from '@/components/UI/CustomButton';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import ProductInvoice from '../ProductInvoice';
// import RightDrawer from '@/components/UI/RightDrawer';
import DialogBox from '@/components/UI/Modalbox';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import { useCurrency } from '@/hooks/useCurrency';
import CustomOrgPagination from '@/components/UI/customPagination';
import './transactionhistory.scss';

const TransactionHistory = () => {
  const { getSymbol } = useCurrency();
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortOrder, setSortOrder] = useState({
    key: 'idOrder',
    value: 'ASC',
  }); // Sort order for `invoice`
  const [transactions, setTransactions] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [invoiceData, setInvoiceData] = useState([]);
  const [loading, setLoading] = useState(false);

  const getAllSubscriptionHistory = async (
    search,
    page,
    Rpp,
    Sort,
    isSort = true
  ) => {
    isSort && setLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_ALL_PAYMENT_HISTORY +
          `?search=${search}&page=${page}&${Sort?.key}=${Sort?.value}&size=${
            Rpp ? Rpp : rowsPerPage
          }`
      );

      if (status === 200) {
        const transaction =
          data?.data &&
          data?.data?.map((history) => {
            return {
              ...history,
              invoice:
                history?.subscription_purchased?.subscription_plans
                  ?.subs_plan_name,
              amount:
                history?.subscription_purchased?.subscription_plans
                  ?.subs_plan_cost,
            };
          });
        setTransactions(transaction);
        setTotalCount(data?.count);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const updatedSort = {
      key: key,
      value: newOrder,
    };
    setSortOrder(updatedSort);
    setCurrentPage(1);
    getAllSubscriptionHistory('', 1, rowsPerPage, updatedSort, false);
  };

  const handleInvoicePrieview = async (paymentId) => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS.GET_INVOICE_PREVIEW + `?paymentId=${paymentId}`
      );
      if (status === 200) {
        const responseData = data?.data;
        const invoice = [
          {
            id: 1,
            invoice_details: responseData.invoice_details,
            subscription_details: responseData.subscription_details,
            organization_details: responseData.organization_details,
            payment_details: responseData.payment_details,
            company_details: responseData.company_details,
            bank_details: responseData.bank_details,
            vat_summary: responseData.vat_summary,
          },
        ];
        setInvoiceData(invoice);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const columns = [
    {
      field: 'id',
      headerName: 'Invoice No',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderHeader: () => (
        <Box className="d-flex align-center gap-5">
          <Typography variant="h6 p14 wrap-header-text">
            Invoice
            <span className="amount-text arrow-wrap">
              No
              {sortOrder.key === 'idOrder' && sortOrder.value === 'DESC' ? (
                <ArrowDownward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'idOrder', value: 'ASC' });
                    handleSort('idOrder');
                  }}
                />
              ) : (
                <ArrowUpward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'idOrder', value: 'DESC' });
                    handleSort('idOrder');
                  }}
                />
              )}
            </span>
          </Typography>
        </Box>
      ),
      renderCell: (params) => {
        return (
          <Typography className="text-ellipsis table-body-text">
            {params?.row?.id ? params?.row?.id : '-'}
          </Typography>
        );
      },
    },
    {
      field: 'pay_deduction_date',
      headerName: 'Invoice Date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderHeader: () => (
        <Box className="d-flex align-center gap-5">
          <Typography variant="h6 p14 wrap-header-text">
            Invoice
            <span className="amount-text arrow-wrap">
              Date
              {sortOrder.key === 'dateOrder' && sortOrder.value === 'DESC' ? (
                <ArrowDownward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'dateOrder', value: 'ASC' });
                    handleSort('dateOrder');
                  }}
                />
              ) : (
                <ArrowUpward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'dateOrder', value: 'DESC' });
                    handleSort('dateOrder');
                  }}
                  style={{ cursor: 'pointer' }}
                />
              )}
            </span>
          </Typography>
        </Box>
      ),
      renderCell: (params) => {
        const date = params?.row?.pay_deduction_date
          ? moment(params?.row?.pay_deduction_date).format('DD-MM-YYYY')
          : '-';
        return date !== '-' ? (
          <Tooltip
            arrow
            title={<Typography>{date}</Typography>}
            placement="bottom"
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <Typography className="text-ellipsis table-body-text transaction-date-wrap">
              {date}
            </Typography>
          </Tooltip>
        ) : (
          <Typography className="text-ellipsis table-body-text transaction-date-wrap">
            {date}
          </Typography>
        );
      },
    },
    {
      field: 'pay_transaction_id',
      headerName: 'Transaction Id',
      width: 260,
      minWidth: 260,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderCell: (params) => {
        const transactionId = params?.row?.pay_transaction_id;

        return transactionId ? (
          <Tooltip
            arrow
            title={<Typography>{transactionId}</Typography>}
            placement="bottom"
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <Typography className="text-ellipsis table-body-text">
              {transactionId}
            </Typography>
          </Tooltip>
        ) : (
          <Typography className="text-ellipsis table-body-text">-</Typography>
        );
      },
    },
    {
      field: 'payment_provider',
      headerName: 'Payment Method',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderCell: (params) => {
        const purchased = params?.row?.subscription_purchased;
        const isFreePlan =
          purchased?.subscription_plans?.subs_plan_name === 'Free Plan';
        const hasCard = purchased?.card;
        const provider = purchased?.payment_provider;
        // If free plan and no card, show just a dash
        if (isFreePlan && !hasCard) {
          return (
            <Typography className="table-body-text fw400 text-ellipsis">
              -
            </Typography>
          );
        }

        // If not free plan and card exists, show provider and card
        if (!isFreePlan && hasCard) {
          return (
            <Box className="d-flex align-center justify-center h100 flex-col cursor-pointer">
              <Typography className="table-body-text fw400 text-ellipsis">
                {provider || '-'}
              </Typography>
              <Typography className="table-body-text fw400 text-ellipsis">
                **** {hasCard}
              </Typography>
            </Box>
          );
        }
      },
    },
    {
      field: 'subs_plan_name',
      headerName: 'Plan Details',
      width: 90,
      minWidth: 90,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderCell: (params) => {
        const planName =
          params?.row?.subscription_purchased?.subscription_plans
            ?.subs_plan_name;
        return planName && planName !== '-' ? (
          <Tooltip
            arrow
            title={<Typography>{planName}</Typography>}
            placement="bottom"
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <Typography className="text-ellipsis table-body-text">
              {planName}
            </Typography>
          </Tooltip>
        ) : (
          <Typography className="text-ellipsis table-body-text">-</Typography>
        );
      },
    },
    {
      field: 'pay_amount',
      headerName: 'Total Amount',
      width: 110,
      minWidth: 110,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderHeader: () => (
        <Box className="d-flex align-center gap-5">
          <Typography variant="h6 p14 wrap-header-text">
            Total
            <span className="amount-text arrow-wrap">
              Amounts
              {sortOrder.key === 'amountOrder' && sortOrder.value === 'DESC' ? (
                <ArrowDownward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'amountOrder', value: 'ASC' });
                    handleSort('amountOrder');
                  }}
                />
              ) : (
                <ArrowUpward
                  className="arrow-icon cursor-pointer"
                  fontSize="small"
                  onClick={() => {
                    setSortOrder({ key: 'amountOrder', value: 'DESC' });
                    handleSort('amountOrder');
                  }}
                  style={{ cursor: 'pointer' }}
                />
              )}
            </span>
          </Typography>
        </Box>
      ),
      renderCell: (params) => {
        const currencyCode =
          params?.row?.subscription_purchased?.subscription_plans
            ?.subs_plan_currency;
        const symbol = getSymbol(currencyCode);
        return (
          <Typography className="text-ellipsis table-body-text">
            {symbol}
            {params?.row?.pay_amount}
          </Typography>
        );
      },
    },
    {
      field: 'pay_status',
      headerName: 'Patment Status',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderCell: (params) => {
        const status = params?.row?.pay_status;
        return status && status !== '-' ? (
          <Tooltip
            arrow
            title={<Typography>{status}</Typography>}
            placement="bottom"
            classes={{ tooltip: 'info-tooltip-container' }}
          >
            <Typography
              className={` ${status === 'Paid' ? 'status-paid' : 'status-failed'} text-ellipsis table-body-text`}
            >
              {status}
            </Typography>
          </Tooltip>
        ) : (
          <Typography className="text-ellipsis table-body-text">-</Typography>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 80,
      minWidth: 80,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      headerClassName: 'wrap-header-text',
      renderCell: (params) => {
        return (
          <Box className="d-flex actions align-center justify-center h100">
            <Tooltip
              title="View invoice"
              arrow
              placement="bottom"
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <VisibilityOutlinedIcon
                className="cursor-pointer action-icon-wrap"
                onClick={() => handleOpenDrawer(params?.row?.id)}
              />
            </Tooltip>
            <Tooltip
              title="Download"
              arrow
              placement="bottom"
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <DownloadIcon
                className="cursor-pointer action-icon-wrap"
                onClick={() => handleSingleDownload(params?.row?.id)}
              />
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const onPageChange = (newPage) => {
    setCurrentPage(newPage);
    getAllSubscriptionHistory('', newPage, '', sortOrder);
  };
  const OnRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setCurrentPage(1);
    getAllSubscriptionHistory('', 1, newPage, sortOrder);
  };
  const handleOpenDrawer = (pay_id) => {
    setOpenDrawer(true);
    handleInvoicePrieview(pay_id);
  };
  const handleCloseDrawer = () => {
    setOpenDrawer(false);
  };
  useEffect(() => {
    // getAllSubscriptionPlan();
    getAllSubscriptionHistory('', currentPage, '', sortOrder);
  }, []);

  // const handleSelectRow = (id, checked) => {
  //   if (checked) {
  //     setSelectedRows((prev) => [...prev, id]);
  //   } else {
  //     setSelectedRows((prev) => prev.filter((rowId) => rowId !== id));
  //   }
  // };

  const handleDownload = async () => {
    const payload = {
      paymentIds: selectedRows,
    };
    try {
      const { status, data } = await axiosInstance.post(
        ORG_URLS.GENERATE_INVOICE,
        payload,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        const currentDate = moment().format('YYYY-MM-DD');
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `Microffice-Invoices-${currentDate}.zip`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setSelectedRows([]);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleSingleDownload = async (paymentId) => {
    try {
      const { status, data } = await axiosInstance.post(
        ORG_URLS.GENERATE_INVOICE,
        { paymentIds: [paymentId], download: true }
      );

      if (status === 200) {
        const currentDate = moment().format('YYYY-MM-DD');
        const fileName = `Microffice-Inv-${paymentId}-${currentDate}.pdf`;
        const url = await getAuthBlobUrl(data?.data?.[0]);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        link.setAttribute('title', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleRowSelection = (selectionModel) => {
    setSelectedRows(selectionModel);
  };

  return (
    <Box className="transaction-history">
      <Box className="transaction-history-header d-flex align-center justify-space-between">
        <Typography className="header-text-wrap">Payment History</Typography>
        <Box className="download-btn-wrap d-flex gap-20 align-center">
          {/* <Box className="filters">
            <CustomTextField
              fullWidth
              InputLabelProps={{ shrink: true }}
              value={invoiceFilter}
              onChange={(e) => {
                setInvoiceFilter(e.target.value);
                getAllSubscriptionHistory(e.target.value, 1, '', sortOrder);
                setCurrentPage(1);
              }}
              placeholder="Search payment invoice"
              className="input-field"
              variant="filled"
            />
          </Box> */}
          <Typography className="text-align-end sub-title-text">
            {`(${selectedRows?.length}) items selected`}
          </Typography>
          <Box>
            <CustomButton
              variant="contained"
              type="submit"
              className="download-btn"
              title=""
              startIcon={
                <Tooltip arrow title="Download">
                  <DownloadIcon className="cursor-pointer" />
                </Tooltip>
              }
              onClick={handleDownload}
              isDownload={true}
              disabled={selectedRows?.length === 0}
            />
          </Box>
        </Box>
      </Box>

      <Box className="transaction-table-wrap">
        {loading || !transactions ? (
          <Box
            className="d-flex align-center justify-center"
            style={{ height: '400px' }}
          >
            <CircularProgress />
          </Box>
        ) : transactions.length === 0 ? (
          <Box className="no-data d-flex align-center justify-center">
            <NoDataView
              // image
              title="Payment History"
              description="There is no payment history available at the moment."
            />
          </Box>
        ) : (
          <Box>
            <Box className="table-container table-border-wrap invoice-table table-layout">
              <DataGrid
                disableColumnMenu
                rows={transactions}
                columns={columns}
                pageSize={rowsPerPage}
                checkboxSelection={true}
                disableSelectionOnClick
                onRowSelectionModelChange={handleRowSelection}
                rowSelectionModel={selectedRows}
                hideMenuIcon
                rowHeight={60}
                disableRowSelectionOnClick
                sx={{
                  transition: 'none',
                  [`& .${gridClasses.cell}`]: {
                    py: 1,
                  },
                }}
              />
              <CustomOrgPagination
                className="transaction-pagination"
                currentPage={currentPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={onPageChange}
                OnRowPerPage={OnRowPerPage}
              />
            </Box>
          </Box>
        )}
      </Box>
      <DialogBox
        open={openDrawer}
        handleClose={handleCloseDrawer}
        className="transaction-history-modal"
        title=""
        content={
          <>
            <ProductInvoice invoiceData={invoiceData} />
          </>
        }
      />
    </Box>
  );
};

export default TransactionHistory;
