import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import { InputAdornment } from '@mui/material';
import { getAttributeList } from '@/services/recipeService';
import {
  IngredientIconSize,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import './AllergenComponent.scss';
import Image from '@/components/UI/AppImage/AppImage';

const AllergenComponent = ({ formData, dispatch }) => {
  const [allergenSearch, setAllergenSearch] = useState('');
  const [allergenOptions, setAllergenOptions] = useState([]);
  const [loading, setLoading] = useState(true);

  // Local state for allergens - separate states for common and may contain
  const [localCommonAllergens, setLocalCommonAllergens] = useState(() => {
    return formData?.commonAllergens || [];
  });

  const [localMayContainAllergens, setLocalMayContainAllergens] = useState(
    () => {
      return formData?.mayContainAllergens || [];
    }
  );

  // Sync local state with prop changes
  useEffect(() => {
    setLocalCommonAllergens(formData?.commonAllergens || []);
    setLocalMayContainAllergens(formData?.mayContainAllergens || []);
  }, [formData?.commonAllergens, formData?.mayContainAllergens]);

  // Use local state for immediate UI updates
  const selectedCommonAllergens = localCommonAllergens;
  const selectedMayContainAllergens = localMayContainAllergens;

  // Fetch allergens from API
  useEffect(() => {
    const fetchAllergens = async () => {
      try {
        setLoading(true);
        const response = await getAttributeList(
          '',
          '',
          { status: 'active' },
          '',
          '',
          'allergen' // type
        );

        // Transform API response to component format
        const transformedAllergens = response?.attributes;
        setAllergenOptions(transformedAllergens);
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setAllergenOptions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAllergens();
  }, []);

  // Get severity class for styling
  const getSeverityClass = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'high':
        return 'allergen-component__allergen-item--high';
      case 'medium':
        return 'allergen-component__allergen-item--medium';
      case 'low':
        return 'allergen-component__allergen-item--low';
      default:
        return 'allergen-component__allergen-item--default';
    }
  };

  // Toggle Common Allergen
  const toggleCommonAllergen = (allergenId) => {
    const currentCommonAllergens = selectedCommonAllergens || [];
    const updatedCommonAllergens = currentCommonAllergens?.includes?.(
      allergenId
    )
      ? currentCommonAllergens?.filter?.((id) => id !== allergenId) || []
      : [...(currentCommonAllergens || []), allergenId];

    // Update local state immediately for UI responsiveness
    setLocalCommonAllergens(updatedCommonAllergens);

    // If adding this allergen to common, remove it from may contain (same allergen exclusivity)
    if (
      updatedCommonAllergens.includes(allergenId) &&
      selectedMayContainAllergens.includes(allergenId)
    ) {
      const updatedMayContainAllergens = selectedMayContainAllergens.filter(
        (id) => id !== allergenId
      );
      setLocalMayContainAllergens(updatedMayContainAllergens);
      if (dispatch) {
        dispatch({
          type: 'UPDATE_NUTRITION',
          payload: {
            commonAllergens: updatedCommonAllergens,
            mayContainAllergens: updatedMayContainAllergens,
          },
        });
      }
    } else {
      // Dispatch to parent
      if (dispatch) {
        dispatch({
          type: 'UPDATE_NUTRITION',
          payload: { commonAllergens: updatedCommonAllergens },
        });
      }
    }
  };

  // Toggle May Contain Allergen
  const toggleMayContainAllergen = (allergenId) => {
    const currentMayContainAllergens = selectedMayContainAllergens || [];
    const updatedMayContainAllergens = currentMayContainAllergens?.includes?.(
      allergenId
    )
      ? currentMayContainAllergens?.filter?.((id) => id !== allergenId) || []
      : [...(currentMayContainAllergens || []), allergenId];

    // Update local state immediately for UI responsiveness
    setLocalMayContainAllergens(updatedMayContainAllergens);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: { mayContainAllergens: updatedMayContainAllergens },
      });
    }
  };

  // Filter allergens based on search
  const filteredAllergens =
    allergenOptions?.filter?.((allergen) =>
      allergen?.attribute_title
        ?.toLowerCase?.()
        ?.includes?.(allergenSearch?.toLowerCase?.() || '')
    ) || [];

  if (loading) {
    return (
      <div className="allergen-component">
        <div className="allergen-component__loading">
          <Icon name="Loader2" size={24} color="var(--color-primary)" />
          <span>Loading allergens...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="allergen-component">
      {/* Header */}
      <div className="allergen-component__header">
        <div className="allergen-component__header-content">
          <Icon name="AlertTriangle" size={20} color="var(--color-danger)" />
          <div>
            <h3 className="allergen-component__title">Allergen Information</h3>
            <p className="allergen-component__description">
              Select allergens that this recipe contains or may contain
            </p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="allergen-component__search">
        <CustomTextField
          placeholder="Search allergens..."
          value={allergenSearch}
          onChange={(e) => setAllergenSearch(e?.target?.value || '')}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Icon
                  name="Search"
                  size={16}
                  color="var(--text-color-slate-gray)"
                />
              </InputAdornment>
            ),
          }}
          fullWidth
        />
      </div>

      {/* Common Allergens Grid */}
      <div className="allergen-component__allergens">
        <h3 className="allergen-component__allergens-title">
          Common Allergens
        </h3>
        <div className="allergen-component__allergens-grid">
          {filteredAllergens?.map?.((allergen) => {
            return (
              <button
                key={`common-${allergen?.id}`}
                onClick={() => toggleCommonAllergen(allergen?.id)}
                className={`allergen-component__allergen-item ${
                  selectedCommonAllergens?.includes?.(allergen?.id)
                    ? 'allergen-component__allergen-item--selected'
                    : getSeverityClass(allergen?.severity)
                }`}
              >
                <div
                  className={`allergen-component__allergen-icon ${
                    selectedCommonAllergens?.includes?.(allergen?.id)
                      ? 'allergen-component__allergen-icon--selected'
                      : 'allergen-component__allergen-icon--default'
                  }`}
                >
                  {allergen?.iconItem?.iconUrl && (
                    <Image
                      src={allergen?.iconItem?.iconUrl}
                      alt={allergen?.attribute_title}
                      width={IngredientIconSize}
                      height={IngredientIconSize}
                      style={{ objectFit: 'contain' }}
                    />
                  )}

                  {/* <Icon name={allergen?.icon} size={20} color="currentColor" /> */}
                </div>

                <div className="allergen-component__allergen-content">
                  <div className="allergen-component__allergen-label">
                    {allergen?.attribute_title}
                  </div>
                  <div className="allergen-component__allergen-severity">
                    {allergen?.attribute_description}
                  </div>
                </div>

                {selectedCommonAllergens?.includes?.(allergen?.id) && (
                  <Icon name="Check" size={16} />
                )}
              </button>
            );
          }) || []}
        </div>
      </div>

      {/* May Contain Allergens Grid */}
      <div className="allergen-component__allergens">
        <h3 className="allergen-component__allergens-title">
          May Contain Allergens
        </h3>
        <div className="allergen-component__allergens-grid">
          {filteredAllergens?.map?.((allergen) => {
            const isInCommon = selectedCommonAllergens?.includes?.(
              allergen?.id
            );
            return (
              <button
                key={`may-contain-${allergen?.id}`}
                onClick={() => toggleMayContainAllergen(allergen?.id)}
                disabled={isInCommon}
                className={`allergen-component__allergen-item  ${
                  selectedMayContainAllergens?.includes?.(allergen?.id)
                    ? 'allergen-component__allergen-item--selected allergen-component__may-allergen-item--selected'
                    : getSeverityClass(allergen?.severity)
                } ${
                  isInCommon
                    ? 'allergen-component__allergen-item--disabled'
                    : ''
                }`}
              >
                <div
                  className={`allergen-component__allergen-icon allergen-component__allergen-icon--may-contain ${
                    selectedMayContainAllergens?.includes?.(allergen?.id)
                      ? 'allergen-component__allergen-icon--selected'
                      : 'allergen-component__allergen-icon--default'
                  }`}
                >
                  {/* <Icon name={allergen?.icon} size={20} color="currentColor" /> */}
                  {allergen?.iconItem?.iconUrl && (
                    <Image
                      src={allergen?.iconItem?.iconUrl}
                      alt={allergen?.attribute_title}
                      width={IngredientIconSize}
                      height={IngredientIconSize}
                      style={{ objectFit: 'contain' }}
                    />
                  )}
                  <span className="allergen-component__allergen-indicator">
                    M
                  </span>
                </div>

                <div className="allergen-component__allergen-content">
                  <div className="allergen-component__allergen-label">
                    {allergen?.attribute_title}
                  </div>
                  <div className="allergen-component__allergen-severity">
                    {allergen?.attribute_description}
                  </div>
                </div>

                {selectedMayContainAllergens?.includes?.(allergen?.id) && (
                  <Icon name="Check" size={16} color="currentColor" />
                )}
              </button>
            );
          }) || []}
        </div>
      </div>

      {/* Combined Allergens Summary */}
      {((selectedCommonAllergens?.length || 0) > 0 ||
        (selectedMayContainAllergens?.length || 0) > 0) && (
        <div className="allergen-component__dietary">
          <div className="allergen-component__dietary-header">
            <Icon name="AlertTriangle" size={16} color="#EF4444" />
            <h4 className="allergen-component__dietary-title">
              Contains and May Contains (
              {(selectedCommonAllergens?.length || 0) +
                (selectedMayContainAllergens?.filter?.(
                  (id) => !selectedCommonAllergens?.includes?.(id)
                )?.length || 0)}
              )
            </h4>
          </div>
          <div className="allergen-component__dietary-grid">
            {/* Common Allergens */}
            {selectedCommonAllergens?.map?.((allergenId) => {
              const allergenData = allergenOptions?.find?.(
                (a) => a?.id === allergenId
              );
              return (
                <div
                  key={`common-${allergenId}`}
                  className="allergen-component__dietary-item allergen-component__dietary-item--selected"
                >
                  <div className="allergen-component__dietary-icon">
                    {/* <Icon
                      name={allergenData?.icon || 'AlertTriangle'}
                      size={16}
                      color="currentColor"
                    /> */}
                    {allergenData?.iconItem?.iconUrl && (
                      <Image
                        src={allergenData?.iconItem?.iconUrl}
                        alt={allergenData?.attribute_title}
                        width={IngredientIconSize}
                        height={IngredientIconSize}
                        style={{ objectFit: 'contain' }}
                      />
                    )}
                  </div>

                  <div className="allergen-component__dietary-content">
                    <span className="allergen-component__dietary-label">
                      {allergenData?.attribute_title || allergenId}
                    </span>
                  </div>

                  <button
                    onClick={() => toggleCommonAllergen(allergenId)}
                    className="allergen-component__dietary-remove"
                    aria-label={`Remove ${allergenData?.attribute_title || allergenId}`}
                  >
                    <Icon name="X" size={14} color="currentColor" />
                  </button>
                </div>
              );
            }) || []}

            {/* May Contain Allergens - Only show if not in Common Allergens */}
            {selectedMayContainAllergens
              ?.filter?.(
                (allergenId) => !selectedCommonAllergens?.includes?.(allergenId)
              )
              ?.map?.((allergenId) => {
                const allergenData = allergenOptions?.find?.(
                  (a) => a?.id === allergenId
                );
                return (
                  <div
                    key={`may-contain-${allergenId}`}
                    className="allergen-component__dietary-item allergen-component__dietary-item--may-contain"
                  >
                    <div className="allergen-component__dietary-icon allergen-component__dietary-icon--may-contain">
                      {/* <Icon
                        name={allergenData?.icon || 'AlertCircle'}
                        size={16}
                        color="currentColor"
                      /> */}

                      {allergenData?.iconItem?.iconUrl && (
                        <Image
                          src={allergenData?.iconItem?.iconUrl}
                          alt={allergenData?.attribute_title}
                          width={IngredientIconSize}
                          height={IngredientIconSize}
                          style={{ objectFit: 'contain' }}
                        />
                      )}
                      <span className="allergen-component__dietary-indicator">
                        M
                      </span>
                    </div>

                    <div className="allergen-component__dietary-content">
                      <span className="allergen-component__dietary-label">
                        {allergenData?.attribute_title || allergenId}
                      </span>
                    </div>

                    <button
                      onClick={() => toggleMayContainAllergen(allergenId)}
                      className="allergen-component__dietary-remove"
                      aria-label={`Remove ${allergenData?.attribute_title || allergenId}`}
                    >
                      <Icon name="X" size={14} color="currentColor" />
                    </button>
                  </div>
                );
              }) || []}
          </div>
        </div>
      )}
    </div>
  );
};

export default AllergenComponent;
