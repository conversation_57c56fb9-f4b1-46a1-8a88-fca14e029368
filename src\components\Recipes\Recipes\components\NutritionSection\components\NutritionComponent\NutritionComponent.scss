.nutrition-component {
  // Header
  &__header {
    margin-bottom: var(--spacing-xl);
  }

  &__header-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-xs) 0;
  }

  &__description {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin: 0;
  }

  // Grid Layout
  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-md);
    }
  }

  &__field-group {
    display: flex;
    flex-direction: column;
  }

  &__field-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  // Icon Colors
  &__icon {
    &--calories {
      color: var(--color-warning);
    }

    &--protein {
      color: var(--color-danger);
    }

    &--carbs {
      color: var(--color-info);
    }

    &--fat {
      color: var(--color-secondary-dark);
    }

    &--fiber {
      color: var(--color-success);
    }

    &--sugar {
      color: var(--color-pink);
    }

    &--sodium {
      color: var(--color-purple);
    }

    &--cholesterol {
      color: var(--color-danger-dark);
    }

    &--vitamin-a {
      color: var(--color-orange);
    }

    &--vitamin-c {
      color: var(--color-yellow);
    }

    &--calcium {
      color: var(--color-gray);
    }

    &--iron {
      color: var(--color-dark);
    }
  }

  // Summary Section
  &__summary {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__summary-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      gap: var(--spacing-xs);
    }
  }

  &__summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);

    &:last-child {
      border-bottom: none;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
  }

  &__summary-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);

    @media (max-width: 768px) {
      font-size: var(--font-size-xs);
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    &__header-content {
      gap: var(--spacing-sm);
    }

    &__title {
      font-size: var(--font-size-base);
    }

    &__description {
      font-size: var(--font-size-xs);
    }

    &__summary {
      padding: var(--spacing-md);
    }
  }

  @media (max-width: 480px) {
    &__grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-sm);
    }

    &__summary {
      padding: var(--spacing-sm);
    }

    &__summary-title {
      font-size: var(--font-size-sm);
    }
  }
}
