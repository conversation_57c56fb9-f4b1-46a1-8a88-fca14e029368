.nutrition-component {
  // Header
  &__header {
    margin-bottom: var(--spacing-xl);
  }

  &__header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  &__description {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin: 0;
  }

  // Loading State
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }

  // Search Section
  &__search {
    margin-bottom: var(--spacing-xl);
  }

  &__search-container {
    position: relative;
  }

  &__suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
  }

  &__suggestion-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.15s ease-out;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);

    &:hover {
      background-color: var(--color-off-white);
    }

    &:not(:last-child) {
      border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);
    }
  }

  // Nutrition List
  &__list {
    margin-bottom: var(--spacing-xl);
  }

  &__list-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    background-color: var(--color-off-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-white);
      border-color: var(--color-primary);
    }
  }

  &__item-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
    min-width: 0;
  }

  &__item-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0;
  }

  &__item-name {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__item-ingredient {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    font-style: italic;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__item-unit {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    min-width: 20px;
  }

  &__item-remove {
    background: none;
    border: none;
    color: var(--color-danger);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.15s ease-out;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background-color: var(--color-danger-opacity);
    }
  }

  // Summary Section
  &__summary {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    margin-bottom: var(--spacing-xl);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__summary-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background-color: var(--color-white);
    border-radius: var(--border-radius-sm);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
  }

  &__summary-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    flex: 1;
    min-width: 0;
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__summary-ingredient {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-xs);
    color: var(--text-color-slate-gray);
    font-style: italic;
  }

  &__summary-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    white-space: nowrap;
  }

  // Empty State
  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-color-slate-gray);

    h4 {
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-lg);
      color: var(--text-color-primary);
      margin: var(--spacing-md) 0 var(--spacing-sm) 0;
    }

    p {
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-normal);
      font-size: var(--font-size-sm);
      color: var(--text-color-slate-gray);
      margin: 0;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    &__header-content {
      gap: var(--spacing-sm);
    }

    &__title {
      font-size: var(--font-size-base);
    }

    &__description {
      font-size: var(--font-size-xs);
    }

    &__summary {
      padding: var(--spacing-md);
    }
  }

  @media (max-width: 480px) {
    &__grid {
      grid-template-columns: 1fr;
      gap: var(--spacing-sm);
    }

    &__summary {
      padding: var(--spacing-sm);
    }

    &__summary-title {
      font-size: var(--font-size-sm);
    }
  }
}
