'use client';
import { Box, Tab, Tooltip, Typography } from '@mui/material';
import React, { useState } from 'react';
import QueryBuilderIcon from '@mui/icons-material/QueryBuilder';
import './ticketdetails.scss';
import Stopwatch from '@/components/StopWatch';
import { Tab<PERSON>ontext, TabList, TabPanel } from '@mui/lab';
import Conversation from './Conversation';
import TimeEntry from './TimeEntry';
import Attachment from './Attachment';
import TicketInformation from './TicketInformation';
import History from './History';
export default function TicketDetails() {
  const [time, setTime] = useState(0);
  const [running, setRunning] = useState(false);
  const [savedTimes, setSavedTimes] = useState([]);
  const [showButtons, setShowButtons] = useState(false);
  const [tab, setTab] = useState(1);
  const reports_tabs = [
    { id: 1, name: 'Ticket Information' },
    { id: 2, name: 'Conversation' },
    { id: 3, name: 'Time Entry' },
    { id: 4, name: 'Attachment' },
    { id: 5, name: 'History' },
  ];

  const tabChangeHandler = (event, newValue) => {
    setTab(newValue);
  };
  return (
    <Box className="details-wrap">
      <Box className="ticket-details-wrap">
        <Typography variant="h5" className="ticket-name">
          Test
        </Typography>
        <Box className="detail-wrap d-flex align-center">
          <Typography variant="h6" className="id-wrap">
            #100
          </Typography>
          <Tooltip title="Yagnik Siddhapura" arrow>
            <Typography component="p" className="name-wrap">
              Yagnik Siddhapura
            </Typography>
          </Tooltip>
          <Box className="d-flex align-center">
            <QueryBuilderIcon className="timer-icon" />
            <Typography variant="h6" className="time-wrap">
              Time
            </Typography>
          </Box>
          <Box className="watch-wrap">
            <Stopwatch
              time={time}
              setTime={setTime}
              running={running}
              setRunning={setRunning}
              savedTimes={savedTimes}
              setSavedTimes={setSavedTimes}
              showButtons={showButtons}
              setShowButtons={setShowButtons}
            />
          </Box>
        </Box>
      </Box>
      <Box className="ticket-tab-handler">
        <TabContext value={String(tab)}>
          <Box className="tabs-wrap">
            <Box className="report-tabs">
              <TabList
                variant="scrollable"
                scrollButtons="auto"
                onChange={tabChangeHandler}
                aria-label="action tabs"
                className="tab-list-sec"
              >
                {reports_tabs?.map((obj, index) => {
                  return (
                    <Tab
                      key={index}
                      label={obj?.name}
                      value={String(obj?.id)}
                      className="tab-name"
                    />
                  );
                })}
              </TabList>
            </Box>
          </Box>
          <TabPanel value="1" className="pl0 pr0 pb0 pt0">
            <TicketInformation />
          </TabPanel>
          <TabPanel value="2" className="pl0 pr0 pb0 pt0">
            <Conversation />
          </TabPanel>
          <TabPanel value="3" className="pl0 pr0 pb0 pt0">
            <TimeEntry savedTimes={savedTimes} />
          </TabPanel>
          <TabPanel value="4" className="pl0 pr0 pb0 pt0">
            <Attachment />
          </TabPanel>
          <TabPanel value="5" className="pl0 pr0 pb0 pt0">
            <History />
          </TabPanel>
        </TabContext>
      </Box>
    </Box>
  );
}
