// Form Progress Indicator Component Styles
.form-progress-indicator {
  // background-color: var(--color-secondary);
  border-bottom: var(--border-width-xs) solid var(--border-color-light-gray);
  padding: var(--spacing-lg) var(--spacing-lg);

  // Large screens padding
  @media (min-width: 1024px) {
    padding: var(--spacing-lg) var(--spacing-xxl);
  }

  // Progress Bar Section
  &__progress-section {
    margin-bottom: var(--spacing-lg);
  }

  &__progress-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
  }

  &__progress-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
  }

  &__progress-count {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__progress-bar {
    width: 100%;
    background-color: var(--color-light-grayish-blue);
    border-radius: var(--border-radius-full);
    height: var(--spacing-sm);
  }

  &__progress-fill {
    background-color: var(--color-primary);
    height: var(--spacing-sm);
    border-radius: var(--border-radius-full);
    transition: all 0.3s ease-out;
  }

  // Desktop Navigation
  &__desktop-nav {
    display: none;
    align-items: center;
    gap: var(--spacing-sm);
    overflow-x: auto;

    @media (min-width: 768px) {
      display: flex;
    }
  }

  &__section-button {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid;
    transition: all 0.15s ease-out;
    white-space: nowrap;
    cursor: pointer;
    background: none;

    &:hover {
      box-shadow: var(--box-shadow-xs);
    }

    &--completed {
      background-color: var(--color-success);
      color: var(--text-color-white);
      border-color: var(--border-color-green);
    }

    &--current {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      border-color: var(--border-color-primary);
    }

    &--error {
      background-color: var(--color-danger);
      color: var(--text-color-white);
      border-color: var(--border-color-red);
    }

    &--pending {
      background-color: var(--color-secondary);
      color: var(--text-color-slate-gray);
      border-color: var(--border-color-light-gray);
    }
  }

  &__section-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__section-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
  }

  // Mobile Navigation
  &__mobile-nav {
    display: block;

    @media (min-width: 768px) {
      display: none;
    }
  }

  &__mobile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
  }

  &__mobile-current {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-color-primary);
  }

  &__mobile-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
  }

  &__mobile-counter {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  &__mobile-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
  }

  &__mobile-dot {
    width: var(--spacing-md);
    height: var(--spacing-md);
    border-radius: var(--border-radius-full);
    border: var(--border-width-sm) solid;
    transition: all 0.15s ease-out;
    cursor: pointer;
    background: none;

    &--completed {
      background-color: var(--color-success);
      border-color: var(--border-color-green);
    }

    &--current {
      background-color: var(--color-primary);
      border-color: var(--border-color-primary);
    }

    &--error {
      background-color: var(--color-danger);
      border-color: var(--border-color-red);
    }

    &--pending {
      background-color: var(--color-secondary);
      border-color: var(--border-color-light-gray);
    }
  }
}
