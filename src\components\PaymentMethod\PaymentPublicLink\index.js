'use client';
import React, { useEffect, useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CachedIcon from '@mui/icons-material/Cached';
import { Config } from '@/helper/context/config';
import './paymentlink.scss';

export default function PaymentPublicLink({ selectedPlan }) {
  const [publicLink, setPublicLink] = useState('');
  const urlPattern = /^(ftp|http|https):\/\/[^ "]+$/;

  const copyToClipboard = () => {
    if (urlPattern.test(publicLink)) {
      navigator.clipboard.writeText(publicLink).then(
        () => {
          setApiMessage('success', 'Link copied');
        },
        () => {
          setApiMessage('error', 'Failed to copy link');
        }
      );
    } else {
      setApiMessage('error', 'Please enter valid url to copy');
    }
  };
  const GeneratePublicLink = async (Plan) => {
    const sendData = {
      plan_id: Plan,
      provider_id: 1,
      public_link: true,
    };
    try {
      const { status, data } = await axiosInstance.post(
        ORG_URLS?.PURCHASE_PLAN,
        sendData
      );

      if (status === 200) {
        if (data?.status) {
          setPublicLink(Config.baseURL + '/org/payment?' + data?.data?.linkUrl);
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      // setLoader(false);
    }
  };

  useEffect(() => {
    if (selectedPlan?.id) {
      GeneratePublicLink(selectedPlan?.id);
    }
  }, [selectedPlan?.id]);

  return (
    <>
      <Box className="add-card-header">
        <Typography variant="h6" className="sub-header-text">
          Payment Public Link
        </Typography>
        <>
          <Box className="public-link-sec d-flex .align-end">
            <Box className="public-payment-input">
              <CustomTextField
                fullWidth
                required
                name="cardHolderName"
                label="Public Link"
                placeholder="Public Link"
                value={publicLink}
                onChange={(e) => setPublicLink(e.target.value)}
              />
            </Box>
            <Box className="generate-password-btn d-flex gap-10">
              {/* {Randompassword ? ( */}
              <Tooltip title="Copy password" arrow>
                <ContentCopyIcon
                  className={'cursor-pointer'}
                  onClick={() => {
                    copyToClipboard();
                  }}
                />
              </Tooltip>
              {/* ) : (
                    <ContentCopyIcon className={'disabled-copy'} />
                  )} */}

              <Tooltip title="Generate password" arrow>
                <CachedIcon
                  className={'cursor-pointer '}
                  onClick={() => {
                    GeneratePublicLink(selectedPlan?.id);
                  }}
                />
              </Tooltip>
            </Box>
          </Box>
        </>
      </Box>
    </>
  );
}
