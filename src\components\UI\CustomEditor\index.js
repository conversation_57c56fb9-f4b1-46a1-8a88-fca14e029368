'use client';

import React, { useRef } from 'react';
import dynamic from 'next/dynamic';

const JoditEditor = dynamic(() => import('jodit-react'), {
  ssr: false,
});
const CustomEditor = (props) => {
  const { content, setContent, height } = props;
  const editor = useRef(null);
  // useEffect(() => {
  //   if (editor?.current) {
  //     const jodit = editor.current;
  //     jodit.events.on('paste', (event) => {
  //       event.preventDefault();

  //       const editorInstance = jodit.editor;
  //       const clipboardData = event.clipboardData || window.clipboardData;
  //       const pastedData = clipboardData.getData('text'); // Get plain text
  //       // Insert the plain text, stripping all formatting
  //       editorInstance.execCommand(
  //         'insertHTML',
  //         pastedData.replace(/<\/?[^>]+(>|$)/g, '')
  //       );
  //     });
  //   }
  // }, []);
  const config = {
    height: height || 200,
    toolbarSticky: false,
    toolbarAdaptive: false,
    buttons: [
      'undo',
      'redo',
      'font',
      'fontsize',
      'paragraph',
      // 'source',
      'bold',
      'italic',
      'underline',
      'strikethrough',
      'superscript',
      'subscript',
      'align',
      'ul',
      'ol',
      'lineHeight',
      'brush',
      'outdent',
      'indent',
      'table',
      'cut',
      'copy',
      // 'paste',
      'selectall',
      // 'hr',
      'eraser',
      'fullsize',
      'preview',
    ],
    // cleanHTML: {
    //   timeout: 500,
    //   fillEmptyParagraph: false,
    //   replaceNBSP: true, // Replaces non-breaking spaces with regular spaces
    // },
    askBeforePasteFromWord: false,
    askBeforePasteHTML: false,
    defaultActionOnPaste: 'insert_clear_html',
    // pasteHTMLActionList: [
    //   'insert_as_html',
    //   'insert_clear_html',
    //   'insert_only_text',
    // ], // for copy css as it from doc
    // defaultActionOnPaste: 'insert_as_html',
    // defaultMode: 1,
    // defaultActionOnPaste: 'insert_as_html', // Paste as HTML by default
    readonly: false,
    beautyHTML: true,
    autofocus: false,
    controls: {
      font: {
        list: {
          '': 'Default',
          'Helvetica,sans-serif': 'Helvetica',
          'Arial,Helvetica,sans-serif': 'Arial',
          'Georgia,serif': 'Georgia',
          'Impact,Charcoal,sans-serif': 'Impact',
          'Tahoma,Geneva,sans-serif': 'Tahoma',
          "'Times New Roman',Times,serif": 'Times New Roman',
          'Verdana,Geneva,sans-serif': 'Verdana',
          'Roboto Medium,Arial,sans-serif': 'Roboto',
          'Calibri, sans-serif': 'Calibri',
        },
      },
      lineHeight: {
        list: {
          1: 1,
          1.15: 1.15,
          1.5: 1.5,
          2: 2,
        },
      },
    },
    tabIndex: 1,
  };

  return (
    <div className="custom-editor">
      <JoditEditor
        ref={editor}
        height={height || 200}
        value={content}
        config={config}
        onBlur={(newContent) => {
          setContent(newContent);
        }}
        // onChange={(newContent) => {
        //   setContent(newContent);
        // }}
      />
    </div>
  );
};

export default CustomEditor;
