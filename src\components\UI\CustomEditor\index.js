'use client';

import React, { useRef } from 'react';
import dynamic from 'next/dynamic';

const JoditEditor = dynamic(() => import('jodit-react'), {
  ssr: false,
});
const CustomEditor = (props) => {
  const { content, setContent, height } = props;
  const editor = useRef(null);
  // useEffect(() => {
  //   if (editor?.current) {
  //     const jodit = editor.current;
  //     jodit.events.on('paste', (event) => {
  //       event.preventDefault();

  //       const editorInstance = jodit.editor;
  //       const clipboardData = event.clipboardData || window.clipboardData;
  //       const pastedData = clipboardData.getData('text'); // Get plain text
  //       // Insert the plain text, stripping all formatting
  //       editorInstance.execCommand(
  //         'insertHTML',
  //         pastedData.replace(/<\/?[^>]+(>|$)/g, '')
  //       );
  //     });
  //   }
  // }, []);
  const config = {
    height: height || 200,
    toolbarSticky: false,
    toolbarAdaptive: false,
    buttons: [
      'fontsize',
      'brush', // Text color
      'bold',
      'italic',
      'underline',
      'strikethrough',
      'ul', // Unordered list only
    ],
    // cleanHTML: {
    //   timeout: 500,
    //   fillEmptyParagraph: false,
    //   replaceNBSP: true, // Replaces non-breaking spaces with regular spaces
    // },
    askBeforePasteFromWord: false,
    askBeforePasteHTML: false,
    defaultActionOnPaste: 'insert_clear_html',
    // pasteHTMLActionList: [
    //   'insert_as_html',
    //   'insert_clear_html',
    //   'insert_only_text',
    // ], // for copy css as it from doc
    // defaultActionOnPaste: 'insert_as_html',
    // defaultMode: 1,
    // defaultActionOnPaste: 'insert_as_html', // Paste as HTML by default
    readonly: false,
    beautyHTML: true,
    autofocus: false,
    controls: {
      fontsize: {
        list: {
          8: '8px',
          9: '9px',
          10: '10px',
          11: '11px',
          12: '12px',
          14: '14px',
          16: '16px',
          18: '18px',
          20: '20px',
          24: '24px',
          28: '28px',
          32: '32px',
        },
      },
    },
    tabIndex: 1,
  };

  return (
    <div className="custom-editor">
      <JoditEditor
        ref={editor}
        height={height || 200}
        value={content}
        config={config}
        onBlur={(newContent) => {
          setContent(newContent);
        }}
        // onChange={(newContent) => {
        //   setContent(newContent);
        // }}
      />
    </div>
  );
};

export default CustomEditor;
