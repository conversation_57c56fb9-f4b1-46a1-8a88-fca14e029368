'use client';
import React, { useEffect, useState } from 'react';
import { Box, Popover, Typography } from '@mui/material';
import Holiday from '@/components/Leave/Holiday';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import DialogBox from '@/components/UI/Modalbox';
import AddEditHolidayModal from './AddEditHoliday';
import './holidayManagement.scss';

export default function HolidayManagement() {
  const [holidayList, setHolidayList] = useState([]);
  const [loader, setLoader] = useState(true);
  const [holidayRowsPerPage, setHolidayRowsPerPage] = useState('');
  const [leavePage, setLeavePage] = useState(1);
  const [leaveTotalCount, setLeaveTotalCount] = useState(0);
  const [anchorEl, setAnchorEl] = useState(null);
  const [createModal, setCreateModal] = useState(false);
  const [editType, setEditType] = useState();

  const [leaveFilterData, setLeaveFilterData] = useState({
    searchValue: '',
    year: '',
    status: '',
  });
  const [holidayAppliedFil, setHolidayAppliedFil] = useState({
    searchValue: '',
    year: '',
    status: '',
  });

  const open = Boolean(anchorEl);

  const id = open ? 'simple-popper' : undefined;

  const handleClose = () => {
    setAnchorEl(null);
  };

  const getHolidayList = async (
    leavePage,
    searchValue,
    filter,
    Rpp,
    isUpdated = false
  ) => {
    !isUpdated && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${
          URLS?.GET_HOLIDAY_LIST
        }?search=${searchValue}&page=${leavePage}&holiday_policy_year=${
          filter?.year
        }&holidayTypeStatus=${filter?.status}&size=${Rpp ? Rpp : holidayRowsPerPage}`
      );

      if (status === 200) {
        setHolidayList(data?.data);
        setLeaveTotalCount(data?.count);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveBalanceListDownload = async (
    leavePage,
    searchValue,
    filter,
    format,
    Rpp
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        `${
          URLS?.GET_LEAVE_BALANCE
        }?branch_id=&department_id=${''}&role_id=&search=${searchValue}&page=${leavePage}&size=${
          Rpp ? Rpp : holidayRowsPerPage
        }&download=${format}`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute(
          'download',
          `leave_balance.${format === 'excel' ? 'xlsx' : format}`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getHolidayList(
      leavePage,
      holidayAppliedFil?.searchValue,
      leaveFilterData,
      '',
      false
    );
  }, []);

  const handleDownload = (format) => {
    getLeaveBalanceListDownload(
      leavePage,
      holidayAppliedFil?.searchValue,
      holidayAppliedFil,
      format
    );

    setAnchorEl(null);
  };
  return (
    <Box className="holiday-management-wrap">
      <Holiday
        leaveFilterData={leaveFilterData}
        setLeaveFilterData={setLeaveFilterData}
        holidayAppliedFil={holidayAppliedFil}
        setHolidayAppliedFil={setHolidayAppliedFil}
        getHolidayList={getHolidayList}
        loader={loader}
        leavePage={leavePage}
        setHRowsPerPage={setHolidayRowsPerPage}
        setLeavePage={setLeavePage}
        holidayList={holidayList}
        leaveTotalCount={leaveTotalCount}
        setCreateModal={setCreateModal}
        setEditType={setEditType}
      />
      <Popover
        className="download-popover"
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box className="export-option">
          <Typography
            className="p14 fw600 pb8 cursor-pointer"
            onClick={() => handleDownload('pdf')}
          >
            PDF
          </Typography>
          <Typography
            className="p14 fw600 pb8 cursor-pointer"
            onClick={() => handleDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="p14 fw600 cursor-pointer"
            onClick={() => handleDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>

      <DialogBox
        open={createModal}
        handleClose={() => {
          setCreateModal(false);
          setTimeout(() => {
            setEditType();
          }, 500);
        }}
        title={editType ? 'Edit Holiday List' : 'Add Holiday List'}
        className="small-dialog-box-container"
        content={
          <AddEditHolidayModal
            holidayAppliedFil={holidayAppliedFil}
            getHolidayList={getHolidayList}
            setCreateModal={setCreateModal}
            editType={editType}
            setEditType={setEditType}
          />
        }
      />
    </Box>
  );
}
