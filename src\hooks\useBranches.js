import { useState } from 'react';
import { branchService } from '@/services/branchService';
import { setApiMessage } from '@/helper/common/commonFunctions';

export const useBranches = () => {
  const [branchList, setBranchList] = useState([]);
  const [branchCatList, setBranchCatList] = useState([]);
  const [loading, setLoading] = useState(false);

  const getBranchList = async (search = '', pageNo = 1) => {
    try {
      setLoading(true);
      const branches = await branchService.getBranchList(search, pageNo);
      setBranchList(branches);
      return branches;
    } catch (error) {
      setBranchList([]);
      setApiMessage('error', error?.response?.data?.message);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getCategoryBranchList = async (
    categoryId,
    search = '',
    pageNo = ''
  ) => {
    try {
      setLoading(true);
      const branches = await branchService.getCategoryBranchList(
        categoryId,
        search,
        pageNo
      );
      setBranchCatList(branches);
      return branches;
    } catch (error) {
      setBranchCatList([]);
      setApiMessage('error', error?.response?.data?.message);
      return [];
    } finally {
      setLoading(false);
    }
  };

  return {
    branchList,
    branchCatList,
    loading,
    getBranchList,
    getCategoryBranchList,
  };
};
