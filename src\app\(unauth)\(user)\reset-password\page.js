'use client';

import React, { useContext, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Box, Typography, InputAdornment, IconButton } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { setApiMessage } from '@/helper/common/commonFunctions';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { fetchFromStorage, removeFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { CustomTextField } from '@/components/UI/CommonField/index';
import CustomButton from '@/components/UI/button';
// import NVlogo from '../../../../../public/images/logo_NV.png';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import './resetpassword.scss';
import AppBrandLogo from '@/components/UI/AppBrandLogo';

const ResetPassword = () => {
  const router = useRouter();
  const { authState } = useContext(AuthContext);
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [showrepeatPassword, setShowrepeatPassword] = useState(false);

  // const [loader, setLoader] = useState(false);
  return (
    <>
      <Box className="login-page d-flex justify-center align-center">
        <Box className="login-block d-flex justify-center">
          <AppBrandLogo
            className="text-align"
            onClick={() => router.push('/login')}
          />

          <Typography
            variant="h3"
            className="h3 main-heading fw700 pt16 text-align"
          >
            Update your password
          </Typography>
          <Typography
            variant="h6"
            className="p16 forgot-content fw400 text-align pt40"
          >
            Please enter a new password and confirm password. Make sure you
            remember it always.
          </Typography>
          <Box className="pt32">
            <Formik
              initialValues={{
                new_password: '',
                repeat_password: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                new_password: Yup.string()
                  .trim()
                  .required('This field is required')
                  // .min(8, 'Password length must be minimum 8 character'),
                  .min(8, 'Password must be at least 8 characters')
                  .matches(
                    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
                    'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'
                  ),
                repeat_password: Yup.string()
                  .trim()
                  .required('This field is required')
                  .oneOf(
                    [Yup.ref('new_password'), null],
                    'The passwords you entered do not match'
                  ),
              })}
              onSubmit={async (requestData) => {
                let sendData = {
                  user_email: authState?.Email
                    ? authState?.Email
                    : fetchFromStorage(identifiers?.EMAIL)
                      ? fetchFromStorage(identifiers?.EMAIL)
                      : '',
                  new_password: requestData?.new_password,
                  // confirm_password: requestData?.repeat_password
                };
                try {
                  // setLoader(true);
                  const { status, data } = await axiosInstance.post(
                    URLS.RESET_PASSWORD,
                    sendData
                  );

                  if (status === 200) {
                    if (data.status) {
                      setApiMessage('success', data?.message);
                      removeFromStorage(identifiers?.EMAIL);
                      router.push('/login');
                    } else {
                      setApiMessage('error', data?.message);
                    }
                    // setLoader(false);
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                  // setLoader(false);
                }
              }}
            >
              {({
                errors,
                touched,
                handleBlur,
                values,
                handleSubmit,
                handleChange,
                // dirty,
                // isValid,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <Box>
                    <CustomTextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="new_password"
                      name="new_password"
                      value={values.new_password}
                      label="Enter new password"
                      type={showEnterPassword ? 'text' : 'password'}
                      variant="filled"
                      className={
                        touched.new_password && errors.new_password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      error={Boolean(
                        touched.new_password && errors.new_password
                      )}
                      helperText={touched.new_password && errors.new_password}
                      onBlur={handleBlur}
                      placeholder="Enter new password"
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton
                              disableRipple
                              onClick={() =>
                                setShowCurrentPassword(!showEnterPassword)
                              }
                            >
                              <Box className="eye-wrap">
                                {showEnterPassword ? (
                                  <VisibilityIcon />
                                ) : (
                                  <VisibilityOffIcon />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box className="pt40">
                    <CustomTextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="repeat_password"
                      name="repeat_password"
                      value={values.repeat_password}
                      label="Confirm new password"
                      placeholder="Enter confirm new password"
                      type={showrepeatPassword ? 'text' : 'password'}
                      variant="filled"
                      className={
                        touched.repeat_password && errors.repeat_password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      error={Boolean(
                        touched.repeat_password && errors.repeat_password
                      )}
                      helperText={
                        touched.repeat_password && errors.repeat_password
                      }
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton
                              disableRipple
                              onClick={() =>
                                setShowrepeatPassword(!showrepeatPassword)
                              }
                            >
                              <Box className="eye-wrap">
                                {showrepeatPassword ? (
                                  <VisibilityIcon />
                                ) : (
                                  <VisibilityOffIcon />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Box textAlign="center" className="signin-btn pt24">
                      <CustomButton
                        fullWidth
                        type="submit"
                        variant="contained"
                        background="#39596e"
                        className="p16"
                        fontWeight="600"
                        colorhover="#FFFFFF"
                        isLogin={true}
                        backgroundhover="#39596e"
                        title="Change my password"
                      />
                    </Box>
                  </Box>
                </Form>
              )}
            </Formik>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default ResetPassword;
