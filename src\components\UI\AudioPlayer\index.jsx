import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './AudioPlayer.scss';

const AudioPlayer = ({ audioUrl, onClose }) => {
  return (
    <div className="audio-player">
      <div className="audio-player__content">
        <audio controls className="audio-player__controls">
          <source src={audioUrl} type="audio/mpeg" />
          Your browser does not support the audio element.
        </audio>
        <button className="audio-player__close" onClick={onClose}>
          <Icon name="X" size={16} color="white" />
        </button>
      </div>
    </div>
  );
};

export default AudioPlayer;
