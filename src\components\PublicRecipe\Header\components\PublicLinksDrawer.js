import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import { CONTACT_NO, INFO_LINKS } from '@/helper/constants/urls';
import Link from 'next/link';
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined';
import '../publicheader.scss';

export default function PublicLinksDrawer() {
  const links = [
    { text: 'Terms and Conditions', path: INFO_LINKS?.TermsConditions },
    { text: 'Privacy Notice', path: INFO_LINKS?.PrivacyPolicy },
  ];

  return (
    <Box className="public-links-drawer">
      <Box className="links-container">
        {links?.map((link) => (
          <Box key={link?.text} className="link-item">
            <Link
              className="brown-text link-item-text"
              target="_blank"
              href={link?.path}
            >
              {link?.text}
            </Link>
          </Box>
        ))}
      </Box>
      <Divider />
      <Box className="contact-us-container">
        <Box className="link-item">
          <Link
            className="brown-text link-item-text"
            target="_blank"
            href={INFO_LINKS?.contactUs}
          >
            Contact Us
          </Link>
        </Box>
        <Box className="d-flex align-center gap-sm phone-wrap">
          <LocalPhoneOutlinedIcon className="phone-icon" />
          <Link className="brown-text" href={`tel:${CONTACT_NO?.phone}`}>
            <Typography className="brown-text">{CONTACT_NO?.phone}</Typography>
          </Link>
        </Box>
      </Box>
    </Box>
  );
}
