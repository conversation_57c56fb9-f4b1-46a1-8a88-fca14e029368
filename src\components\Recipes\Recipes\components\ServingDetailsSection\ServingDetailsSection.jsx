import React, { useState, useEffect } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import './ServingDetailsSection.scss';

// Validation Schema
const servingValidationSchema = Yup.object().shape({
  yield: Yup.object().shape({
    value: Yup.number()
      .min(0, 'Yield cannot be negative')
      .max(9999, 'Yield too high'),
    unit: Yup.string().required('Unit is required'),
  }),
  totalPortions: Yup.number()
    .min(1, 'Must have at least 1 portion')
    .max(999, 'Too many portions'),
  servingMethod: Yup.string().max(100, 'Serving method too long'),
  serveIn: Yup.string().max(100, 'Serve in option too long'),
  garnish: Yup.string().max(500, 'Garnish description too long'),
  fohTips: Yup.string().max(1000, 'FOH tips too long'),
  chefTips: Yup.string().max(1000, 'Chef tips too long'),
});

const ServingDetailsSection = ({ formData, dispatch }) => {
  // Local state for serving to ensure immediate UI updates
  const [localServing, setLocalServing] = useState(() => {
    return (
      formData?.serving || {
        yield: { value: 0, unit: 'servings' },
        totalPortions: 0,
        singlePortionSize: 0,
        servingMethod: '',
        serveIn: '',
        garnish: '',
        fohTips: '',
        chefTips: '',
      }
    );
  });

  // Sync local state with prop changes
  useEffect(() => {
    setLocalServing(
      formData?.serving || {
        yield: { value: 0, unit: 'servings' },
        totalPortions: 0,
        singlePortionSize: 0,
        servingMethod: '',
        serveIn: '',
        garnish: '',
        fohTips: '',
        chefTips: '',
      }
    );
  }, [formData?.serving]);

  // Use local state for immediate UI updates
  const serving = localServing;

  const yieldUnits = [
    { label: 'Servings', value: 'servings' },
    { label: 'Portions', value: 'portions' },
    { label: 'Cups', value: 'cups' },
    { label: 'Liters', value: 'liters' },
    { label: 'Pieces', value: 'pieces' },
    { label: 'Slices', value: 'slices' },
    { label: 'Bowls', value: 'bowls' },
    { label: 'Plates', value: 'plates' },
  ];

  const servingMethods = [
    { label: 'Individual plates', value: 'individual_plates' },
    { label: 'Family style', value: 'family_style' },
    { label: 'Buffet style', value: 'buffet_style' },
    { label: 'Shared platters', value: 'shared_platters' },
    { label: 'Tasting portions', value: 'tasting_portions' },
    { label: 'Cocktail style', value: 'cocktail_style' },
    { label: 'Picnic style', value: 'picnic_style' },
  ];

  const serveInOptions = [
    { label: 'Dinner plates', value: 'dinner_plates' },
    { label: 'Salad plates', value: 'salad_plates' },
    { label: 'Bowls', value: 'bowls' },
    { label: 'Soup bowls', value: 'soup_bowls' },
    { label: 'Ramekins', value: 'ramekins' },
    { label: 'Glasses', value: 'glasses' },
    { label: 'Mugs', value: 'mugs' },
    { label: 'Platters', value: 'platters' },
    { label: 'Serving dishes', value: 'serving_dishes' },
  ];

  // Validate serving field
  const validateServingField = async (field, value) => {
    try {
      await servingValidationSchema.validateAt(field, { [field]: value });
      return null;
    } catch (error) {
      return error?.message || 'Invalid value';
    }
  };

  const updateServing = async (field, value) => {
    // Update local state immediately for UI responsiveness
    const updatedServing = { ...serving, [field]: value };
    setLocalServing(updatedServing);

    // Validate field
    await validateServingField(field, value);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_SERVING',
        payload: { [field]: value },
      });
    }
  };

  const updateYield = async (field, value) => {
    const updatedYield = { ...serving?.yield, [field]: value };

    // Update local state immediately for UI responsiveness
    const updatedServing = { ...serving, yield: updatedYield };
    setLocalServing(updatedServing);

    // Validate yield
    await validateServingField('yield', updatedYield);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_SERVING',
        payload: { yield: updatedYield },
      });
    }

    // Auto-calculate single portion size
    if (field === 'value' || (serving?.totalPortions || 0) > 0) {
      const yieldValue =
        field === 'value' ? parseFloat(value) || 0 : serving?.yield?.value || 0;
      const totalPortions = serving?.totalPortions || 0;
      const singlePortionSize =
        totalPortions > 0 ? yieldValue / totalPortions : 0;

      // Update local state with calculated portion size
      const finalUpdatedServing = { ...updatedServing, singlePortionSize };
      setLocalServing(finalUpdatedServing);

      // Dispatch calculated portion size
      if (dispatch) {
        dispatch({
          type: 'UPDATE_SERVING',
          payload: { singlePortionSize },
        });
      }
    }
  };

  // Auto-calculate single portion size when total portions change
  useEffect(() => {
    if ((serving?.yield?.value || 0) > 0 && (serving?.totalPortions || 0) > 0) {
      const singlePortionSize =
        (serving?.yield?.value || 0) / (serving?.totalPortions || 1);
      if (singlePortionSize !== (serving?.singlePortionSize || 0)) {
        // Update local state
        const updatedServing = { ...serving, singlePortionSize };
        setLocalServing(updatedServing);

        // Dispatch to parent
        if (dispatch) {
          dispatch({
            type: 'UPDATE_SERVING',
            payload: { singlePortionSize },
          });
        }
      }
    }
  }, [
    serving?.yield?.value,
    serving?.totalPortions,
    serving?.singlePortionSize,
    dispatch,
  ]);

  return (
    <div className="serving-details-section">
      {/* Yield and Portions */}
      <div className="serving-details-section__yield-section">
        <div className="serving-details-section__header">
          <h3 className="serving-details-section__title">Yield & Portions</h3>
          <p className="serving-details-section__description">
            Define how much this recipe makes and how it should be portioned
          </p>
        </div>

        <div className="serving-details-section__yield-grid">
          {/* Recipe Yield */}
          <div className="serving-details-section__yield-field">
            <div className="serving-details-section__yield-input-group">
              <CustomTextField
                type="number"
                label="Recipe Yield"
                value={serving?.yield?.value || ''}
                onChange={(e) =>
                  updateYield('value', parseFloat(e?.target?.value) || 0)
                }
                inputProps={{ step: 0.1, min: 0 }}
                placeholder="0"
                className="serving-details-section__yield-value"
                fullWidth
              />
              <CustomSelect
                label="&nbsp;"
                // value={serving?.yield?.unit || 'servings'}
                onChange={(e) => updateYield('unit', e?.value || 'servings')}
                className="serving-details-section__yield-unit"
                options={yieldUnits}
                value={
                  yieldUnits?.find((item) => {
                    return item?.value === serving?.yield?.unit;
                  }) || ''
                }
                isClearable={false}
              />
            </div>
          </div>

          {/* Total Portions */}
          <div className="">
            <CustomTextField
              label="Total Portions"
              type="number"
              value={serving?.totalPortions || ''}
              onChange={(e) =>
                updateServing('totalPortions', parseInt(e?.target?.value) || 0)
              }
              inputProps={{ min: 1 }}
              placeholder="0"
              fullWidth
            />
          </div>

          {/* Single Portion Size */}
          <div className="">
            <CustomTextField
              label="Single Portion Size"
              type="number"
              value={serving?.singlePortionSize?.toFixed?.(2) || ''}
              placeholder="Auto-calculated"
              helperText="Automatically calculated from yield ÷ portions"
              InputProps={{
                readOnly: true,
                endAdornment: (
                  <span className="serving-details-section__portion-size-unit">
                    {serving?.yield?.unit || 'servings'}
                  </span>
                ),
              }}
              disabled
              fullWidth
            />
          </div>
        </div>
      </div>

      {/* Serving Instructions */}
      <div className="serving-details-section__serving-section">
        <div className="serving-details-section__header">
          <h3 className="serving-details-section__title">
            Serving Instructions
          </h3>
          <p className="serving-details-section__description">
            How should this dish be served and presented?
          </p>
        </div>

        <div className="serving-details-section__serving-grid">
          <div className="">
            <CustomSelect
              label="Serving Method"
              fullWidth
              options={servingMethods}
              value={
                servingMethods?.find((item) => {
                  return item?.value === serving?.servingMethod;
                }) || ''
              }
              onChange={(e) => updateServing('servingMethod', e?.value || '')}
            />
          </div>

          <div className="">
            <CustomSelect
              label="Serve In"
              fullWidth
              options={serveInOptions}
              value={
                serveInOptions?.find((item) => {
                  return item?.value === serving?.serveIn;
                }) || ''
              }
              onChange={(e) => updateServing('serveIn', e?.value || '')}
            />
          </div>
        </div>

        <div className="">
          <CustomTextField
            label="Garnish & Presentation"
            value={serving?.garnish || ''}
            onChange={(e) => updateServing('garnish', e?.target?.value || '')}
            placeholder="Describe garnish, plating, and presentation details..."
            multiline
            rows={3}
            fullWidth
          />
        </div>
      </div>

      {/* Professional Tips */}
      <div className="serving-details-section__tips-section">
        <div className="serving-details-section__header">
          <h3 className="serving-details-section__title">Professional Tips</h3>
          <p className="serving-details-section__description">
            Share insights for front-of-house and kitchen staff
          </p>
        </div>

        <div className="serving-details-section__tips-grid">
          <div className="serving-details-section__tip-group">
            <div className="serving-details-section__tip-header">
              <Icon name="Users" size={16} color="#10B981" />
              <span className="serving-details-section__tip-label">
                Front of House Tips
              </span>
            </div>
            <div className="serving-details-section__tip-info serving-details-section__tip-info--foh">
              <Icon
                name="Info"
                size={14}
                className="serving-details-section__tip-info-icon"
              />
              <p className="serving-details-section__tip-info-text serving-details-section__tip-info-text--foh">
                <span className="serving-details-section__tip-strong">
                  Tips for servers:
                </span>{' '}
                Include information about allergens, temperature, timing, and
                customer preferences.
              </p>
            </div>
            <CustomTextField
              value={serving?.fohTips || ''}
              onChange={(e) => updateServing('fohTips', e?.target?.value || '')}
              placeholder="Add tips for front-of-house staff..."
              multiline
              rows={4}
              fullWidth
            />
          </div>

          <div className="serving-details-section__tip-group">
            <div className="serving-details-section__tip-header">
              <Icon name="ChefHat" size={16} color="#F59E0B" />
              <span className="serving-details-section__tip-label">
                Chef Tips
              </span>
            </div>
            <div className="serving-details-section__tip-info serving-details-section__tip-info--chef">
              <Icon
                name="Lightbulb"
                size={14}
                className="serving-details-section__tip-info-icon"
              />
              <p className="serving-details-section__tip-info-text serving-details-section__tip-info-text--chef">
                <span className="serving-details-section__tip-strong">
                  Kitchen insights:
                </span>{' '}
                Share professional techniques, timing, quality checks, and
                troubleshooting.
              </p>
            </div>
            <CustomTextField
              value={serving?.chefTips || ''}
              onChange={(e) =>
                updateServing('chefTips', e?.target?.value || '')
              }
              placeholder="Add professional chef tips..."
              multiline
              rows={4}
              fullWidth
            />
          </div>
        </div>
      </div>

      {/* Serving Summary */}
      <div className="serving-details-section__summary">
        <h4 className="serving-details-section__summary-title">
          Serving Summary
        </h4>
        <div className="serving-details-section__summary-grid">
          <div className="serving-details-section__summary-item">
            <div className="serving-details-section__summary-value">
              {serving?.yield?.value || 0} {serving?.yield?.unit || 'servings'}
            </div>
            <div className="serving-details-section__summary-label">
              Total Yield
            </div>
          </div>
          <div className="serving-details-section__summary-item">
            <div className="serving-details-section__summary-value">
              {serving?.totalPortions || 0}
            </div>
            <div className="serving-details-section__summary-label">
              Portions
            </div>
          </div>
          <div className="serving-details-section__summary-item">
            <div className="serving-details-section__summary-value">
              {serving?.singlePortionSize?.toFixed?.(1) || 0}
            </div>
            <div className="serving-details-section__summary-label">
              Per Portion
            </div>
          </div>
          <div className="serving-details-section__summary-item">
            <div className="serving-details-section__summary-value">
              {serving?.servingMethod ? '✓' : '—'}
            </div>
            <div className="serving-details-section__summary-label">
              Method Set
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServingDetailsSection;
