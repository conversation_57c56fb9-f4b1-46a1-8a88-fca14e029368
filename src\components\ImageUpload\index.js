'use client';
import React from 'react';
import { Box, Typography } from '@mui/material';
import { useDropzone } from 'react-dropzone';
import CloudUploadOutlinedIcon from '@mui/icons-material/CloudUploadOutlined';
import HeaderImage from '@/components/UI/ImageSecurity';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import './imageupload.scss';

export default function ImageUpload({
  previewImage,
  setPreviewImage,
  setFieldValue,
  fieldName,
  touched,
  errors,
}) {
  const { getRootProps, getInputProps } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.length) {
        const file = acceptedFiles[0];
        setPreviewImage({ url: URL.createObjectURL(file) }); // Generate a preview URL
        // setPreviewImage(file); // Generate a preview URL
        setFieldValue(fieldName, file);
        // onImageUpload(file);
      }
    },
    accept: 'image/*',
  });

  return (
    <Box className="w100">
      <Box className="d-flex logo-section-wrap">
        <Box className="logo-wrap">
          {previewImage?.url ? (
            <HeaderImage
              imageUrl={previewImage?.url}
              alt="users"
              style={{ marginRight: '4px' }}
              className=""
              type="avtar"
            />
          ) : (
            <AccountCircleIcon className="profile-image" />
          )}
        </Box>

        <Box className="upload-sec">
          <Box {...getRootProps()} className="upload-area">
            <CloudUploadOutlinedIcon />
            <input {...getInputProps()} />
            <Typography className="p14 upload-text">
              Drop your image here
            </Typography>
          </Box>
        </Box>
      </Box>

      {!previewImage?.url && touched.fieldName && errors.fieldName && (
        <Typography variant="body2" color="error" className="field-error">
          This field is required
        </Typography>
      )}
    </Box>
  );
}
