@import '@/styles/variable.scss';

// .select-input-wrap {
//   z-index: 99999 !important;
//   animation-fill-mode: none !important;

//   .select-all-wrap {
//     padding: 5px;
//     color: $color-primary;
//     font-size: 14px;
//     cursor: pointer;
//   }

//   [class*='__selected'] {
//     background-color: $color-platinum !important;
//     box-shadow: none !important;

//     [class*='__selected'] {
//       &::before {
//         background-color: $color-primary;
//         border-color: $color-primary !important;
//       }
//     }
//   }

//   .___SSlider_v6mlw_gg_ {
//     background-color: $color-primary !important;
//     width: 4px;
//   }

//   .apply-btn {
//     background-color: $color-primary !important;
//   }
// }
