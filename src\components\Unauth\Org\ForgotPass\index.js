'use client';

import React, { useContext } from 'react';
import * as Yup from 'yup';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { Box, InputAdornment, TextField, Typography } from '@mui/material';
import { useRouter } from 'next/navigation';
import { Formik, Form } from 'formik';
import { saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import {
  EmailIcon,
  ForgotLeftVector,
  ForgotRightVector,
} from '@/helper/common/images';
import CustomButton from '@/components/UI/button';
import './forgotpass.scss';

export default function Forgotpassword() {
  const { setAuthState } = useContext(AuthContext);
  const router = useRouter();
  // const [loader, setLoader] = useState(false);
  // const isOrg = false;
  const handleNavigate = () => {
    router.push('/org/login');
  };
  return (
    <>
      <Formik
        initialValues={{
          email: '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          email: Yup.string()
            .required('This field is required')
            .matches(
              /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
              'Please enter valid email'
            ),
        })}
        onSubmit={async (requestData) => {
          let sendData;
          sendData = {
            email: requestData?.email,
          };

          try {
            // setLoader(true);
            const { status, data } = await axiosInstance.post(
              ORG_URLS.FORGOT_PASSWORD,
              sendData
            );
            if (status === 200) {
              if (data.status) {
                setApiMessage('success', data?.message);
                saveToStorage(identifiers?.EMAIL, requestData?.email);
                saveToStorage(identifiers?.USER_ID, data?.data);
                setAuthState({ Email: requestData?.email });
                router.push(`/org/verify-email`);
              } else {
                setApiMessage('error', data?.message);
              }
              // setLoader(false);
            }
          } catch (error) {
            setApiMessage('error', error?.response?.data?.message);
            // setLoader(false);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          // dirty,
          // isValid,
        }) => (
          <Box className="forgot-pass-wrap">
            <Box className="forgot-pass-container">
              <Box className="forgot-pass">
                <Typography
                  className="forgot-pass-text heading-text"
                  variant="h4"
                >
                  Forgot Your Password?
                </Typography>
                <Typography
                  component="p"
                  className="pass-sub-text sub-heading-text"
                >
                  Please enter your email Id. We will send a code to your email
                </Typography>
                <Form onSubmit={handleSubmit}>
                  <Box className="forgot-pass-input-wrap">
                    <Typography
                      className="lable-wrap un-auth-label-wrap"
                      variant="h6"
                    >
                      Email Address*
                    </Typography>
                    <TextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="email"
                      name="email"
                      value={values?.email}
                      placeholder="Email address"
                      error={Boolean(touched.email && errors.email)}
                      helperText={touched.email && errors.email}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      fullWidth
                      className="forgot-pass-input"
                      variant="standard"
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <Box className="email-icon-wrap">
                              <EmailIcon />
                            </Box>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>
                  <Box className="submit-btn-wrap">
                    <CustomButton
                      // disabled={!isValid || !dirty}
                      fullWidth
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#39596e"
                      className="submit-btn"
                      colorhover="#FFFFFF"
                      fontWeight="600"
                      type="submit"
                      isLogin={true}
                      title="Submit"
                    />
                  </Box>
                  <Typography className="remember-pass" component="p">
                    Remember your password?
                    <span
                      className="login-wrap"
                      onClick={() => handleNavigate()}
                    >
                      Log In
                    </span>
                  </Typography>
                </Form>
              </Box>
              <Box className="left-vector-wrap">
                <ForgotLeftVector className="left-vector" />
              </Box>
              <Box className="right-vector-wrap">
                <ForgotRightVector className="left-vector" />
              </Box>
            </Box>
          </Box>
        )}
      </Formik>
    </>
  );
}
