import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';

export const getResignationList = async (
  page,
  searchValue,
  filter,
  rowsPerPage
) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_RESIGNATION_LIST +
        `?search=${searchValue}&size=${rowsPerPage}&page=${page}&branch_id=${filter?.branch}&department_id=${
          filter?.department
        }&status=${filter?.status}&role_id=${filter?.role}&applied_date=${
          filter?.appliedDate
            ? dayjs(filter?.appliedDate)?.format('YYYY-MM-DD')
            : ''
        }&updated_date=${
          filter?.updatedDate
            ? dayjs(filter?.updatedDate)?.format('YYYY-MM-DD')
            : ''
        }&last_serving_date=${
          filter?.lastServingDate
            ? dayjs(filter?.lastServingDate)?.format('YYYY-MM-DD')
            : ''
        }`
    );

    if (status === 200 || status === 201) {
      return {
        success: true,
        data: data?.resignationList || [],
        page: data?.page,
        count: data?.count,
      };
    }
    return {
      success: false,
      data: [],
      count: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getOneResignation = async (id) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_ONE_USER_RESIGNATION + `?resignation_id=${id}`
    );
    if (status === 200) {
      return {
        success: true,
        data: data?.data,
      };
    }
    return {
      success: false,
      data: null,
    };
  } catch (error) {
    throw error;
  }
};

export const updateResignation = async (id, payload) => {
  try {
    const { status, data } = await axiosInstance.put(
      URLS?.UPDATE_RESIGNATION + `${id}`,
      payload
    );
    if (status === 200 || status === 201) {
      return {
        success: true,
        message: data?.message,
      };
    }
    return {
      success: false,
    };
  } catch (error) {
    throw error;
  }
};

export const getResignationDetails = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_RESIGNATION_DETAILS
    );
    if (status === 200) {
      return {
        success: true,
        data: data?.data,
      };
    }
    return {
      success: false,
      data: null,
    };
  } catch (error) {
    throw error;
  }
};

export const sendResignation = async (payload) => {
  try {
    const { status, data } = await axiosInstance.post(
      URLS.SEND_RESIGNATION,
      payload
    );
    if (status === 200 || status === 201) {
      return {
        success: true,
        message: data?.message,
      };
    }
    return {
      success: false,
      message: data?.message,
    };
  } catch (error) {
    throw error;
  }
};

export const getLeavingChecklist = async (id) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_LEAVING_CHECKLIST + `${id}`
    );
    if (status === 200) {
      return {
        success: true,
        data: data?.data,
      };
    }
    return {
      success: false,
      data: null,
    };
  } catch (error) {
    throw error;
  }
};

export const verifyLeavingChecklist = async (id, payload) => {
  try {
    const { status, data } = await axiosInstance.put(
      URLS?.VERIFY_LEAVING_CHECKLIST + `${id}`,
      payload
    );
    if (status === 200) {
      return {
        success: true,
        message: data?.message,
      };
    }
    return {
      success: false,
      message: data?.message,
    };
  } catch (error) {
    throw error;
  }
};

export const updateResignationRemark = async (id, payload) => {
  try {
    const { status, data } = await axiosInstance.put(
      URLS?.UPDATE_RESIGNATION + `${id}`,
      payload
    );
    if (status === 200 || status === 201) {
      return {
        success: true,
        message: data?.message,
      };
    }
    return {
      success: false,
      message: data?.message,
    };
  } catch (error) {
    throw error;
  }
};
