'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';
import { Box } from '@mui/material';
import './layout.scss';

export default function PublicLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  // const [authdata, setAuthData] = useState(true);
  useEffect(() => {
    if (pathname === '/org/login') {
      localStorage.clear();
    } else if (checkOrganizationRole('org_master')) {
      router?.push('/org/organization');
    } else if (checkOrganizationRole('super_admin')) {
      router?.push('/sorg/organization');
    } else if (checkOrganizationRole('staff')) {
      router.push('/myprofile');
    } else if (
      localStorage.getItem(identifiers?.AUTH_DATA) &&
      JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)) &&
      (pathname === '/login' ||
        pathname === '/otp' ||
        pathname === '/forgot-password' ||
        pathname === '/reset-password' ||
        pathname === '/resetpassword')
    ) {
      router?.push('/chart-dashboard');
    }

    // setAuthData(JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)));
  }, []);
  return <Box className="main-container">{children}</Box>;
}
