@import '@/styles/variable.scss';

.support-ticket-wrap {
    .new-ticket-wrap {
        color: $color-primary;
    }

    .select-field-wrap {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        column-gap: 15px;
        row-gap: 15px;

        @media(max-width:767px) {
            grid-template-columns: repeat(1, 1fr);
        }
    }

    .slected-wrap {
        width: 100%;
    }

    .text-input-wrap {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        column-gap: 15px;
        row-gap: 15px;

        @media(max-width:992px) {
            grid-template-columns: repeat(2, 1fr);
            row-gap: 30px
        }

        @media(max-width:767px) {
            grid-template-columns: repeat(1, 1fr);
        }

    }

    .MuiInputBase-root {
        min-height: 37px !important;

        .MuiInputBase-input {
            padding: 7px 16px !important;
        }
    }

    .discription-text {
        margin-bottom: 2px !important;
    }

    .upload-sec {
        width: 100%;
        border: 1px solid $color-Dark-10;
        height: 110px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;

        .upload-area {
            height: 100%;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .upload-text {
                color: $color-primary;
                padding: 10px;
            }
        }

        svg {
            width: 30px;
            height: 30px;
            fill: $color-primary !important;
        }

        .upload-text {
            color: $color-primary;
        }
    }

    .media-previews {
        margin-top: 16px;

        .preview-container {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            position: relative;
            border: 1px solid #d9dae2;

            .close-icon-wrap {
                position: absolute;
                line-height: 0px;
                top: 0px;
                right: 0px;
            }

            .image-container,
            .video-container {
                box-shadow: 0px 0px 5px lightgray;
                line-height: 0px;
            }

            .preview-img {
                width: 100px;
                height: 100px;
                object-fit: cover;
                cursor: pointer;
                border-radius: 8px;

                &:hover {
                    opacity: 0.8;
                }
            }
        }
    }

    .create-ticket-wrap {

        .create-ticket-btn,
        .cancel-ticket-btn {
            padding: 6px 18px !important;
            font-size: 16px !important;
            font-weight: 500;
        }

        .create-ticket-btn {
            &:hover {
                color: white !important;
                box-shadow: none !important;
            }
        }

        .cancel-ticket-btn {
            background-color: $color-secondary !important;
            color: $color-primary !important;

            &:hover {
                box-shadow: none !important;
            }
        }

        @media(max-width:575px){
            justify-content: center !important;
        }
    }

    .error {
        color: #d32f2f !important;
        font-family: Inter, sans-serif !important;
        font-size: 12px !important;
        line-height: 18px !important;
        letter-spacing: -0.5px !important;
        font-weight: 600 !important;

        fieldset {
            border: 1px solid #d32f2f !important;
        }
    }
}