'use client';
import React, { useEffect, useState, useRef } from 'react';
import { Box, Stepper, Step, StepLabel, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import LeaveAccrual from '@/components/Leave/AddLeavePolicy/LeaveAccrual';
import LeaveApplicationRules from '@/components/Leave/AddLeavePolicy/LeaveApplicationRules';
import HolidaysAndWeekends from '@/components/Leave/AddLeavePolicy/HolidaysAndWeekends';
import LeaveApproval from '@/components/Leave/AddLeavePolicy/LeaveApproval';
import LeaveRestriction from '@/components/Leave/AddLeavePolicy/LeaveRestriction';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { useRouter, useSearchParams } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import _ from 'lodash';
import dayjs from 'dayjs';
import './addleavepolicy.scss';

// Main Component
export default function AddLeavePolicyComp({ isEdit, policyid, getLeaveList }) {
  const [activeStep, setActiveStep] = useState(0);
  const [leaveTypeList, setLeaveTypeList] = useState();
  const [leaveType, setLeaveType] = useState();
  const [save, setSave] = useState();
  const [staffList, setStaffList] = useState([]);
  const [random, setRandom] = useState('');
  const [encashment, setEncashment] = useState([
    {
      encashment_number: '',
      encashment_max_limit: '',
      encashment_leave_balance_exceeds: '',
    },
  ]);
  const [resetTime, setResetTime] = useState({
    type: 'yearly',
    reset_value: [
      {
        on_date: 31,
        month: 12,
      },
    ],
  });
  const [accrualTime, setAccrualTime] = useState({
    type: 'yearly',
    reset_value: [
      {
        on_date: 1,
        month: 1,
        days: 0,
      },
    ],
  });
  const [prior_leave, setPrior_leave] = useState([
    {
      name: '',
      type: '',
      limit: '',
    },
  ]);
  const [leavePolicySubmit, setLeavePolicySubmit] = useState({
    leaveAccrual: false,
    leaveApplicationRules: false,
    leaveRestriction: false,
    holidaysAndWeekends: false,
    leaveApproval: false,
  });
  const [leavePolicyBack, setLeavePolicyBack] = useState({
    leaveAccrual: false,
    leaveApplicationRules: false,
    leaveRestriction: false,
    holidaysAndWeekends: false,
    leaveApproval: false,
  });
  const [leavePolicyDetails, setLeavePolicyDetails] = useState({
    leaveAccrual: {},
    leaveApplicationRules: {},
    leaveRestriction: {},
    holidaysAndWeekends: {},
    leaveApproval: {},
  });
  const router = useRouter();
  const searchParams = useSearchParams();
  // const id = searchParams.get('id');
  const id = searchParams.get('policy_id');
  const isAnuual = searchParams.get('is_anuual');
  const formikRefAccrual = useRef(null);
  const formikRefAppRule = useRef(null);
  const formikRefRestriction = useRef(null);
  const formikRefHoliday = useRef(null);
  const formikRefApproval = useRef(null);

  const leaveStep1Payload = (values) => {
    return {
      leave_policy_name: values?.policyName,
      leave_type_id: values?.leaveType,
      leave_calender_year_start_from: dayjs(
        values?.leaveCalendarStartYear
      ).format('YYYY-MM'),
      leave_policy_description: values?.description,
      leave_policy_end_date: values?.leaveCalendarEndYear
        ? dayjs(values?.leaveCalendarEndYear).format('YYYY-MM-DD') !==
          'Invalid Date'
          ? dayjs(values?.leaveCalendarEndYear).format('YYYY-MM-DD')
          : null
        : null,
      has_leave_policy_default: values?.defaultLeavePolicy,
      salary_deduction_hours: values?.salaryDed ? values?.salaryDed : 1,
      effective_after_count: values?.effectiveDays ? values?.effectiveDays : 0,
      effective_after_type: values?.effectiveTimePeriod, // 'days','months'
      effective_from_type: values?.effectiveFrom, // 'date_of_joining','after_internship_end','after_probation_end'
      new_employee_leave_entitlement_count: values?.newEmpLeaveDay
        ? values?.newEmpLeaveDay
        : 0,
      new_employee_leave_entitlement_type: values?.newEmpLeaveType, // 'days', 'months'
      new_employee_leave_entitlement_their_type: values?.newEmpLeaveTheirType, // 'date_of_joining','after_internship_end','after_probation_end'
      ...(values?.effectiveFrom === 'date_of_joining' &&
        values?.newEmpLeaveTheirType === 'date_of_joining' && {
          has_employee_leave_in_probation: values?.probation,
          probation_leave_days_count: values?.probationLimit
            ? values?.probationLimit
            : 0,
        }),
      leave_policy_accural: values?.accrual,
      ...(values?.accrual && {
        stop_policy_accural: values?.stopaccural,
        stop_policy_accural_limit: values?.stopaccurallimit
          ? values?.stopaccurallimit
          : 0,
        ...(isAnuual === 'true' && {
          leave_balance_based_on_emp_contract: values?.empcontract,
        }),
        stop_policy_accural_timewise_type: values?.accrualTimewise, // 'yearly','monthly','quarterly','half_yearly','one_time',
        stop_policy_accural_timewise_value: accrualTime?.reset_value,
        leave_accural_based_on_contract:
          values?.considerLeaveAccrual === 'yes'
            ? true
            : values?.considerLeaveAccrual === 'no'
              ? false
              : '',
        earned_leave_accural_based: values?.accrualBasedLeave,
        restrict_leave_accural_on_emp_exit: values?.restrictLeaveAccrual,
        pro_rate_accural: values?.prorateAccrual, // 'pro_rate', 'non_pro_rate'
      }),

      leave_policy_reset: values?.reset,
      ...(values?.reset && {
        leave_policy_reset_type: values?.resetTimePeriod, // 'yearly','monthly','quarterly','half_yearly','one_time'
        leave_policy_reset_value: resetTime?.reset_value,
        prioritize_encasment_carry_forward: values?.encashOrCarryForward, // 'encashment', 'carry_forward'
        deduct_encashment_amount: values?.deductEncashAmount,
        perquisite_value_calculated: values?.leavePaymentMethod, // 'fixed_amount', 'formula_based'
        encashment_multiply_by_number: values?.encashmentMultiplier
          ? values?.encashmentMultiplier
          : 0,
        restrict_encashment_emp_notice_period: values?.encashmentRestriction,
        allow_for_anytime_encashment: values?.allowEncashmentAnytime,
        allow_for_anytime_encashment_value: values?.allowEncashmentAnytime
          ? {
              encashment_allowed: values?.encashMentAllowed, // "true","false",
              encashment_count: values?.encashMentAllowedDays,
              encashment_time: values?.encashMentAllowedIn, // "week","month","year"
              encashment_submitted_between_period:
                values?.encashmentAllowedPeriods, // "true","false",
              encashment_submitted_start_month: values?.encashmentAllowedStart,
              encashment_submitted_end_month: values?.encashmentAllowedEnd,
              probation_period_user_cannot_encashment:
                values?.employeesInProbation,
              encashment_number_by_type: values?.employeeCanEncash, //  "percentage","unit","maintaing_leave_days"
              encashment_exceed_data: encashment,
              encashment_require_approval:
                values?.encashmentRequestApproval === 'yes'
                  ? true
                  : values?.encashmentRequestApproval === 'no'
                    ? false
                    : '',
            }
          : {
              encashment_number: values?.encashmentDays
                ? values?.encashmentDays
                : 0,
              encashment_number_by_type: values?.encashmentPercentage, // "percentage","unit"
              encashment_max_limit: values?.encashmentMaxLimit,
              encashment_min_limit: values?.encashmentMinLimit,
            },
        carry_forward_date: {
          carry_forward_number: values?.carryForwardDays
            ? values?.carryForwardDays
            : 0,
          carry_forward_number_by_type: values?.carryForwardPercentage, // "percentage","unit"
          carry_forward_max_limit: values?.carryForwardMaxLimit,
          carry_forward_min_limit: values?.carryForwardMinLimit,
        },
      }),
    };
  };
  const leaveStep2Payload = (values) => {
    return {
      restrict_by_gender: values?.restrictTo,
      restrict_by_gender_status: values?.restrictedGender, // 'male','female','other'
      restrict_by_marital: values?.employeeRestriction,
      restrict_by_marital_status: values?.employeeRestrictionStatus, // 'married', 'unmarried'
      round_of_decimal_leave_balance: values?.roundingOption, // 'no', 'full_day', 'half_day'
      allow_leave_request_data: {
        past_date: values?.pastDates,
        ...(values?.pastDates && {
          past_date_status: values?.past,
          past_date_days: values?.pastDays ? values?.pastDays : 0,
        }),
        future_date: values?.futureDates,
        ...(values?.futureDates && {
          future_date_status: values?.next,
          future_date_days: values?.nextDays ? values?.nextDays : 0,
          apply_in_advance_days_status: values?.toBeApplied,
          apply_in_advance_days: values?.toBeAppliedDays
            ? values?.toBeAppliedDays
            : 0,
        }),
      },
    };
  };
  const leaveStep3Payload = (values) => {
    return {
      has_maximum_consecutive: values?.maxConsecutiveDays,
      maximum_consecutive_count: values?.maxConsecutiveNoDays
        ? values?.maxConsecutiveNoDays
        : 0,
      has_allow_gap_between_leave: values?.minGapInDays,
      gap_between_leave_application: values?.minGapBetweenApplications
        ? values?.minGapBetweenApplications
        : 0,
      emp_can_apply_in_notice_period: values?.employeeCanConsume,
      extend_notice_period: values?.employeeCanExtend,
      allow_view_for_same_leave_type: values?.allowEmpSameLeave,
      maximum_leave_allowed_for_period_count: values?.maxApplications
        ? values?.maxApplications
        : 0,
      maximum_leave_allowed_for_period_type: values?.employeeTimePeriod, // 'weekly','monthly','quarterly', 'yearly'
    };
  };
  const leaveStep4Payload = (values) => {
    return {
      holiday_between_leave_period: values?.holidaysDontCount, // 'dont_count', 'count_as_leave'
      holiday_between_leave_period_data: {
        holiday_accompaying_days: values?.AccompayingDays
          ? values?.AccompayingDays
          : 0,
        configure_sandwich_days: values?.sandwichRuleDuration
          ? values?.sandwichRuleDuration
          : 0,
        apply_sandwich_before_holiday: values?.sandwichBeforeHoliday,
        apply_sandwich_after_holiday: values?.sandwichAfterHoliday,
        apply_sandwich_between_holiday: values?.leaveBetweenHoliday,
      },
      weekend_between_leave_period: values?.weekendsDontCount, // 'dont_count', 'count_as_leave'
      weekend_between_leave_period_data: {
        weekend_accompaying_days: values?.sandwichRuleDaysForWeekend
          ? values?.sandwichRuleDaysForWeekend
          : 0,
        configure_sandwich_days: values?.sandwichRuleDurWeekend
          ? values?.sandwichRuleDurWeekend
          : 0,
        apply_sandwich_before_weekend: values?.sandwichBeforeWeekend,
        apply_sandwich_after_weekend: values?.sandwichAfterWeekend,
        apply_sandwich_between_weekend: values?.leaveBetweenWeekend,
      },
    };
  };

  const steps = [
    {
      label: 'Leave Accrual',
      component: (
        <LeaveAccrual
          leaveTypeList={leaveTypeList}
          leavePolicyDetails={leavePolicyDetails}
          setLeavePolicyDetails={setLeavePolicyDetails}
          formikRefAccrual={formikRefAccrual}
          setLeavePolicySubmit={setLeavePolicySubmit}
          leavePolicySubmit={leavePolicySubmit}
          LeaveAccrualPayload={leaveStep1Payload}
          encashment={encashment}
          setEncashment={setEncashment}
          random={random}
          setRandom={setRandom}
          resetTime={resetTime}
          setResetTime={setResetTime}
          accrualTime={accrualTime}
          setAccrualTime={setAccrualTime}
          leavePolicyBack={leavePolicyBack}
          setLeavePolicyBack={setLeavePolicyBack}
          id={id}
          leaveType={leaveType}
          isAnuual={isAnuual}
        />
      ),
    },
    {
      label: 'Leave Application Rules',
      component: (
        <LeaveApplicationRules
          leavePolicyDetails={leavePolicyDetails}
          setLeavePolicyDetails={setLeavePolicyDetails}
          formikRefAppRule={formikRefAppRule}
          setLeavePolicySubmit={setLeavePolicySubmit}
          leavePolicySubmit={leavePolicySubmit}
          LeaveStep2Payload={leaveStep2Payload}
          prior_leave={prior_leave}
          setPrior_leave={setPrior_leave}
          leavePolicyBack={leavePolicyBack}
          setLeavePolicyBack={setLeavePolicyBack}
          leaveType={leaveType}
        />
      ),
    },
    {
      label: 'Leave Restriction',
      component: (
        <LeaveRestriction
          leavePolicyDetails={leavePolicyDetails}
          setLeavePolicyDetails={setLeavePolicyDetails}
          formikRefRestriction={formikRefRestriction}
          leaveTypeList={leaveTypeList}
          setLeavePolicySubmit={setLeavePolicySubmit}
          leavePolicySubmit={leavePolicySubmit}
          LeaveStep3Payload={leaveStep3Payload}
          leavePolicyBack={leavePolicyBack}
          setLeavePolicyBack={setLeavePolicyBack}
          leaveType={leaveType}
        />
      ),
    },
    {
      label: 'Holidays and Weekends',
      component: (
        <HolidaysAndWeekends
          leavePolicyDetails={leavePolicyDetails}
          setLeavePolicyDetails={setLeavePolicyDetails}
          formikRefHoliday={formikRefHoliday}
          setLeavePolicySubmit={setLeavePolicySubmit}
          leavePolicySubmit={leavePolicySubmit}
          LeaveStep4Payload={leaveStep4Payload}
          leavePolicyBack={leavePolicyBack}
          setLeavePolicyBack={setLeavePolicyBack}
          leaveType={leaveType}
        />
      ),
    },
    {
      label: 'Leave Approval',
      component: (
        <LeaveApproval
          leavePolicyDetails={leavePolicyDetails}
          setLeavePolicyDetails={setLeavePolicyDetails}
          formikRefApproval={formikRefApproval}
          staffList={staffList}
          setStaffList={setStaffList}
          setLeavePolicySubmit={setLeavePolicySubmit}
          leavePolicySubmit={leavePolicySubmit}
          leavePolicyBack={leavePolicyBack}
          setLeavePolicyBack={setLeavePolicyBack}
          leaveType={leaveType}
        />
      ),
    },
  ];
  const stringifyFormat = (value) => {
    return JSON.stringify(value);
  };
  const parseFormat = (value) => {
    return JSON.parse(value);
  };

  // add/update leave policy
  const handleAddUpdateLeavePolicy = async (
    accural,
    application,
    restriction,
    holiday,
    approval
  ) => {
    // step 1
    let accuralformat = accural ? accural : leavePolicyDetails?.leaveAccrual;
    accuralformat.stop_policy_accural_timewise_value = stringifyFormat(
      accuralformat.stop_policy_accural_timewise_value
    );
    accuralformat.leave_policy_reset_value = stringifyFormat(
      accuralformat.leave_policy_reset_value
    );
    accuralformat.allow_for_anytime_encashment_value = stringifyFormat(
      accuralformat.allow_for_anytime_encashment_value
    );
    accuralformat.carry_forward_date = stringifyFormat(
      accuralformat.carry_forward_date
    );
    // step 2
    let appruleformat = application
      ? application
      : leavePolicyDetails?.leaveApplicationRules;
    appruleformat.allow_leave_request_data = stringifyFormat(
      appruleformat.allow_leave_request_data
    );
    // step 3
    let restrictionformats = restriction
      ? restriction
      : leavePolicyDetails?.leaveRestriction;
    // step 4
    let holidayWeekendformat = holiday
      ? holiday
      : leavePolicyDetails?.holidaysAndWeekends;
    holidayWeekendformat.holiday_between_leave_period_data = stringifyFormat(
      holidayWeekendformat?.holiday_between_leave_period_data
    );
    holidayWeekendformat.weekend_between_leave_period_data = stringifyFormat(
      holidayWeekendformat?.weekend_between_leave_period_data
    );

    // step 5
    let approvalformats = approval
      ? approval
      : leavePolicyDetails?.leaveApproval;
    const requestData = {
      leave_type_id: id,
      leave_accural_form: accuralformat,
      leave_application_rules_form: appruleformat,
      leave_restricition_form: restrictionformats,
      holiday_weekend_form: holidayWeekendformat,
      leave_approval_form: approvalformats,
    };
    const method = !isEdit ? 'post' : 'put';
    const ApiUrl = !isEdit
      ? URLS.ADD_LEAVE_POLICY
      : URLS.UPDATE_LEAVE_POLICY + `${policyid}`;
    try {
      const { status, data } = await axiosInstance[method](ApiUrl, requestData);
      if (status === 200 || status === 201) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getLeaveList('', true);
          setTimeout(() => {
            router.back();
          }, 1000);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // get leave type
  const getLeaveTypeList = async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_LEAVE_TYPE);
      if (status === 200) {
        let filterUserList = data?.data?.map((user) => ({
          label: user?.name,
          value: user?.id,
        }));
        setLeaveTypeList(filterUserList);
        const selectedType = data?.data?.find((d) => d?.id === Number(id));
        setLeaveType(selectedType);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // List of user list
  const getUserList = async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_USER_LIST);

      if (status === 200) {
        const alloption = [{ label: 'Select all', value: 'all' }];
        let filterUserList = data?.userList?.map((user) => ({
          label: user?.user_full_name,
          value: user?.id,
        }));
        let mergeList = _.concat(alloption, filterUserList);
        setStaffList(mergeList);
      }
    } catch (error) {
      setStaffList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // get leave policy
  const getLeavePoliciesData = async (id) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_POLICY_BY_ID + `${id}`
      );
      if (status === 200 || status === 201) {
        const policyData = data?.data;
        let accuralformats = policyData?.leave_accural_form;
        accuralformats.stop_policy_accural_timewise_value = parseFormat(
          accuralformats.stop_policy_accural_timewise_value
        );
        accuralformats.leave_policy_reset_value = parseFormat(
          accuralformats.leave_policy_reset_value
        );
        accuralformats.allow_for_anytime_encashment_value = parseFormat(
          accuralformats.allow_for_anytime_encashment_value
        );
        accuralformats.carry_forward_date = parseFormat(
          accuralformats.carry_forward_date
        );
        [
          'createdAt',
          'updatedAt',
          'updated_by',
          'status',
          'created_by',
        ].forEach((key) => delete accuralformats[key]);
        let appruleformats = policyData?.leave_application_rules_form;
        appruleformats.allow_leave_request_data = parseFormat(
          appruleformats.allow_leave_request_data
        );
        ['createdAt', 'updatedAt'].forEach((key) => delete appruleformats[key]);
        let holidayWeekendformats = policyData?.holiday_weekend_form;
        holidayWeekendformats.holiday_between_leave_period_data = parseFormat(
          holidayWeekendformats.holiday_between_leave_period_data
        );
        holidayWeekendformats.weekend_between_leave_period_data = parseFormat(
          holidayWeekendformats.weekend_between_leave_period_data
        );
        ['createdAt', 'updatedAt'].forEach(
          (key) => delete holidayWeekendformats[key]
        );
        let restricitionformats = policyData?.leave_restricition_form;
        ['createdAt', 'updatedAt'].forEach(
          (key) => delete restricitionformats[key]
        );
        let approvalformats = policyData?.leave_approval_form;
        if (approvalformats?.leave_approval_meta_form[0]) {
          approvalformats.leave_approval_meta_form[0].user_id =
            approvalformats?.leave_approval_meta_form &&
            approvalformats?.leave_approval_meta_form?.length > 0 &&
            approvalformats?.leave_approval_meta_form[0].users
              ? approvalformats?.leave_approval_meta_form[0].users?.map(
                  (f) => f?.id
                )
              : [];
        } else {
          approvalformats.leave_approval_meta_form = [
            {
              user_id:
                approvalformats?.leave_approval_meta_form &&
                approvalformats?.leave_approval_meta_form?.length > 0 &&
                approvalformats?.leave_approval_meta_form[0].users
                  ? approvalformats?.leave_approval_meta_form[0].users?.map(
                      (f) => f?.id
                    )
                  : [],
            },
          ];
        }

        const pdata = {
          leaveAccrual: accuralformats,
          leaveApplicationRules: appruleformats,
          leaveRestriction: restricitionformats,
          holidaysAndWeekends: holidayWeekendformats,
          leaveApproval: approvalformats,
        };
        setLeavePolicyDetails(pdata);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
      router.push(`/leave-policy-type`);
    }
  };
  useEffect(() => {
    getUserList('', 1, '', '', '');
  }, []);
  useEffect(() => {
    id && getLeaveTypeList();
  }, [id]);
  useEffect(() => {
    policyid && getLeavePoliciesData(policyid);
  }, [policyid]);

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
    }
  };
  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };
  // Next buttons
  useEffect(() => {
    if (leavePolicySubmit?.leaveAccrual) {
      setTimeout(() => {
        if (Object.keys(formikRefAccrual?.current?.errors).length === 0) {
          handleNext();
        }
      }, 500);
    }
  }, [leavePolicySubmit?.leaveAccrual]);
  useEffect(() => {
    if (leavePolicySubmit?.leaveApplicationRules) {
      setTimeout(() => {
        if (Object.keys(formikRefAppRule?.current?.errors).length === 0) {
          handleNext();
        }
      }, 500);
    }
  }, [leavePolicySubmit?.leaveApplicationRules]);
  useEffect(() => {
    if (leavePolicySubmit?.leaveRestriction) {
      setTimeout(() => {
        if (Object.keys(formikRefRestriction?.current?.errors).length === 0) {
          handleNext();
        }
      }, 500);
    }
  }, [leavePolicySubmit?.leaveRestriction]);
  useEffect(() => {
    if (leavePolicySubmit?.holidaysAndWeekends) {
      setTimeout(() => {
        if (Object.keys(formikRefHoliday?.current?.errors).length === 0) {
          handleNext();
        }
      }, 500);
    }
  }, [leavePolicySubmit?.holidaysAndWeekends]);
  useEffect(() => {
    if (leavePolicySubmit?.leaveApproval) {
      setTimeout(() => {
        if (Object.keys(formikRefApproval?.current?.errors).length === 0) {
          const LeaveApprovalValues = formikRefApproval?.current?.values;

          let getIdss = LeaveApprovalValues?.individualuser?.map(
            (i) => i?.value
          );

          const leaveApproval = {
            leave_request_require_approval:
              LeaveApprovalValues?.approvalRequired === 'yes' ? true : false,
            leave_approval_meta_form: [
              {
                leave_policy_leval: 1,
                user_id: getIdss,
              },
            ],
          };

          handleAddUpdateLeavePolicy('', '', '', '', leaveApproval);
        }
      }, 500);
    }
  }, [leavePolicySubmit?.leaveApproval]);

  // Back Buttons
  useEffect(() => {
    if (leavePolicyBack?.leaveApplicationRules) {
      setTimeout(() => {
        if (Object.keys(formikRefAppRule?.current?.errors).length === 0) {
          handleBack();
        }
      }, 500);
    }
  }, [leavePolicyBack?.leaveApplicationRules]);
  useEffect(() => {
    if (leavePolicyBack?.leaveRestriction) {
      setTimeout(() => {
        if (Object.keys(formikRefRestriction?.current?.errors).length === 0) {
          handleBack();
        }
      }, 500);
    }
  }, [leavePolicyBack?.leaveRestriction]);
  useEffect(() => {
    if (leavePolicyBack?.holidaysAndWeekends) {
      setTimeout(() => {
        if (Object.keys(formikRefHoliday?.current?.errors).length === 0) {
          handleBack();
        }
      }, 500);
    }
  }, [leavePolicyBack?.holidaysAndWeekends]);
  useEffect(() => {
    if (leavePolicyBack?.leaveApproval) {
      setTimeout(() => {
        if (Object.keys(formikRefApproval?.current?.errors).length === 0) {
          handleBack();
        }
      }, 500);
    }
  }, [leavePolicyBack?.leaveApproval]);
  // On save buttons
  useEffect(() => {
    if (save === 0 && leavePolicySubmit?.leaveAccrual) {
      setTimeout(() => {
        if (Object.keys(formikRefAccrual?.current?.errors).length === 0) {
          handleAddUpdateLeavePolicy(
            leaveStep1Payload(formikRefAccrual?.current?.values),
            '',
            '',
            '',
            ''
          );
        }
      }, 500);
    } else if (save === 1 && leavePolicySubmit?.leaveApplicationRules) {
      setTimeout(() => {
        if (Object.keys(formikRefAppRule?.current?.errors).length === 0) {
          handleAddUpdateLeavePolicy(
            '',
            leaveStep2Payload(formikRefAppRule?.current?.values),
            '',
            '',
            ''
          );
        }
      }, 500);
    } else if (save === 2 && leavePolicySubmit?.leaveRestriction) {
      setTimeout(() => {
        if (Object.keys(formikRefRestriction?.current?.errors).length === 0) {
          handleAddUpdateLeavePolicy(
            '',
            '',
            leaveStep3Payload(formikRefRestriction?.current?.values),
            '',
            ''
          );
        }
      }, 500);
    } else if (save === 3 && leavePolicySubmit?.holidaysAndWeekends) {
      setTimeout(() => {
        if (Object.keys(formikRefHoliday?.current?.errors).length === 0) {
          handleAddUpdateLeavePolicy(
            '',
            '',
            '',
            leaveStep4Payload(formikRefHoliday?.current?.values),
            ''
          );
        }
      }, 500);
    }
  }, [
    save,
    leavePolicySubmit?.leaveAccrual,
    leavePolicySubmit?.leaveApplicationRules,
    leavePolicySubmit?.leaveRestriction,
    leavePolicySubmit?.holidaysAndWeekends,
    leavePolicySubmit?.leaveApproval,
  ]);

  return (
    <Box className="add-leave-policy-container">
      <Box className="step-container-wrap">
        <Box className=" d-flex align-center justify-space-between flex-wrap gap-sm">
          <Box className="d-flex align-center">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                router.back();
              }}
            />
            <Typography className="body-text fw600 pr8">
              {isEdit ? `Edit Leave Policy` : 'Add Leave Policy'}
            </Typography>
          </Box>
          <Box className="prev-next-btn-wrap d-flex align-center gap-sm">
            <Box className="d-flex align-center gap-sm">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => {
                  router.back();
                }}
              />
              <CustomButton
                fullWidth
                variant="outlined"
                title="Back"
                disabled={activeStep === 0}
                onClick={() => {
                  if (activeStep === 1) {
                    if (formikRefAppRule?.current) {
                      formikRefAppRule?.current.handleSubmit();
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveApplicationRules: true,
                        leaveAccrual: false,
                        leaveRestriction: false,
                        holidaysAndWeekends: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 2) {
                    if (formikRefRestriction?.current) {
                      formikRefRestriction?.current.handleSubmit();
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveRestriction: true,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        holidaysAndWeekends: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 3) {
                    if (formikRefHoliday?.current) {
                      formikRefHoliday?.current.handleSubmit();
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        holidaysAndWeekends: true,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 4) {
                    if (formikRefApproval?.current) {
                      formikRefApproval?.current.handleSubmit();
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        holidaysAndWeekends: false,
                        leaveApproval: true,
                      });
                    }
                  }
                }}
              />
              <CustomButton
                fullWidth
                variant="outlined"
                title="Next"
                disabled={activeStep === steps?.length - 1}
                onClick={() => {
                  if (activeStep === 0) {
                    if (formikRefAccrual?.current) {
                      formikRefAccrual?.current.handleSubmit();
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveApproval: false,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        holidaysAndWeekends: false,
                        leaveAccrual: true,
                      });
                    }
                  } else if (activeStep === 1) {
                    if (formikRefAppRule?.current) {
                      formikRefAppRule?.current.handleSubmit();
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveApplicationRules: true,
                        leaveAccrual: false,
                        leaveRestriction: false,
                        holidaysAndWeekends: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 2) {
                    if (formikRefRestriction?.current) {
                      formikRefRestriction?.current.handleSubmit();
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveRestriction: true,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        holidaysAndWeekends: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 3) {
                    if (formikRefHoliday?.current) {
                      formikRefHoliday?.current.handleSubmit();
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        holidaysAndWeekends: true,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        leaveApproval: false,
                      });
                    }
                  }
                }}
              />
              <CustomButton
                fullWidth
                variant="contained"
                title="Save & Finish"
                disabled={
                  Object.keys(leavePolicyDetails?.leaveAccrual).length === 0 ||
                  Object.keys(leavePolicyDetails?.leaveApplicationRules)
                    .length === 0 ||
                  Object.keys(leavePolicyDetails?.leaveRestriction).length ===
                    0 ||
                  (activeStep !== 4 &&
                    Object.keys(leavePolicyDetails?.leaveApproval).length === 0)
                }
                onClick={() => {
                  if (
                    formikRefApproval?.current &&
                    activeStep === 3 &&
                    leaveType?.leave_period_type === 'hour'
                  ) {
                    formikRefApproval?.current.handleSubmit();
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      holidaysAndWeekends: false,
                      leaveAccrual: false,
                      leaveApplicationRules: false,
                      leaveRestriction: false,
                      leaveApproval: true,
                    });
                  } else if (formikRefApproval?.current && activeStep === 4) {
                    formikRefApproval?.current.handleSubmit();
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      holidaysAndWeekends: false,
                      leaveAccrual: false,
                      leaveApplicationRules: false,
                      leaveRestriction: false,
                      leaveApproval: true,
                    });
                  } else if (activeStep === 0) {
                    if (formikRefAccrual?.current) {
                      formikRefAccrual?.current.handleSubmit();
                      setSave(0);
                      setLeavePolicySubmit({
                        holidaysAndWeekends: false,
                        leaveAccrual: true,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 1) {
                    if (formikRefAppRule?.current) {
                      formikRefAppRule?.current.handleSubmit();
                      setSave(1);
                      setLeavePolicySubmit({
                        holidaysAndWeekends: false,
                        leaveAccrual: false,
                        leaveApplicationRules: true,
                        leaveRestriction: false,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 2) {
                    if (formikRefRestriction?.current) {
                      formikRefRestriction?.current.handleSubmit();
                      setSave(2);
                      setLeavePolicySubmit({
                        holidaysAndWeekends: false,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        leaveRestriction: true,
                        leaveApproval: false,
                      });
                    }
                  } else if (activeStep === 3) {
                    if (formikRefHoliday?.current) {
                      formikRefHoliday?.current.handleSubmit();
                      setSave(3);
                      setLeavePolicySubmit({
                        holidaysAndWeekends: true,
                        leaveAccrual: false,
                        leaveApplicationRules: false,
                        leaveRestriction: false,
                        leaveApproval: false,
                      });
                    }
                  }
                }}
              />
            </Box>
          </Box>
        </Box>
        <Box className="step-container mt16">
          <Stepper
            className="steper-wrap"
            activeStep={activeStep}
            alternativeLabel
          >
            {steps?.map((step, index) => (
              <Step className="steps-wrap" key={index}>
                <StepLabel
                  StepIconComponent={(props) => (
                    <Box
                      className={`custom-step-icon title-text d-flex justify-center align-center ${
                        activeStep === 0 && index !== 0
                          ? 'disable-step'
                          : props.active
                            ? 'active-step'
                            : ''
                      }`}
                    >
                      {index + 1}
                    </Box>
                  )}
                >
                  <span
                    className={`step-label title-text ${
                      activeStep === 0 && index !== 0 ? 'disable-label' : ''
                    }`}
                  >
                    {step?.label}
                  </span>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
      </Box>

      <Box className="step-content" sx={{ mt: 3 }}>
        {steps[activeStep]?.component}
      </Box>
    </Box>
  );
}
