'use client';

import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { staticOptions } from '@/helper/common/staticOptions';
import CustomImageUploader from '@/components/ImageUpload/CustomImageUploader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { createAllergen, updateAllergen } from '@/services/recipeService';

export default function AddEditAllergen({
  singleData,
  handleCloseAddEditModal,
}) {
  const [loader, setLoader] = useState(false);
  const isDefault = singleData?.isDefault || false;
  const attributeType = 'allergen';

  return (
    <Box>
      {/* {loader && <PreLoader />} */}
      <Formik
        initialValues={{
          allergen_name: singleData?.attribute_title || '',
          allergen_icon: singleData?.iconItem?.iconUrl || '',
          allergen_status: singleData?.attribute_status || 'active',
          imagePreview: singleData?.iconItem?.iconUrl || '',
          allergen_description: singleData?.attribute_description || '',
          acceptFiles: null,
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          allergen_name: Yup.string().trim().required('This field is required'),
          allergen_status: Yup.string()
            .trim()
            .required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);

          // Prepare data object
          const body = new FormData();
          body.append('attribute_title', requestData?.allergen_name?.trim());
          body.append(
            'attribute_status',
            requestData?.allergen_status || 'active'
          );
          body.append(
            'attribute_description',
            requestData?.allergen_description?.trim()
          );
          body.append('attribute_type', attributeType);

          body.append('attributeIcon', requestData?.allergen_icon);

          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          };

          try {
            if (singleData?.id) {
              // Update existing allergen
              const data = await updateAllergen(singleData.id, body, config);
              setApiMessage('success', data?.message);
            } else {
              // Create new allergen
              const data = await createAllergen(body, config);
              setApiMessage('success', data?.message);
            }

            setLoader(false);
            // Close modal and refresh parent component
            handleCloseAddEditModal(true); // Pass true to indicate successful operation
          } catch (error) {
            setLoader(false);
            setApiMessage(
              'error',
              error?.response?.data?.message || 'Operation failed'
            );
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => {
          return (
            <Box className="recipe-modal-form">
              <Form onSubmit={handleSubmit}>
                <Box className="category-grid-container">
                  <Box className="">
                    <CustomImageUploader
                      imagePreview={values.imagePreview}
                      acceptFiles={values.acceptFiles}
                      error={errors?.allergen_icon}
                      fieldErrorText={errors?.allergen_icon}
                      onDropAccepted={(file) => {
                        const url = URL.createObjectURL(file);
                        setFieldValue('imagePreview', url);
                        setFieldValue('acceptFiles', { link: url });
                        setFieldValue('allergen_icon', file); // Store only filename for form validation
                      }}
                      onRemoveImage={() => {
                        setFieldValue('imagePreview', '');
                        setFieldValue('acceptFiles', null);
                        setFieldValue('allergen_icon', '');
                      }}
                      label="Allergen icon"
                      placeholder=""
                      uploadClassName="category-upload-container"
                      previewClassName="category-preview-container"
                    />
                  </Box>
                  <Box className="w100 ml8">
                    <Box className="w100">
                      <CustomTextField
                        fullWidth
                        name="allergen_name"
                        value={values?.allergen_name}
                        label="Allergen name"
                        placeholder="Enter allergen name"
                        error={Boolean(
                          touched?.allergen_name && errors?.allergen_name
                        )}
                        helperText={
                          touched?.allergen_name && errors?.allergen_name
                        }
                        onBlur={handleBlur}
                        onChange={(e) => {
                          if (e.target.value.length <= 60) {
                            handleChange(e);
                          }
                        }}
                        required
                        disabled={isDefault}
                      />
                      <Typography className="sub-title-text text-align-end">
                        {values?.allergen_name?.length + '/ 90'}
                      </Typography>
                    </Box>
                    <Box className="w100">
                      <CustomSelect
                        fullWidth
                        label="Status"
                        name="allergen_status"
                        placeholder="Status"
                        options={staticOptions?.ORG_STATUS}
                        value={
                          staticOptions?.ORG_STATUS?.find(
                            (opt) => opt?.value === values?.allergen_status
                          ) || ''
                        }
                        onChange={(selectedOption) =>
                          setFieldValue(
                            'allergen_status',
                            selectedOption?.value || ''
                          )
                        }
                        error={Boolean(
                          touched?.allergen_status && errors?.allergen_status
                        )}
                        helperText={
                          touched?.allergen_status && errors?.allergen_status
                        }
                        disabled={isDefault}
                        isClearable={false}
                        required
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  </Box>
                </Box>

                <Box className="w100 mt8">
                  <CustomTextField
                    fullWidth
                    name="allergen_description"
                    value={values?.allergen_description}
                    label="Description"
                    placeholder="Enter allergen description"
                    error={Boolean(
                      touched?.allergen_description &&
                        errors?.allergen_description
                    )}
                    helperText={
                      touched?.allergen_description &&
                      errors?.allergen_description
                    }
                    onBlur={handleBlur}
                    multiline={true}
                    rows={2}
                    disabled={isDefault}
                    onChange={(e) => {
                      if (e.target.value.length <= 250) {
                        handleChange(e);
                      }
                    }}
                  />
                  <Typography className="sub-title-text text-align-end">
                    {values?.allergen_description?.length + '/ 250'}
                  </Typography>
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Cancel"
                    onClick={() => handleCloseAddEditModal(false)}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!(dirty && isValid) || loader}
                    title={`${loader ? 'Save...' : 'Save'}`}
                  />
                </Box>
              </Form>
            </Box>
          );
        }}
      </Formik>
    </Box>
  );
}
