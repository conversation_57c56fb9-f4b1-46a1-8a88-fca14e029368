'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { identifiers } from '@/helper/constants/identifier';
import { Box } from '@mui/material';
import NotificationBanner from '@/components/UI/NotificationBanner';
import './layout.scss';

export default function PublicLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  // const [authdata, setAuthData] = useState(true);
  useEffect(() => {
    if (
      localStorage.getItem(identifiers?.AUTH_DATA) &&
      JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)) &&
      (pathname === '/login' ||
        pathname === '/otp' ||
        pathname === '/forgot-password' ||
        pathname === '/reset-password' ||
        pathname === '/resetpassword')
    ) {
      router?.push('/chart-dashboard');
    }

    // setAuthData(JSON.parse(localStorage.getItem(identifiers?.AUTH_DATA)));
  }, []);

  return (
    <>
      <Box>
        <NotificationBanner />
      </Box>
      <Box>{children}</Box>;
    </>
  );
}
