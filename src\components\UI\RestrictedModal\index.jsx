'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomButton from '../CustomButton';
import './restrictedModal.scss';

export default function RestrictedModal({ handleConfirm, limit = false }) {
  return (
    <Box className="restricted-modal-wrap">
      {limit ? (
        <Typography className="restricted-text">
          Staff creation limit exceeded.
          <br />
          <br />
          Please upgrade your plan or contact admin to add more staff members.
        </Typography>
      ) : (
        <Typography className="restricted-text">
          Your profile information is incomplete.
          <br />
          <br />
          Please update your profile and plan details to access additional
          features of the site.
        </Typography>
      )}
      <Box className="restricted-action">
        <CustomButton
          fullWidth
          className=""
          variant="contained"
          title="Ok"
          onClick={() => handleConfirm()}
        />
      </Box>
    </Box>
  );
}
