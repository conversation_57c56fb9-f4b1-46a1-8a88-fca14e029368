.hmrc-newstart-section {
  .additional-textfeild {
    .MuiInputBase-root {
      padding: 0 !important;
      padding-top: 0 !important;

      .MuiInputBase-inputMultiline {
        padding: 5px 12px 8px !important;
      }
    }
  }
  .info-icon:hover {
    color: var(--color-primary);
  }
  .statement-info {
    display: flex;
    justify-content: space-between;
    max-width: 100%;
    width: 415px;

    .info-icon {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
  .statement-checkbox {
    .check-box-text {
      margin-left: 0 !important;
      max-width: 100%;
      width: 415px;
      border: 1px solid var(--color-black);
      border-radius: 5px;

      .MuiButtonBase-root {
        margin-left: 0 !important;
      }
    }

    .MuiTypography-root {
      font-family: var(--font-family-primary);
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-regular);
      color: var(--text-color-black);
      line-height: var(--line-height-base);
      padding: 5px 20px 5px 5px;
    }
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed !important;
      .MuiCheckbox-root.Mui-checked {
        color: var(--color-primary);
      }
      .MuiTypography-root {
        color: var(--color-black) !important;
      }
    }
  }
  .display-grid-50 {
    display: grid;
    grid-template-columns: repeat(
      auto-fit,
      minmax(0, calc(500px + var(--spacing-lg)))
    );
    gap: var(--spacing-lg);
    align-items: flex-start;
    width: 100%;
  }
  .display-grid-proffesional {
    display: grid;
    grid-template-columns: 31% 31% 31%;
    column-gap: 32px;

    @media (max-width: 1200px) {
      grid-template-columns: 48% 48%;
      column-gap: 12px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 100%;
    }
  }
  .display-grid-proffesional-desc {
    display: grid;
    grid-template-columns: 100%;
  }

  .otp-section {
    div {
      display: flex;
      flex-wrap: wrap;
      row-gap: 10px;
    }

    input {
      border-radius: 5px;
      width: 30px !important;
      height: 34px !important;
      border: 2px solid #dddddd;
      font-size: 18px;
      margin-right: 15px;

      @media (max-width: 399px) {
        width: 25px !important;
        height: 31px !important;
        margin-right: 10px !important;
      }

      @media (max-width: 339px) {
        margin-right: 7px !important;
      }
    }
  }
  .checkbox-details {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;

    .check-box-text {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      margin-left: 0;

      .MuiButtonBase-root {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }
      .MuiCheckbox-root.Mui-checked {
        color: var(--color-primary);
      }

      .MuiTypography-root {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
        width: 27px;
      }
    }
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed !important;
      .MuiCheckbox-root.Mui-checked {
        color: var(--color-primary);
      }
      .MuiTypography-root {
        color: var(--color-black) !important;
      }
    }
  }
  .payment-section {
    display: flex;
    align-items: center;

    .dots {
      padding: 4px;
      background: var(--color-primary);
      border-radius: 100%;
      margin-right: 8px;
    }
  }
  .plan-checkbox-details {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    width: 60%;

    .check-box-text {
      display: flex;
      flex-direction: row-reverse;
      justify-content: flex-start;
      margin-left: 0;
      width: 100%;

      .MuiButtonBase-root {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
      }
      .MuiCheckbox-root.Mui-checked {
        color: var(--color-primary);
      }

      .MuiTypography-root {
        font-family: var(--font-family-primary);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        line-height: var(--line-height-base);
        width: 100%;
      }
    }

    @media (max-width: 768px) {
      width: 100%;
    }
    .Mui-disabled {
      opacity: 1;
      cursor: not-allowed !important;
      .MuiCheckbox-root.Mui-checked {
        color: var(--color-primary);
      }
      .MuiTypography-root {
        color: var(--color-black) !important;
      }
    }
  }
}
