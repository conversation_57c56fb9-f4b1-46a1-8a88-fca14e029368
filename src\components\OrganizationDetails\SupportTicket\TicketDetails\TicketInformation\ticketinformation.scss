@import '@/styles/variable.scss';

.form-wrap {
    .custom-select-wrap {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;

        @media(max-width:767px) {
            grid-template-columns: repeat(1, 1fr);
            gap: 0;
        }
    }

    .custom-date-time {
        .MuiInputBase-root {
            .MuiInputBase-input {
                padding: 2px 14px;
            }

            fieldset {
                height: 38px;
            }
        }
    }

    .followers-wrap {
        padding: 20px 0px;
        gap: 50px;

        .followers-list-wrap {

            .followers-list {
                gap: 2px;
                display: flex;
                flex-wrap: wrap;

                .follower-item {
                    position: relative;

                    .follower-img {
                        border-radius: 50px;
                        width: 30px;
                        height: 30px;
                        display: block;
                    }

                    &:hover::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        border-radius: 50px;
                        z-index: 1;
                    }

                    .close-icon-wrap {
                        position: absolute;
                        left: 51%;
                        bottom: 43%;
                        transform: translate(-50%, 50%);
                        opacity: 0;
                        z-index: 2;
                        transition: opacity 0.3s ease;

                        .close-icon {
                            fill: white;
                            height: 17px;
                            width: 17px;
                        }
                    }

                    &:hover .close-icon-wrap {
                        opacity: 1;
                    }
                }
            }

            .add-follower-wrap {
                opacity: 0;
                transition: opacity 0.3s ease;

                .add-btn {
                    fill: $color-Dark-30;
                    height: 18px;
                    width: 18px;
                    transition: fill 0.3s ease;
                }

                .add-follow-text {
                    color: $color-Dark-30;
                    font-size: 15px;
                    transition: color 0.3s ease;
                }

                &:hover {
                    opacity: 1;

                    .add-btn {
                        fill: $color-blue;
                    }

                    .add-follow-text {
                        color: $color-blue;
                    }
                }
            }

            .followers-menu {
                .followers-search {

                    .select__control {

                        padding: 0px;

                        .select__value-container {
                            padding: 0px;

                            .select__single-value {
                                margin-left: 14px;
                                margin-top: 2px;
                            }

                            .select__input-container {
                                margin: 0px 0px 0px 14px;
                            }

                            .select__placeholder {
                                padding: 2px 0px 0px 14px;
                                font-family: Inter, sans-serif;
                            }
                        }

                        .select__indicators {
                            .select__indicator {
                                svg {
                                    color: $color-Dark-90;
                                }
                            }
                        }
                    }
                }

            }

        }
    }

    .buttons-wrap {

        .cancel-btn,
        .submit-btn {
            padding: 4px 20px !important;
            font-size: 15px;
            font-weight: 500;

            &:hover {
                color: white !important;
                box-shadow: none !important;
            }
        }

        .cancel-btn {
            background-color: white !important;
            color: $color-primary !important;

            &:hover {
                color: $color-primary !important;
            }
        }

    }

    .select {
        .MuiSelect-select {
            padding: 7.5px 7px 12px 15px;
        }
    }

    .select-assignee-wrap {
        .assignee-text {
            width: 100%;
            max-width: 200px;
        }

        .assignee-select {
            width: 100%;
            max-width: 300px;
        }

        .slected-wrap {
            .select__control {

                padding: 0px;

                .select__value-container {
                    padding: 0px;

                    .select__single-value {
                        margin-left: 14px;
                        margin-top: 2px;
                    }

                    .select__input-container {
                        margin: 0px 0px 0px 14px;
                    }

                    .select__placeholder {
                        padding: 2px 0px 0px 14px;
                        font-family: Inter, sans-serif;
                    }
                }

                .select__indicators {
                    .select__indicator {
                        svg {
                            color: $color-Dark-90;
                        }
                    }
                }
            }
        }
    }

}