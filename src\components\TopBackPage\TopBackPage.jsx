'use client';

import React from 'react';
import { Box, Typography, Divider } from '@mui/material';
import { useRouter } from 'next/navigation';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';

export default function TopBackPageButton({
  title = 'Back',
  onBackClick = null,
  showDivider = true,
  className = '',
}) {
  const router = useRouter();

  const handleBack = () => {
    if (onBackClick && typeof onBackClick === 'function') {
      onBackClick();
    } else {
      router.back();
    }
  };

  return (
    <>
      <Box className={`top-back-page-button-container ${className}`}>
        <Box className="back-button" onClick={handleBack}>
          <ArrowBackIosIcon />
        </Box>
        <Typography className="body-semibold">{title}</Typography>
      </Box>
      {showDivider && <Divider className="mb8" />}
    </>
  );
}
