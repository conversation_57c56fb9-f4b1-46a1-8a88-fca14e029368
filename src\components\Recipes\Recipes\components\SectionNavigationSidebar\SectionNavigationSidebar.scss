// Section Navigation Sidebar Component Styles
.section-navigation-sidebar {
  width: 320px; // w-80 = 20rem = 320px
  border-right: var(--border-width-xs) solid var(--border-color-light-gray);

  @media (max-width: 767px) {
    display: none;
  }

  &__content {
    padding: var(--spacing-lg) var(--spacing-xxl);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-lg);
  }

  // Section Navigation
  &__nav {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__button {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    text-align: left;
    transition: all 0.15s ease-out;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--text-color-black);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      svg {
        stroke: var(--color-primary);
      }
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      box-shadow: var(--box-shadow-xs);
    }
  }

  &__section-content {
    flex: 1;
  }

  &__section-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
  }

  &__required {
    font-size: var(--font-size-sm);
    color: var(--text-error);
    margin-left: var(--spacing-xxs);
  }

  // Cost Summary
  &__cost-summary {
    margin-top: var(--spacing-2xl);
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border-radius: var(--border-radius-md);
    border: var(--border-width-xs) solid var(--color-primary);
  }

  &__cost-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__cost-items {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }

  &__cost-item {
    display: flex;
    justify-content: space-between;
  }

  &__cost-label {
    font-family: var(--font-family-primary);
    color: var(--text-color-slate-gray);
  }

  &__cost-value {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }
}
