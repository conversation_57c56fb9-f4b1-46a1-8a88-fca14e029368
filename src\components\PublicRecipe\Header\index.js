'use client';
import { useState } from 'react';
import { Box, Typography } from '@mui/material';
import Image from 'next/image';
import MenuOutlinedIcon from '@mui/icons-material/MenuOutlined';
import CustomDrawer from '@/components/UI/CustomDrawer';
import logo from '../../../../public/images/app-logo.svg';
import PublicLinksDrawer from './components/PublicLinksDrawer';
import './publicheader.scss';

export default function PublicRecipeHeader() {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const handleDrawerToggle = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  return (
    <>
      <header className="public-recipe-header d-flex align-center">
        <Box
          className="d-flex align-center gap-sm public-menu-wrap"
          onClick={handleDrawerToggle}
        >
          <MenuOutlinedIcon className="public-menu-icon cursor-pointer" />
          <Typography className="brown-text menu-text cursor-pointer text-capital">
            MENU
          </Typography>
        </Box>
        <Box className="public-logo-wrap">
          <Image src={logo} alt="logo" width={100} height={100} />
        </Box>
      </header>

      <CustomDrawer
        anchor="left"
        open={isDrawerOpen}
        onClose={handleDrawerToggle}
        title="CLOSE"
        content={
          <Box className="public-menu-content">
            <PublicLinksDrawer />
          </Box>
        }
      />
    </>
  );
}
