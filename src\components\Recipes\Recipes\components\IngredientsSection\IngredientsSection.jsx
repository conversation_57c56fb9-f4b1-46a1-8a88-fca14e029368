import React, { useState, useEffect } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { InputAdornment } from '@mui/material';
import { useRecipeMeasures } from '@/hooks/useRecipeMeasures';
import { useRouter } from 'next/navigation';
import {
  getIngredientItemsList,
  getAttributeList,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import './IngredientsSection.scss';

// Validation Schema
const ingredientValidationSchema = Yup.object().shape({
  name: Yup.string()
    .trim()
    .required('Ingredient name is required')
    .min(2, 'Name must be at least 2 characters'),
  quantity: Yup.number()
    .min(0, 'Quantity cannot be negative')
    .required('Quantity is required'),
  unit: Yup.string().required('Unit is required'),
  cost: Yup.number().min(0, 'Cost cannot be negative'),
  wastagePercentage: Yup.number()
    .min(0, 'Wastage cannot be negative')
    .max(100, 'Wastage cannot exceed 100%'),
});

const IngredientsSection = ({
  formData,
  dispatch,
  validationErrors = {},
  currency,
}) => {
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { unitsOfMeasureOptions } = useRecipeMeasures();
  const [localIngredients, setLocalIngredients] = useState(() => {
    // Handle both data.ingredients and data directly being an array
    return Array.isArray(formData) ? formData : formData?.ingredients || [];
  });

  // API-driven ingredient database state
  const [ingredientDatabase, setIngredientDatabase] = useState([]);
  const [isLoadingIngredients, setIsLoadingIngredients] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState(null);
  const [cookingMethods, setCookingMethods] = useState([]);
  const [preparationMethods, setPreparationMethods] = useState([]);
  const [isCookingMethod, setIsCookingMethod] = useState(
    formData?.isCookingMethod || false
  );
  const [isPreparationMethod, setIsPreparationMethod] = useState(
    formData?.isPreparationMethod || false
  );

  // Sync local state with prop changes
  useEffect(() => {
    // Handle both data.ingredients and data directly being an array
    const ingredientsData = Array.isArray(formData)
      ? formData
      : formData?.ingredients || [];
    setLocalIngredients(ingredientsData);
  }, [formData, formData?.ingredients]);

  useEffect(() => {
    fetchCookingMethods();
    fetchPreparationMethods();
  }, []);

  // Use local ingredients for immediate UI updates
  const ingredients = localIngredients;

  // API function to fetch ingredients based on search term
  const fetchIngredients = async (searchQuery = '') => {
    if (!searchQuery.trim()) {
      setIngredientDatabase([]);
      return;
    }

    try {
      setIsLoadingIngredients(true);
      const response = await getIngredientItemsList(
        searchQuery,
        '',
        { status: 'active' },
        '',
        ''
      );

      // Transform API response to match expected format
      const transformedIngredients =
        response?.ingredients?.map((ingredient) => ({
          ...ingredient,
          preparationMethod: 'whole',
          cookingMethod: 'raw',
          wastagePercentage: 0,
          quantity: 1,
        })) || [];

      setIngredientDatabase(transformedIngredients);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch ingredients'
      );
      setIngredientDatabase([]);
    } finally {
      setIsLoadingIngredients(false);
    }
  };

  // API functions to fetch cooking and preparation methods using getAttributeList
  const fetchCookingMethods = async () => {
    try {
      const response = await getAttributeList(
        '',
        '',
        { status: 'active' },
        '',
        '',
        'ingredient_cooking_method' // type
      );
      // Transform API response to match expected format
      const transformedCookingMethods =
        response?.attributes?.map((method) => ({
          label: method?.attribute_title,
          value: method?.id,
        })) || [];
      setCookingMethods(transformedCookingMethods);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch cooking methods'
      );
      setCookingMethods([]);
    }
  };

  const fetchPreparationMethods = async () => {
    try {
      const response = await getAttributeList(
        '',
        '',
        { status: 'active' },
        '',
        '',
        'preparation_method' // type
      );
      // Transform API response to match expected format
      const transformedPreparationMethods =
        response?.attributes?.map((method) => ({
          label: method?.attribute_title,
          value: method?.id,
        })) || [];
      setPreparationMethods(transformedPreparationMethods);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch preparation methods'
      );
      setPreparationMethods([]);
    }
  };

  // Debounced search effect
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (searchTerm?.trim()) {
        fetchIngredients(searchTerm);
      } else {
        setIngredientDatabase([]);
      }
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [searchTerm]);

  // Use ingredientDatabase directly since it's already filtered by API
  const filteredIngredients = ingredientDatabase || [];

  const validateIngredient = async (ingredient) => {
    try {
      await ingredientValidationSchema.validate(ingredient, {
        abortEarly: false,
      });
      return {};
    } catch (error) {
      const errors = {};
      error?.inner?.forEach?.((err) => {
        if (err?.path) {
          errors[err.path] = err?.message || 'Invalid value';
        }
      });
      return errors;
    }
  };

  const addIngredient = (ingredientData = null) => {
    const wastageMultiplier =
      1 + (ingredientData?.wastagePercentage || 0) / 100;
    const baseCost = ingredientData?.cost || 0;
    const finalCost =
      (ingredientData?.quantity || 0) * baseCost * wastageMultiplier;

    const newIngredient = {
      ...ingredientData,
      cost: baseCost, // Ensure cost field is set
      unit:
        ingredientData?.unit ||
        ingredientData?.measure_of_cost?.unit_title ||
        'gram', // Ensure unit field is set
      finalCost,
    };

    const updatedIngredients = [...(ingredients || []), newIngredient];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    // Ensure dispatch is called properly
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }

    setSearchTerm('');
    setShowSuggestions(false);
  };

  const updateIngredient = async (index, field, value) => {
    if (!ingredients?.[index]) return;

    const updatedIngredients = [...(ingredients || [])];
    updatedIngredients[index] = {
      ...updatedIngredients[index],
      [field]: value,
    };

    // Recalculate final cost when relevant fields change
    if (['quantity', 'cost', 'wastagePercentage']?.includes?.(field)) {
      const ingredient = updatedIngredients[index];
      const wastageMultiplier = 1 + (ingredient?.wastagePercentage || 0) / 100;
      const baseCost = ingredient?.cost || 0;
      updatedIngredients[index].finalCost =
        (ingredient?.quantity || 0) * baseCost * wastageMultiplier;
    }

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    // Always update the ingredient, validation is for display purposes only
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }

    // Validate for UI feedback (optional)
    validateIngredient(updatedIngredients[index]);
  };

  const removeIngredient = (index) => {
    const updatedIngredients =
      ingredients?.filter?.((_, i) => i !== index) || [];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }
  };

  const selectIngredientFromDatabase = (ingredientData) => {
    if (ingredientData) {
      addIngredient(ingredientData);
    }
  };

  const addFirstIngredient = () => {
    router.push('/recipes/ingredients/create');
  };

  return (
    <div className="ingredients-section">
      {/* Add Ingredient Search */}
      <div className="ingredients-section__search">
        <div className="ingredients-section__header">
          <h3 className="ingredients-section__title">Recipe Ingredients</h3>
        </div>

        <div className="ingredients-section__search-input">
          <CustomTextField
            value={searchTerm}
            onChange={(e) => {
              const value = e?.target?.value || '';
              setSearchTerm(value);
              setShowSuggestions(value.length > 0);
            }}
            onFocus={() => setShowSuggestions((searchTerm?.length || 0) > 0)}
            onBlur={() => {
              // Delay hiding suggestions to allow for clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder="Search ingredients database..."
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Icon
                    name="Search"
                    size={20}
                    className="ingredients-section__search-icon"
                  />
                </InputAdornment>
              ),
              endAdornment: isLoadingIngredients && (
                <InputAdornment position="end">
                  <Icon
                    name="Loader"
                    size={16}
                    className="ingredients-section__loading-icon"
                  />
                </InputAdornment>
              ),
            }}
          />
        </div>

        {/* Search Suggestions */}
        {showSuggestions && searchTerm?.trim() && (
          <div className="ingredients-section__suggestions">
            {isLoadingIngredients ? (
              <div className="ingredients-section__suggestion-loading">
                <Icon name="Loader" size={16} />
                <span>Searching ingredients...</span>
              </div>
            ) : (filteredIngredients?.length || 0) > 0 ? (
              filteredIngredients?.slice?.(0, 10)?.map?.((ingredient) => (
                <button
                  key={ingredient?.id}
                  onClick={() => selectIngredientFromDatabase(ingredient)}
                  className="ingredients-section__suggestion-item"
                >
                  <div className="ingredients-section__suggestion-content">
                    <div className="ingredients-section__suggestion-icon">
                      <Icon name="Package" size={14} />
                    </div>
                    <div className="ingredients-section__suggestion-details">
                      <div className="ingredients-section__suggestion-name">
                        {ingredient?.ingredient_name || 'Unknown Ingredient'}
                      </div>
                      <div className="ingredients-section__suggestion-meta">
                        {ingredient?.category &&
                        ingredient?.category?.length > 0
                          ? ingredient?.category
                              ?.map((cat) => cat?.category_name)
                              .join(', ') + ' • '
                          : ''}
                        {currency}
                        {ingredient?.cost?.toFixed?.(2) || '0.00'}/
                        {ingredient?.measure_of_cost?.unit_title || ''}
                      </div>
                      {ingredient?.ingredient_description && (
                        <div className="ingredients-section__suggestion-description">
                          {ingredient.ingredient_description}
                        </div>
                      )}
                    </div>
                  </div>
                  <Icon name="Plus" size={16} />
                </button>
              )) || []
            ) : (
              <div className="ingredients-section__suggestion-empty">
                <Icon name="Search" size={16} />
                <span>No ingredients found for "{searchTerm}"</span>
              </div>
            )}
          </div>
        )}
      </div>
      <div>
        <div>
          <CustomCheckbox
            checked={isCookingMethod}
            onChange={() => {
              setIsCookingMethod(!isCookingMethod);
              formData.isCookingMethod = !isCookingMethod;
            }}
            label="Display cooking method"
          />
        </div>
        <div>
          <CustomCheckbox
            checked={isPreparationMethod}
            onChange={() => {
              setIsPreparationMethod(!isPreparationMethod);
              formData.isPreparationMethod = !isPreparationMethod;
            }}
            label="Display preparation method"
          />
        </div>
      </div>
      {/* Ingredients List */}
      <div className="ingredients-section__list">
        {(ingredients?.length || 0) === 0 ? (
          <div className="ingredients-section__empty-state">
            <Icon
              name="Package"
              size={48}
              className="ingredients-section__empty-icon"
            />
            <h4 className="ingredients-section__empty-title">
              No ingredients added yet
            </h4>
            <p className="ingredients-section__empty-description">
              Search for ingredients above or add custom ingredients to get
              started
            </p>
            <CustomButton
              onClick={() => addFirstIngredient()}
              variant="contained"
            >
              Add First Ingredient
            </CustomButton>
          </div>
        ) : (
          ingredients?.map?.((ingredient, index) => (
            <div
              key={ingredient?.id || index}
              className="ingredients-section__item"
            >
              {/* Ingredient Header */}
              <div className="ingredients-section__item-header">
                <div className="ingredients-section__item-info">
                  <div className="ingredients-section__item-icon">
                    <Icon name="Package" size={16} color="#D97706" />
                  </div>
                  <div className="ingredients-section__item-details">
                    <input
                      type="text"
                      value={ingredient?.ingredient_name || ''}
                      onChange={(e) =>
                        updateIngredient(index, 'name', e?.target?.value || '')
                      }
                      placeholder="Ingredient name"
                      className="ingredients-section__item-name"
                    />
                    {ingredient?.category &&
                    ingredient?.category?.length > 0 ? (
                      <div className="ingredients-section__item-category">
                        {ingredient?.category
                          ?.map((cat) => cat?.category_name)
                          .join(', ')}
                      </div>
                    ) : (
                      '-'
                    )}
                  </div>
                </div>

                <button
                  onClick={() => removeIngredient(index)}
                  className="ingredients-section__remove-button"
                  aria-label={`Remove ${ingredient?.ingredient_name || 'ingredient'}`}
                >
                  <Icon name="Trash2" size={16} color="currentColor" />
                </button>
              </div>

              {/* Quantity and Unit */}
              <div className="ingredients-section__grid-2">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Quantity"
                    type="number"
                    value={ingredient?.quantity?.toString?.() || ''}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'quantity',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 0.01, min: 0 }}
                    fullWidth
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomSelect
                    label="Unit"
                    name={`unit`}
                    fullWidth
                    options={unitsOfMeasureOptions}
                    value={
                      unitsOfMeasureOptions?.find((item) => {
                        return (
                          item?.value === ingredient?.unit ||
                          item?.value ===
                            ingredient?.measure_of_cost?.unit_title
                        );
                      }) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(index, 'unit', e?.value || 'g')
                    }
                    isClearable={false}
                  />
                </div>
              </div>

              {/* Cost and Wastage */}
              <div className="ingredients-section__grid-3">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Base Cost"
                    type="number"
                    value={ingredient?.cost?.toString?.() || ''}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'cost',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 0.01, min: 0 }}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Wastage"
                    type="number"
                    value={ingredient?.wastagePercentage || 0}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'wastagePercentage',
                        parseInt(e?.target?.value) || 0
                      )
                    }
                    fullWidth
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">%</InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Final Cost"
                    value={(ingredient?.finalCost || 0)?.toFixed?.(2) || '0.00'}
                    fullWidth
                    disabled
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              </div>

              {/* Preparation Methods */}
              <div className="ingredients-section__grid-2">
                {isCookingMethod && (
                  <div className="ingredients-section__field-group">
                    <CustomSelect
                      label="Cooking Method"
                      name={`cookingMethod-${index}`}
                      fullWidth
                      options={cookingMethods}
                      value={
                        cookingMethods?.find((item) => {
                          return item?.value === ingredient?.cookingMethod;
                        }) || ''
                      }
                      onChange={(e) =>
                        updateIngredient(
                          index,
                          'cookingMethod',
                          e?.value || 'raw'
                        )
                      }
                      isClearable={false}
                    />
                  </div>
                )}

                {isPreparationMethod && (
                  <div className="ingredients-section__field-group">
                    <CustomSelect
                      label="Preparation"
                      name={`preparationMethod-${index}`}
                      fullWidth
                      options={preparationMethods}
                      value={
                        preparationMethods?.find((item) => {
                          return item?.value === ingredient?.preparationMethod;
                        }) || ''
                      }
                      onChange={(e) =>
                        updateIngredient(
                          index,
                          'preparationMethod',
                          e?.value || 'whole'
                        )
                      }
                      isClearable={false}
                    />
                  </div>
                )}
              </div>

              {/* Allergens Display */}
              {ingredient?.allergy &&
                (ingredient?.allergy?.length || 0) > 0 && (
                  <div className="ingredients-section__allergens">
                    <Icon name="AlertTriangle" size={14} color="#F59E0B" />
                    <span className="ingredients-section__allergens-text">
                      Contains:{' '}
                      {ingredient?.allergy
                        ?.map((att) => att?.attribute_title)
                        .join(', ')}
                    </span>
                  </div>
                )}
            </div>
          ))
        )}
      </div>

      {/* Ingredients Summary */}
      {(ingredients?.length || 0) > 0 && (
        <div className="ingredients-section__summary">
          <h4 className="ingredients-section__summary-title">
            Ingredients Summary
          </h4>
          <div className="ingredients-section__summary-grid">
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {ingredients?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Total Ingredients
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {currency}
                {ingredients
                  ?.reduce?.((sum, ing) => sum + (ing?.finalCost || 0), 0)
                  ?.toFixed?.(2) || '0.00'}
              </div>
              <div className="ingredients-section__summary-label">
                Total Cost
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {[
                  ...new Set(
                    ingredients?.flatMap?.((ing) => ing?.allergy || []) || []
                  ),
                ]?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Unique Allergens
              </div>
            </div>
          </div>
        </div>
      )}

      {validationErrors?.ingredients && (
        <div className="ingredients-section__error">
          <Icon name="AlertCircle" size={16} />
          <span className="other-field-error-text">
            {validationErrors.ingredients}
          </span>
        </div>
      )}
    </div>
  );
};

export default IngredientsSection;
