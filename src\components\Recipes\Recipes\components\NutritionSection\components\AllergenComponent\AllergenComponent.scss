.allergen-component {
  // Header
  &__header {
    margin-bottom: var(--spacing-xl);
  }

  &__header-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-primary);
    margin: 0 0 var(--spacing-xs) 0;
  }

  &__description {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin: 0;
  }

  // Loading State
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
  }

  // Search
  &__search {
    margin-bottom: var(--spacing-xl);
  }

  // Allergens Section
  &__allergens {
    margin-bottom: var(--spacing-xl);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__allergens-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__allergens-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-md);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-sm);
    }
  }

  // Allergen Item
  &__allergen-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    cursor: pointer;
    transition: all 0.15s ease-out;
    text-align: left;
    width: 100%;

    &:hover {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }

    &--selected {
      background-color: var(--color-danger);
      color: var(--text-color-white);
      border-color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger);
        border-color: var(--color-danger);
      }
    }

    &--high {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);
    }

    &--medium {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
    }

    &--low {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: var(--color-secondary);
      color: var(--text-color-slate-gray);
      border-color: var(--border-color-light-gray);

      &:hover {
        background-color: var(--color-secondary);
        color: var(--text-color-slate-gray);
        border-color: var(--border-color-light-gray);
      }
    }

    &--default {
      &:hover {
        background-color: var(--color-off-white);
      }
    }
  }
  .allergen-component__may-allergen-item--selected {
    background-color: var(--color-warning);
    color: var(--text-color-white);
    border: none;
  }

  &__allergen-icon {
    width: var(--spacing-2xl);
    height: var(--spacing-2xl);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease-out;
    position: relative;

    &--default {
      background-color: var(--color-secondary);
      color: var(--text-color-slate-gray);
    }

    // &--selected {
    //   background-color: var(--color-danger);
    //   color: var(--text-color-white);
    // }

    &--may-contain {
      .allergen-component__allergen-indicator {
        position: absolute;
        top: -4px;
        right: -4px;
        background-color: var(--color-warning);
        color: var(--text-color-white);
        font-size: var(--font-size-xxs);
        font-weight: var(--font-weight-bold);
        width: 16px;
        height: 16px;
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid var(--text-color-white);
        z-index: 10;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      }
    }
  }

  &__allergen-indicator {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--color-warning);
    color: var(--text-color-white);
    font-size: var(--font-size-xxs);
    font-weight: var(--font-weight-bold);
    width: 16px;
    height: 16px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--text-color-white);
    z-index: 10;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  &__allergen-content {
    flex: 1;
    min-width: 0;
  }

  &__allergen-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-xs);
  }

  &__allergen-severity {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-normal);
    font-size: var(--font-size-xs);
    color: var(--text-color-black);
    opacity: 0.8;
  }

  // Dietary Summary
  &__dietary {
    background-color: var(--color-off-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    margin-top: var(--spacing-xl);
  }

  &__dietary-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
  }

  &__dietary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    color: var(--text-color-primary);
    margin: 0;
  }

  &__dietary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-sm);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  &__dietary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);

    &--selected {
      background-color: var(--color-danger);
      color: var(--text-color-white);
    }

    &--may-contain {
      background-color: var(--color-warning);
      color: var(--text-color-white);
    }
  }

  &__dietary-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &--may-contain {
      .allergen-component__dietary-indicator {
        position: absolute;
        top: -3px;
        right: -3px;
        background-color: var(--color-warning);
        color: var(--text-color-white);
        font-size: var(--font-size-xxs);
        font-weight: var(--font-weight-bold);
        width: 14px;
        height: 14px;
        border-radius: var(--border-radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid var(--text-color-white);
        z-index: 10;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }
  }

  &__dietary-indicator {
    position: absolute;
    top: -3px;
    right: -3px;
    background-color: var(--color-warning);
    color: var(--text-color-white);
    font-size: var(--font-size-xxs);
    font-weight: var(--font-weight-bold);
    width: 14px;
    height: 14px;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--text-color-white);
    z-index: 10;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  &__dietary-content {
    flex: 1;
    min-width: 0;
  }

  &__dietary-label {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: inherit;
  }

  &__dietary-remove {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    transition: background-color 0.15s ease-out;

    &:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    &__header-content {
      gap: var(--spacing-sm);
    }

    &__title {
      font-size: var(--font-size-base);
    }

    &__description {
      font-size: var(--font-size-xs);
    }

    &__dietary {
      padding: var(--spacing-md);
    }
  }

  @media (max-width: 480px) {
    &__allergens-grid {
      grid-template-columns: 1fr;
    }

    &__dietary {
      padding: var(--spacing-sm);
    }

    &__dietary-title {
      font-size: var(--font-size-sm);
    }
  }
}
