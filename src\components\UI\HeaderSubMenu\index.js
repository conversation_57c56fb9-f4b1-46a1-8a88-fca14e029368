import React, { useContext, useEffect, useState } from 'react';
import { Tab<PERSON>ontext, TabList } from '@mui/lab';
import { Box, Tab } from '@mui/material';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';

// Utility function to check if path matches menu item
const isPathMatch = (menuLink, currentPath, searchParams) => {
  const { isRequests, isRequest, isEdit, isActivity } = searchParams;

  // Direct path match
  if (menuLink === currentPath) return true;

  // Staff related paths
  if (
    menuLink === '/staff' &&
    (currentPath === '/staff' ||
      currentPath.includes('/staff/') ||
      currentPath.includes('/user/'))
  ) {
    return true;
  }

  // DSR related paths
  if (
    menuLink === '/dsr-request' &&
    isRequests &&
    currentPath.includes('/dsr/')
  ) {
    return true;
  }
  if (
    menuLink === '/dsr' &&
    (isRequest || isEdit || isActivity || currentPath.includes('/adddsr')) &&
    currentPath.includes('/dsr/')
  ) {
    return true;
  }

  // WSR related paths
  if (
    menuLink === '/wsr-request' &&
    isRequests &&
    currentPath.includes('/wsr/')
  ) {
    return true;
  }
  if (
    menuLink === '/wsr' &&
    (isRequest || isEdit || isActivity || currentPath.includes('/addwsr')) &&
    currentPath.includes('/wsr/')
  ) {
    return true;
  }

  // Payroll related paths
  if (
    menuLink === '/payroll-request' &&
    isRequests &&
    currentPath.includes('/payroll/')
  ) {
    return true;
  }
  if (
    menuLink === '/payroll' &&
    (isRequest ||
      isEdit ||
      isActivity ||
      currentPath.includes('/addexpenses')) &&
    currentPath.includes('/payroll/')
  ) {
    return true;
  }

  // Leave policy related paths
  if (
    menuLink === '/leave-policy-type' &&
    (currentPath.includes('/create-policy-type') ||
      currentPath.includes('/edit-policy-type') ||
      currentPath.includes('/edit-leave-policy') ||
      currentPath.includes('/add-leave-policy'))
  ) {
    return true;
  }

  // Document related paths
  if (
    menuLink === '/document-staff/all' &&
    (currentPath.includes('/document-staff/') ||
      currentPath.includes('/create-content') ||
      currentPath.includes('/create-category'))
  ) {
    return true;
  }
  if (
    menuLink === '/document-own/all' &&
    currentPath.includes('/document-own/')
  ) {
    return true;
  }

  // Leave related paths
  if (
    menuLink === '/own-leave' &&
    (currentPath.includes('/apply-leave') ||
      currentPath.includes('/own-leave/'))
  ) {
    return true;
  }
  if (menuLink === '/leave-remark' && currentPath.includes('/leave-remark/')) {
    return true;
  }
  if (
    menuLink === '/leave-reports' &&
    currentPath.includes('/leave-reports/')
  ) {
    return true;
  }

  // Other specific paths
  if (
    menuLink === '/budget-forecast' &&
    currentPath.includes('/budget-forecast/')
  ) {
    return true;
  }
  if (
    menuLink === '/chart-dashboard' &&
    currentPath.includes('/chart-dashboard/')
  ) {
    return true;
  }

  return false;
};

const HeaderSubMenu = ({ headerMenu, pathname, isMenuActive }) => {
  const router = useRouter();
  const [tab, setTab] = useState(null);
  const { setIsActive, authState, setRestrictedModal } =
    useContext(AuthContext);
  const searchParams = useSearchParams();
  const isRequests = searchParams.get('isRequests');
  const isRequest = searchParams.get('isRequest');
  const isActivity = searchParams.get('isActivity');
  const isEdit = searchParams.get('isEdit');
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);

  const tabChangeHandler = (event, newValue) => {
    const isOrgView = checkOrganizationRole('org_master') || false;
    const isRestricted = isNormalUser
      ? false
      : isOrgView
        ? authdata?.user_status !== 'active' ||
          authState?.purchase_plan === false
        : !authState?.profile_status;

    if (isRestricted) {
      if (isOrgView) {
        if (pathname === '/org/organization') {
          setRestrictedModal(true);
        } else {
          router.push('/org/organization');
        }
      } else {
        if (pathname === '/myprofile') {
          setRestrictedModal(true);
        } else {
          router.push('/myprofile');
        }
      }
    } else {
      setTab(newValue);

      const selected = headerMenu?.find((f) => {
        return String(f?.id) === String(newValue);
      });
      if (selected) {
        router.push(selected?.link);
      }
      setIsActive(isMenuActive);
    }
  };

  useEffect(() => {
    if (headerMenu && headerMenu.length > 0 && pathname) {
      const selectedIndex = headerMenu?.find((item) => item?.link === pathname);
      if (selectedIndex) {
        setTab(String(selectedIndex?.id));
        setIsActive(isMenuActive);
      } else if (pathname?.includes('/budget-forecast/')) {
        setTab(String(headerMenu[1]?.id));
      } else if (isRequests) {
        setTab(
          pathname?.includes('/payroll/')
            ? '6'
            : pathname?.includes('/wsr/')
              ? '4'
              : '2'
        );
      } else if (isEdit || isRequest || isActivity) {
        setTab(
          pathname?.includes('/payroll/')
            ? '5'
            : pathname?.includes('/wsr/')
              ? '3'
              : '1'
        );
      } else if (pathname?.includes('/addwsr')) {
        setTab(String(3));
      } else if (pathname?.includes('/adddsr')) {
        setTab(String(1));
      } else if (pathname?.includes('/addexpenses')) {
        setTab(String(5));
      } else if (pathname?.includes('/document-own/')) {
        setTab(String(2));
      } else if (
        pathname?.includes('/create-policy-type') ||
        pathname?.includes('/edit-policy-type') ||
        pathname?.includes('/edit-leave-policy') ||
        pathname?.includes('/add-leave-policy')
      ) {
        setTab(String(3));
      } else if (pathname?.includes('/holiday')) {
        setTab(String(5));
      } else if (pathname?.includes('/apply-leave')) {
        setTab(String(2));
      } else {
        setTab(String(headerMenu[0]?.id));
      }
    }
  }, [pathname, headerMenu]);

  return (
    <>
      <Box className="header-menu-options">
        <TabContext value={String(tab)}>
          <Box className="">
            <TabList
              variant="scrollable"
              scrollButtons="auto"
              onChange={tabChangeHandler}
              aria-label="action tabs"
              className="header-menu-options-tab"
            >
              {headerMenu?.map((obj, index) => {
                if (
                  obj?.permission &&
                  authState?.id &&
                  (authState?.UserPermission?.[obj?.permission] === 2 ||
                    authState?.UserPermission?.[obj?.permission] === 1 ||
                    (obj?.permission === 'leavecenter' &&
                      obj?.id === 2 &&
                      authState?.web_user_active_role_id !== 1) ||
                    (obj?.permission === 'notificationcenter' &&
                      obj?.id === 5) ||
                    (obj?.permission === 'notificationcenter' &&
                      obj?.id === 2 &&
                      (checkOrganizationRole('org_master') ||
                        checkOrganizationRole('super_admin') ||
                        authState?.web_user_active_role_id !== 1)) ||
                    (obj?.permission === 'resignationcenter' &&
                      obj?.id === 9) ||
                    (obj?.permission === 'resignationcenter' &&
                      obj?.id === 2 &&
                      authState?.web_user_active_role_id !== 1) ||
                    (obj?.permission === 'staffpermission' &&
                      (authState?.UserPermission?.['staff'] === 1 ||
                        authState?.UserPermission?.['staff'] === 2)) ||
                    obj?.permission === 'change_request' ||
                    obj?.permission === 'owncategory' ||
                    (obj?.permission === 'category' && obj?.id === 15))
                ) {
                  return (
                    <Tab
                      key={index}
                      sx={{ mr: 2 }}
                      // label={obj?.name}
                      value={String(obj?.id)}
                      label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {obj.icon}
                          <Box component="span" sx={{ marginLeft: 1 }}>
                            {obj.name}
                          </Box>
                        </Box>
                      }
                      className={`header-menu-detail ${
                        isPathMatch(obj?.link, pathname, {
                          isRequests,
                          isRequest,
                          isEdit,
                          isActivity,
                        })
                          ? 'selected-header-menu selected-menu'
                          : ''
                      }`}
                    />
                  );
                } else {
                  return <React.Fragment key={index}></React.Fragment>;
                }
              })}
            </TabList>
          </Box>
        </TabContext>
      </Box>
    </>
  );
};

export default HeaderSubMenu;
