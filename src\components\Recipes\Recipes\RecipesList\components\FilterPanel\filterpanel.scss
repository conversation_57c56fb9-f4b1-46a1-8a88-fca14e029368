.filter-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: auto;
  max-height: 80vh;
  background-color: var(--color-white);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  box-shadow: var(--box-shadow-xs);
  border: 1px solid var(--border-color-light-gray);
  z-index: 50;
  transition: all 0.3s ease-in-out;
  transform: translateY(100%);
  overflow-y: auto;

  @media (min-width: 768px) {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    width: 320px;
    max-height: none;
    border-radius: var(--border-radius-md);
    transform: translateY(0);
    z-index: auto;
  }

  &--open {
    transform: translateY(0);
  }

  &__overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;

    @media (min-width: 768px) {
      display: none;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color-light-gray);
    position: sticky;
    top: 0;
    background-color: var(--color-white);
    z-index: 10;
  }

  &__title {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin: 0;
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__clear-btn {
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    background: transparent;
    border: none;
    cursor: pointer;
    text-decoration: underline;
    text-underline-offset: 2px;

    &:hover {
      color: var(--btn-color-primary);
    }
  }

  &__close-btn {
    // padding: var(--spacing-xxs);
    // background: transparent;
    // border: none;
    display: flex;
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xxs);
    background: var(--color-secondary);
    color: var(--text-color-slate-gray);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }
  }

  &__content {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    font-family: var(--font-family-primary);
    margin: 0;
  }

  &__options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xsm);
  }

  &__option-btn {
    width: 100%;
    text-align: left;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background-color: var(--color-secondary);
    color: var(--text-color-slate-gray);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--text-color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
    }
  }

  // Range Section
  &__range-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__range-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  &__range-container {
    position: relative;
    height: 8px;
  }

  &__range-slider {
    position: absolute;
    width: 100%;
    height: 8px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    appearance: none;
    cursor: pointer;
    outline: none;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--color-primary);
      cursor: pointer;
      border: 2px solid var(--color-white);
      box-shadow: var(--shadow-sm);
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--color-primary);
      cursor: pointer;
      border: 2px solid var(--color-white);
      box-shadow: var(--shadow-sm);
    }

    &--overlay {
      background: transparent;
      top: 0;
    }
  }

  &__range-inputs {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__range-input {
    width: 80px;
    padding: var(--field-padding);
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-size: var(--font-size-sm);
    background-color: var(--color-white);
    color: var(--text-color-black);

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  &__range-separator {
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
  }

  // Chips Section
  &__chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  &__chip {
    padding: var(--spacing-xxs) var(--spacing-xsm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    text-transform: capitalize;
    background-color: var(--color-light-grayish-blue);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
    border: none;
    cursor: pointer;

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
    }

    &--allergen-active {
      background-color: var(--color-danger);
      color: var(--color-white);

      &:hover {
        background-color: var(--color-danger);
        color: var(--color-white);
      }
    }

    &--dietary-active {
      background-color: var(--color-success);
      color: var(--color-white);

      &:hover {
        background-color: var(--color-success);
        color: var(--color-white);
      }
    }

    &--loading {
      pointer-events: none;
      background-color: #f5f5f5;
      border-color: #e0e0e0;

      .skeleton-text {
        width: 60px;
        height: 14px;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 2px;
      }
    }
  }

  &__loading {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  &__no-data {
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
    font-style: italic;
    padding: var(--spacing-sm);
  }

  &__footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color-light-gray);

    @media (min-width: 768px) {
      display: none;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
