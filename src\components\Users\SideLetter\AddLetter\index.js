'use client';

import React, { useState, useRef } from 'react';
import {
  Box,
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';
import ImageIcon from '@mui/icons-material/Image';
import VideoLibraryIcon from '@mui/icons-material/VideoLibrary';
import LibraryMusicIcon from '@mui/icons-material/LibraryMusic';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { useDropzone } from 'react-dropzone';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import PermMediaIcon from '@mui/icons-material/PermMedia';
import TextSnippetIcon from '@mui/icons-material/TextSnippet';
import _ from 'lodash';

export default function AddLetters({
  setCreateModal,
  UserId,
  getTemplateDetails,
  currentPage,
  searchValue,
}) {
  const [loader, setLoader] = useState(false);
  const [acceptedMedia, setAcceptedMedia] = useState([]);
  const formikRef = useRef(null);
  const {
    getRootProps: getRootPropsMultiple,
    getInputProps: getInputPropsMultiplegetRootProps,
    // acceptedFiles,
  } = useDropzone({
    accept: {
      'application/pdf': [],
      'image/*': [],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [], // .xlsx
      'application/vnd.ms-excel': [], // .xls
      'application/msword': [], // .doc
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        [], // .docx
      'text/csv': [], // .csv
      // 'application/vnd.ms-powerpoint': [], // .ppt
      // 'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      //   [], // .pptx,
      'text/plain': [], // .txt
    },
    multiple: false,
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 200 * 1024 * 1024; // 200MB in bytes
      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      // var totalFiles = _.concat(acceptedMedia, acceptedFile);
      var totalFiles = _.concat(acceptedFile);
      if (rejectedFiles.length > 0) {
        setApiMessage(
          'error',
          'Please upload a PDF, image, Excel, Word, or CSV file.'
        );
        setAcceptedMedia([]);
        formikRef.current.setFieldValue('uploadedMedia', []);
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 200MB.');
      } else if (totalFiles.length > 11) {
        setApiMessage('error', 'Please upload less than 10 files');
      } else {
        setAcceptedMedia(totalFiles);
        formikRef.current.setFieldValue('uploadedMedia', totalFiles);
      }
    },
  });
  const uploadedmedia = (type, name) => {
    const filename = name;
    if (type.startsWith('image')) {
      return (
        <Box className="image-sec">
          <ImageIcon />
          <Typography className="p14 file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.startsWith('video')) {
      return (
        <Box className="image-sec">
          <VideoLibraryIcon />
          <Typography className="p14 file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.includes('pdf')) {
      return (
        <Box className="image-sec">
          <PictureAsPdfIcon />
          <Typography className="p14 file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else if (type.startsWith('audio')) {
      return (
        <Box className="image-sec">
          <LibraryMusicIcon />
          <Typography className="p14 file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    } else {
      return (
        <Box className="image-sec">
          <TextSnippetIcon />
          <Typography className="p14 file-name text-ellipsis-line">
            {filename}
          </Typography>
        </Box>
      );
    }
  };
  return (
    <Box>
      <Formik
        innerRef={formikRef}
        initialValues={{
          latterTitle: '',
          requireCheck: true,
          uploadedMedia: [],
          remark: '',
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          latterTitle: Yup.string().trim().required('This field is required'),
          uploadedMedia: Yup.array().min(
            1,
            'At least one department must be selected'
          ),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);
          const body = new FormData();
          requestData?.latterTitle &&
            body.append('side_letter_title', requestData?.latterTitle);
          requestData?.remark &&
            body.append('side_letter_description', requestData?.remark);
          UserId && body.append('recipient_user_id', UserId);
          requestData?.uploadedMedia &&
            requestData?.uploadedMedia?.length > 0 &&
            requestData?.uploadedMedia?.map((media) => {
              body.append(`side_letter_file`, media);
            });
          body.append('has_confirmation_required', requestData?.requireCheck);
          // let sendData = {
          //   side_letter_title: requestData?.latterTitle,
          //   side_letter_description: requestData?.remark
          //     ? requestData?.remark
          //     : null,
          //   recipient_user_id: UserId,
          // };
          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          };
          const ApiUrl = URLS.ADD_SIDE_LETTER;
          const method = 'post';
          try {
            const { status, data } = await axiosInstance[method](
              ApiUrl,
              body,
              config
            );
            if (status === 200 || status === 201) {
              if (data?.status) {
                setCreateModal(false);
                setApiMessage('success', data?.message);
                setLoader(false);
                getTemplateDetails(currentPage, searchValue);
              } else {
                setApiMessage('error', data?.message);
                setLoader(false);
              }
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="pt16">
              <CustomTextField
                fullWidth
                id="latterTitle"
                name="latterTitle"
                value={values?.latterTitle}
                label="Letter Title"
                required
                placeholder="Enter Letter Title"
                error={Boolean(touched.latterTitle && errors.latterTitle)}
                helperText={touched.latterTitle && errors.latterTitle}
                onBlur={handleBlur}
                onChange={(e) => {
                  if (event.target.value.length <= 60) {
                    handleChange(e);
                  }
                }}
              />
              <Typography className="sub-title-text text-align-end">
                {values?.latterTitle?.length + '/ 60'}
              </Typography>
            </Box>
            <Box>
              <Typography className="field-label pb4">
                Upload Media
                <span className="required"> *</span>
              </Typography>
              <Box className="upload-sec cursor-pointer w100 text-align">
                <Box
                  {...getRootPropsMultiple({ className: 'dropzone' })}
                  className="upload-area"
                >
                  <PermMediaIcon />
                  <input {...getInputPropsMultiplegetRootProps()} />
                  <Typography className="p14 upload-text">
                    Drop your media here
                  </Typography>
                </Box>
              </Box>
              {acceptedMedia && acceptedMedia?.length > 0 ? (
                <Box className="">
                  {acceptedMedia?.map((item, i) => (
                    <Box className="uploaded-media-sec">
                      {uploadedmedia(item?.type, item?.name)}
                      <DeleteOutlineIcon
                        className="cursor-pointer"
                        onClick={() => {
                          const files = acceptedMedia?.filter(
                            (id, index) => i !== index
                          );
                          setAcceptedMedia(files);
                          formikRef.current.setFieldValue(
                            'uploadedMedia',
                            files
                          );
                        }}
                      />
                    </Box>
                  ))}
                </Box>
              ) : (
                <></>
              )}
              {touched.uploadedMedia && errors.uploadedMedia && (
                <Typography
                  variant="body2"
                  color="error"
                  className="field-error"
                >
                  This field is required
                </Typography>
              )}
            </Box>
            <Box className="pt8">
              <CustomTextField
                id="remark"
                name="remark"
                multiline
                rows={2}
                fullWidth
                error={Boolean(touched.remark && errors.remark)}
                helperText={touched.remark && errors.remark}
                placeholder="Remark"
                value={values?.remark}
                className="additional-textfeild"
                label="Remark"
                onChange={(e) => {
                  if (event.target.value.length <= 160) {
                    handleChange(e);
                  }
                }}
              />
              <Typography className="sub-title-text text-align-end">
                {values?.remark?.length + '/ 160'}
              </Typography>
            </Box>
            <FormGroup className="pb24">
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                    // onChange={handleChange}
                    disableRipple
                    // disabled={isUpdate}
                  />
                }
                name="requireCheck"
                className="check-box-form p14"
                // name="checkedRolesIds"
                checked={values?.requireCheck}
                onChange={() => {
                  setFieldValue('requireCheck', !values?.requireCheck);
                }}
                label="Require Confirmation"
              />
            </FormGroup>

            <Box className="create-cancel-button justify-end">
              <CustomButton
                fullWidth
                variant="outlined"
                title="Cancel"
                onClick={() => setCreateModal(false)}
              />
              <CustomButton
                fullWidth
                type="submit"
                variant="contained"
                colorhover="#000000"
                title={`${loader ? 'Send Letter...' : 'Send Letter'}`}
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
