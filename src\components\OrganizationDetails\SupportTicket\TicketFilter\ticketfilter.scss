@import '@/styles/variable.scss';

.ticket-filter-wrap {
    .select {
        .MuiSelect-select {
            padding: 7.5px 7px 12px 15px;
        }
    }

    .ticket-filter-btns {
        .btns-wrap {

            .apply-btn-wrap,
            .cancel-btn-wrap {
                background: white;
                border: 1px solid $color-primary;
                display: flex;
                align-items: center;
                border-radius: 5px !important;
                cursor: pointer;
            }

            .apply-btn-wrap {
                padding: 3px 20px !important;
                background-color: $color-primary;
                color: white !important;

                &:hover {
                    box-shadow: none !important;
                    background-color: $color-primary !important;
                    color: white !important;
                }
            }

            .cancel-btn-wrap {
                padding: 3px 15px !important;
                color: $color-primary !important;
                background-color: white !important;

                &:hover {
                    box-shadow: none !important;
                    background-color: white !important;
                    color: $color-primary !important;
                }
            }
        }
    }
}