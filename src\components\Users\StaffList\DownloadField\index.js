'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import FieldCategorySection from './components/FieldCategorySection';
import SelectedFieldsList from './components/SelectedFieldsList';
import ExportPreview from './components/ExportPreview';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import './styles/DownloadField.scss';
import _ from 'lodash';

export default function DownloadField() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFields, setSelectedFields] = useState([]);
  const [expandedCategories, setExpandedCategories] = useState(['personal']);
  const [showPreview, setShowPreview] = useState(false);
  const [fieldCategories, setFieldCategories] = useState([]);
  const [loaderUser, setLoaderUser] = useState(true);

  const sectionMeta = {
    personal_details: {
      id: 'personal_details',
      name: 'Personal Information',
      icon: 'User',
    },
    employee_contract: {
      id: 'employee_contract',
      name: 'Employee Contract',
      icon: 'FileText',
    },
    right_to_work: {
      id: 'right_to_work',
      name: 'Right to Work',
      icon: 'Briefcase',
    },
    starter_form: {
      id: 'starter_form',
      name: 'Starter Form',
      icon: 'FileText',
    },
  };

  const transformResponse = (response) => {
    return Object.entries(response).map(([sectionKey, fields]) => {
      const meta = sectionMeta[sectionKey] || {
        id: sectionKey,
        name: sectionKey
          .replace(/_/g, ' ')
          .replace(/\b\w/g, (l) => l.toUpperCase()),
        icon: 'File',
      };

      return {
        id: meta?.id,
        name: meta?.name,
        icon: meta?.icon,
        fields: fields?.map((field) => ({
          id: field?.key,
          name: field?.label,
          type: getFieldType(field?.key), // Helper function to determine field type
          required: isFieldRequired(field?.key), // Helper function to determine if field is required
        })),
      };
    });
  };

  const getFieldType = (fieldKey) => {
    // Define field types based on field key patterns
    if (fieldKey.includes('date')) return 'date';
    if (fieldKey.includes('email')) return 'email';
    if (fieldKey.includes('phone') || fieldKey.includes('mobile')) return 'tel';
    if (fieldKey.includes('address')) return 'textarea';
    if (
      fieldKey.includes('gender') ||
      fieldKey.includes('status') ||
      fieldKey.includes('type')
    )
      return 'select';
    if (fieldKey.includes('number')) return 'number';
    if (fieldKey.startsWith('has_') || fieldKey.startsWith('is_'))
      return 'boolean';
    return 'text'; // Default type
  };

  const isFieldRequired = (fieldKey) => {
    // Define required fields based on business logic
    const requiredFields = [
      'employment_number',
      'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];
    return requiredFields.includes(fieldKey);
  };

  const getFieldOrder = (fieldKey) => {
    const requiredFields = [
      'employment_number',
      'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];
    const index = requiredFields.indexOf(fieldKey);
    return index !== -1 ? index + 1 : requiredFields.length + 1;
  };

  const getFieldList = async () => {
    setLoaderUser(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_USER_FIELDS);

      if (status === 200) {
        const transformedFields = transformResponse(data?.data);

        setFieldCategories(transformedFields);
        const requiredFields = transformedFields?.flatMap((category) =>
          category?.fields
            .filter((field) => field?.required)
            .map((field) => ({
              ...field,
              categoryId: category?.id,
              categoryName: category?.name,
              order: getFieldOrder(field?.id),
            }))
        );
        getStoredField(transformedFields, requiredFields);
        setLoaderUser(false);
      }
    } catch (error) {
      setLoaderUser(false);
      setFieldCategories([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const extractMatchingFields = (forms, keysToKeep) => {
    return keysToKeep
      .flatMap((section) =>
        section?.fields
          ?.filter((field) => forms?.includes(field?.id))
          .map((field) => ({
            ...field,
            categoryId: section?.id,
            categoryName: section?.name,
          }))
      )
      .map((field, index) => ({
        ...field,
        order: index + 1,
      }));
  };

  const getStoredField = async (requiredFields, selected) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STORED_USER_FIELDS
      );

      if (status === 200) {
        const matchingFields =
          data?.data?.user_field_order &&
          data?.data?.user_field_order?.length > 0
            ? extractMatchingFields(
                data?.data?.user_field_order,
                requiredFields
              )
            : selected;
        setSelectedFields(matchingFields);
      }
    } catch (error) {
      setFieldCategories([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Initialize with required fields
  useEffect(() => {
    getFieldList();
  }, []);

  const handleFieldToggle = (field, categoryId, categoryName) => {
    const fieldWithCategory = { ...field, categoryId, categoryName };

    setSelectedFields((prev) => {
      const isSelected = prev.some((f) => f.id === field.id);

      if (isSelected) {
        // Don't allow removing required fields
        if (field.required) return prev;
        return prev.filter((f) => f.id !== field.id);
      } else {
        const newOrder = prev.length + 1;
        return [...prev, { ...fieldWithCategory, order: newOrder }];
      }
    });
  };

  const handleCategorySelectAll = (categoryId, fields) => {
    const categoryFields = fields.map((field, index) => ({
      ...field,
      categoryId,
      categoryName: fieldCategories.find((cat) => cat?.id === categoryId)?.name,
      order: selectedFields?.length + index + 1,
    }));

    setSelectedFields((prev) => {
      // Remove existing fields from this category (except required ones)
      const filteredPrev = prev.filter(
        (f) => f?.categoryId !== categoryId || f?.required
      );

      // Add all fields from category
      const newFields = categoryFields.filter(
        (field) => !filteredPrev.some((f) => f?.id === field?.id)
      );

      return [...filteredPrev, ...newFields];
    });
  };

  const handleCategoryDeselectAll = (categoryId) => {
    setSelectedFields((prev) =>
      prev.filter((f) => f.categoryId !== categoryId || f.required)
    );
  };

  const handleFieldReorder = (dragIndex, hoverIndex) => {
    setSelectedFields((prev) => {
      const draggedField = prev[dragIndex];
      const newFields = [...prev];
      newFields.splice(dragIndex, 1);
      newFields.splice(hoverIndex, 0, draggedField);

      // Update order numbers
      return newFields.map((field, index) => ({
        ...field,
        order: index + 1,
      }));
    });
  };

  const handleRemoveField = (fieldId) => {
    setSelectedFields((prev) => {
      const field = prev.find((f) => f.id === fieldId);
      if (field?.required) return prev;

      return prev
        .filter((f) => f.id !== fieldId)
        .map((field, index) => ({
          ...field,
          order: index + 1,
        }));
    });
  };

  const toggleCategoryExpansion = (categoryId) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const filteredCategories = fieldCategories
    .map((category) => ({
      ...category,
      fields: category.fields.filter((field) =>
        field.name.toLowerCase().includes(searchTerm.toLowerCase())
      ),
    }))
    .filter((category) => category?.fields?.length > 0);

  const handlePreviewExport = () => {
    setShowPreview(true);
  };

  // Save field sequence
  const handleSaveConfiguration = async () => {
    const selectedFieldIds = _.map(selectedFields, 'id');
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.STORE_USER_FIELDS_SEQUENCE,
        {
          user_field_order: selectedFieldIds,
        }
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  return (
    <Box className="download-field-container">
      {/* Page Header */}
      <Box className="page-header">
        <Box className="header-content">
          <Box className="header-text">
            <Typography className="title-sm">
              Field Selection & Export Configuration
            </Typography>
            <Typography className="title-text">
              Customize which user data fields to export and configure their
              sequence
            </Typography>
          </Box>

          <Box className="preview-actions">
            {/* //header-actions */}
            {/* <Box className="sub-title-text field-counter">
              <span className="sub-title-text  counter-number">
                {selectedFields.length}
              </span>{' '}
              fields selected
            </Box> */}
            {/* <button onClick={handlePreviewExport} className="preview-button">
              <Icon
                name="Eye"
                size={16}
                strokeWidth={2}
                className="button-icon"
              />
              Preview Export
            </button> */}
            <CustomButton
              variant="outlined"
              title="Preview Export"
              startIcon={
                <Icon
                  name="Eye"
                  size={16}
                  strokeWidth={2}
                  className="button-icon"
                />
              }
              fullWidth={false}
              onClick={handlePreviewExport}
            />
            {/* <button onClick={handleSaveConfiguration} className="save-button">
              <Icon
                name="Save"
                size={16}
                strokeWidth={2}
                className="button-icon"
              />
              Save Configuration
            </button> */}
            <CustomButton
              variant="contained"
              title="Save Configuration"
              startIcon={
                <Icon
                  name="Save"
                  size={16}
                  strokeWidth={2}
                  className="button-icon"
                />
              }
              fullWidth={false}
              onClick={handleSaveConfiguration}
            />
          </Box>
        </Box>
      </Box>
      {/* Export Preview */}
      {showPreview ? (
        <Box className="export-preview-section">
          <ExportPreview
            selectedFields={selectedFields}
            onClose={() => setShowPreview(false)}
          />
        </Box>
      ) : (
        <>
          {/* Search Bar */}
          <Box className="search-section">
            <Box className="search-section-fields">
              <CustomSearch
                setSearchValue={setSearchTerm}
                searchValue={searchTerm}
                // onKeyPress={handleKeyPress}
              />
            </Box>
          </Box>

          {/* Main Content */}
          <Box className="main-content">
            {/* Available Fields */}
            <Box className="available-fields">
              <Typography className="body-text section-title">
                Available Fields
              </Typography>

              <Box className="categories-container">
                {loaderUser ? (
                  <Box className="content-loader no-data-found">
                    <CircularProgress className="loader" color="inherit" />
                  </Box>
                ) : (
                  <>
                    {filteredCategories?.map((category) => (
                      <FieldCategorySection
                        key={category?.id}
                        category={category}
                        selectedFields={selectedFields}
                        isExpanded={expandedCategories?.includes(category?.id)}
                        onToggleExpansion={toggleCategoryExpansion}
                        onFieldToggle={handleFieldToggle}
                        onSelectAll={handleCategorySelectAll}
                        onDeselectAll={handleCategoryDeselectAll}
                      />
                    ))}
                  </>
                )}
              </Box>
            </Box>

            {/* Selected Fields */}
            <Box className="selected-fields">
              <Typography className="body-text section-title">
                Selected Fields{' '}
                <span className="field-count">({selectedFields?.length})</span>
              </Typography>

              <SelectedFieldsList
                selectedFields={selectedFields}
                onReorder={handleFieldReorder}
                onRemoveField={handleRemoveField}
              />
            </Box>
          </Box>
        </>
      )}
      {/* Action Buttons */}
      {/* <Box className="action-buttons">
        <Box className="preview-actions">
          <button onClick={handlePreviewExport} className="preview-button">
            <Icon
              name="Eye"
              size={16}
              strokeWidth={2}
              className="button-icon"
            />
            Preview Export
          </button>

          <button onClick={handleSaveConfiguration} className="save-button">
            <Icon
              name="Save"
              size={16}
              strokeWidth={2}
              className="button-icon"
            />
            Save Configuration
          </button>
        </Box>

        <Box className="main-actions">
          <button
            onClick={() => navigate('/user-export-dashboard')}
            className="cancel-button"
          >
            Cancel
          </button>

          <button
            onClick={handleGenerateExport}
            disabled={selectedFields.length === 0}
            className="continue-button"
          >
            Continue to Preview
          </button>
        </Box>
      </Box> */}
    </Box>
  );
}
