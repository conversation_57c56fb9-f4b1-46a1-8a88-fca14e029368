import React from 'react';
import { Box, Button } from '@mui/material';
import { Formik, Form } from 'formik';
import CustomSelect from '@/components/UI/selectbox';
import './ticketfilter.scss';
import CustomButton from '@/components/UI/button';
export default function TicketFilter() {
  const priorityOptions = [
    { value: 'none', label: 'None' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
  ];

  const AssigneeOptions = [
    { label: '<PERSON>', value: 'john' },
    { label: '<PERSON>', value: 'jane' },
    { label: 'Doe Ray', value: 'doe' },
  ];

  const DateOptions = [
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'this-week' },
    { label: 'This Month', value: 'this-month' },
  ];

  const OwnerOptions = [
    { label: 'Alice', value: 'alice' },
    { label: 'Bob', value: 'bob' },
    { label: 'Charlie', value: 'charlie' },
  ];

  const initialValues = {
    priority: '',
    assignee: '',
    date: '',
    owner: '',
  };

  const handleSubmit = (values) => {
    console.log('Filters applied:', values);
  };

  return (
    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
      {({ values, handleChange, resetForm }) => (
        <Form className="ticket-filter-wrap">
          <Box>
            {/* Priority */}
            <Box className="pt32">
              <CustomSelect
                className="slected-wrap select"
                placeholder="Select priority"
                options={priorityOptions}
                value={values?.priority}
                name="priority"
                onChange={handleChange}
                label={<span>Priority</span>}
              />
            </Box>

            {/* Assignee */}
            <Box className="pt32">
              <CustomSelect
                className="slected-wrap select"
                placeholder="Select Assignee"
                options={AssigneeOptions}
                value={values?.assignee}
                name="assignee"
                onChange={handleChange}
                label={<span>Assignee</span>}
              />
            </Box>

            {/* Date */}
            <Box className="pt32">
              <CustomSelect
                className="slected-wrap select"
                placeholder="Select Date"
                options={DateOptions}
                value={values?.date}
                name="date"
                onChange={handleChange}
                label={<span>Date</span>}
              />
            </Box>

            {/* Owner */}
            <Box className="pt32">
              <CustomSelect
                className="slected-wrap select"
                placeholder="Select Owner"
                options={OwnerOptions}
                value={values?.owner}
                name="owner"
                onChange={handleChange}
                label={<span>Owner</span>}
              />
            </Box>

            {/* Buttons */}
            <Box className="ticket-filter-btns pt32">
              <Box className="btns-wrap d-flex gap-5 justify-end">
                <CustomButton
                  variant="contained"
                  background="#39596e"
                  className="apply-btn-wrap"
                  // onClick={handleApplyFilter}
                  title="Apply"
                />
                <CustomButton
                  variant="contained"
                  background="#FFFFFF"
                  className="cancel-btn-wrap"
                  // onClick={handleCancelFilter}
                  title="Clear"
                />
              </Box>
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
