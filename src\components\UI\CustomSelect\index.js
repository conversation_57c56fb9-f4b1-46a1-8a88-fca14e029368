import React from 'react';
import Select, { components } from 'react-select';
import { Box, FormLabel, Typography } from '@mui/material';
import './customSelect.scss';

const CustomSelect = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  isMulti = false,
  label,
  error,
  helperText,
  className = '',
  isSearchable = true,
  isClearable = true,
  isDisabled = false,
  required = false,
  name,
  defaultValue = '',
  menuPosition = 'absolute',
  menuPortalTarget,
  styles,
  showDot = false, // 👈 new prop
}) => {
  // Custom Option with dot
  const DotOption = (props) => (
    <components.Option {...props}>
      <Box display="flex" alignItems="center">
        {showDot && (
          <span
            style={{
              backgroundColor: props.data.color || '#39596e',
              width: 'var(--icon-size-xxs-min)',
              minWidth: 'var(--icon-size-xxs-min)',
              height: 'var(--icon-size-xxs-min)',
              borderRadius: 'var(--border-radius-full)',
              marginRight: 'var(--spacing-sm)',
            }}
          />
        )}
        {props.data.label}
      </Box>
    </components.Option>
  );

  // Custom SingleValue with dot
  const DotSingleValue = (props) => (
    <components.SingleValue {...props}>
      <Box display="flex" alignItems="center">
        {showDot && (
          <span
            style={{
              backgroundColor: props.data.color || '#39596e',
              width: 'var(--icon-size-xxs-min)',
              minWidth: 'var(--icon-size-xxs-min)',
              height: 'var(--icon-size-xxs-min)',
              borderRadius: 'var(--border-radius-full)',
              marginRight: 'var(--spacing-sm)',
            }}
          />
        )}
        {props.data.label}
      </Box>
    </components.SingleValue>
  );

  return (
    <Box className={`custom-select-wrapper ${className}`}>
      {label && (
        <FormLabel
          htmlFor={name}
          id={name}
          className={`field-label ${error ? 'error-label' : ''}`}
        >
          {label}
          {required && <span className="required">*</span>}
        </FormLabel>
      )}

      <Select
        options={options}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        isMulti={isMulti}
        isSearchable={isSearchable}
        isClearable={isClearable}
        isDisabled={isDisabled}
        name={name}
        aria-labelledby={name}
        className={`select-container ${error ? 'error-border' : ''}`}
        classNamePrefix="select"
        defaultValue={defaultValue}
        menuPosition={menuPosition}
        menuPortalTarget={menuPortalTarget}
        styles={styles}
        components={{
          Option: DotOption,
          SingleValue: DotSingleValue,
        }}
      />
      {error && helperText && (
        <Typography className="select-field-error-text">
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

export default CustomSelect;
