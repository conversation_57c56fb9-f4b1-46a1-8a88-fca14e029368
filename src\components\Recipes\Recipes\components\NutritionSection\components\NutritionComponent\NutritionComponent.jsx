import React, { useState, useEffect } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import './NutritionComponent.scss';

// Validation Schema for Nutrition
const nutritionValidationSchema = Yup.object().shape({
  calories: Yup.number()
    .min(0, 'Calories cannot be negative')
    .max(9999, 'Calories too high'),
  protein: Yup.number()
    .min(0, 'Protein cannot be negative')
    .max(999, 'Protein too high'),
  carbs: Yup.number()
    .min(0, 'Carbs cannot be negative')
    .max(999, 'Carbs too high'),
  fat: Yup.number().min(0, 'Fat cannot be negative').max(999, 'Fat too high'),
  fiber: Yup.number()
    .min(0, 'Fiber cannot be negative')
    .max(999, 'Fiber too high'),
  sugar: Yup.number()
    .min(0, 'Sugar cannot be negative')
    .max(999, 'Sugar too high'),
  sodium: Yup.number()
    .min(0, 'Sodium cannot be negative')
    .max(9999, 'Sodium too high'),
  cholesterol: Yup.number()
    .min(0, 'Cholesterol cannot be negative')
    .max(999, 'Cholesterol too high'),
});

const NutritionComponent = ({ formData, dispatch }) => {
  // Local state for nutrition to ensure immediate UI updates
  const [localNutrition, setLocalNutrition] = useState(() => {
    return (
      formData?.nutrition || {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        vitaminA: 0,
        vitaminC: 0,
        calcium: 0,
        iron: 0,
      }
    );
  });

  // Sync local state with prop changes
  useEffect(() => {
    setLocalNutrition(
      formData?.nutrition || {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        vitaminA: 0,
        vitaminC: 0,
        calcium: 0,
        iron: 0,
      }
    );
  }, [formData?.nutrition]);

  // Use local state for immediate UI updates
  const nutrition = localNutrition;

  // Nutrition field configuration
  const nutritionFields = [
    {
      key: 'calories',
      label: 'Calories',
      icon: 'Zap',
      unit: 'kcal',
      color: 'nutrition-section__icon--calories',
    },
    {
      key: 'protein',
      label: 'Protein',
      icon: 'Beef',
      unit: 'g',
      color: 'nutrition-section__icon--protein',
    },
    {
      key: 'carbs',
      label: 'Carbohydrates',
      icon: 'Wheat',
      unit: 'g',
      color: 'nutrition-section__icon--carbs',
    },
    {
      key: 'fat',
      label: 'Total Fat',
      icon: 'Droplets',
      unit: 'g',
      color: 'nutrition-section__icon--fat',
    },
    {
      key: 'fiber',
      label: 'Dietary Fiber',
      icon: 'Leaf',
      unit: 'g',
      color: 'nutrition-section__icon--fiber',
    },
    {
      key: 'sugar',
      label: 'Sugars',
      icon: 'Candy',
      unit: 'g',
      color: 'nutrition-section__icon--sugar',
    },
    {
      key: 'sodium',
      label: 'Sodium',
      icon: 'Salad',
      unit: 'mg',
      color: 'nutrition-section__icon--sodium',
    },
    {
      key: 'cholesterol',
      label: 'Cholesterol',
      icon: 'Heart',
      unit: 'mg',
      color: 'nutrition-section__icon--cholesterol',
    },
    {
      key: 'vitaminA',
      label: 'Vitamin A',
      icon: 'Sun',
      unit: 'IU',
      color: 'nutrition-section__icon--vitamin-a',
    },
    {
      key: 'vitaminC',
      label: 'Vitamin C',
      icon: 'Cherry',
      unit: 'mg',
      color: 'nutrition-section__icon--vitamin-c',
    },
    {
      key: 'calcium',
      label: 'Calcium',
      icon: 'Milk',
      unit: 'mg',
      color: 'nutrition-section__icon--calcium',
    },
    {
      key: 'iron',
      label: 'Iron',
      icon: 'Dumbbell',
      unit: 'mg',
      color: 'nutrition-section__icon--iron',
    },
  ];

  // Update nutrition value
  const updateNutrition = (key, value) => {
    const numericValue = value === '' ? 0 : parseFloat(value) || 0;
    const updatedNutrition = { ...nutrition, [key]: numericValue };

    // Update local state immediately for UI responsiveness
    setLocalNutrition(updatedNutrition);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: { nutrition: updatedNutrition },
      });
    }
  };

  // Validate nutrition data
  const validateNutrition = async () => {
    try {
      await nutritionValidationSchema.validate(nutrition, {
        abortEarly: false,
      });
      return {};
    } catch (error) {
      const errors = {};
      error?.inner?.forEach?.((err) => {
        if (err?.path) {
          errors[err.path] = err?.message || 'Invalid value';
        }
      });
      return errors;
    }
  };

  return (
    <div className="nutrition-component">
      {/* Header */}
      <div className="nutrition-component__header">
        <div className="nutrition-component__header-content">
          <Icon name="BarChart3" size={20} color="var(--color-primary)" />
          <div>
            <h3 className="nutrition-component__title">Nutrition Facts</h3>
            <p className="nutrition-component__description">
              Add nutritional information per serving
            </p>
          </div>
        </div>
      </div>

      {/* Main Nutrition Fields */}
      <div className="nutrition-component__grid">
        {nutritionFields?.map?.((field) => (
          <div key={field?.key} className="nutrition-component__field-group">
            <CustomTextField
              label={
                <div className="nutrition-component__field-label">
                  <Icon
                    name={field?.icon}
                    size={16}
                    color="currentColor"
                    className={field?.color}
                  />
                  <span>{field?.label}</span>
                </div>
              }
              type="number"
              value={nutrition?.[field?.key] || ''}
              onChange={(e) =>
                updateNutrition(field?.key, e?.target?.value || '')
              }
              inputProps={{ step: 0.1, min: 0 }}
              placeholder="0"
              helperText={field?.unit}
              fullWidth
            />
          </div>
        )) || []}
      </div>

      {/* Nutrition Summary */}
      {(nutrition?.calories > 0 ||
        nutrition?.protein > 0 ||
        nutrition?.carbs > 0 ||
        nutrition?.fat > 0) && (
        <div className="nutrition-component__summary">
          <h4 className="nutrition-component__summary-title">
            Nutrition Summary
          </h4>
          <div className="nutrition-component__summary-grid">
            <div className="nutrition-component__summary-item">
              <span className="nutrition-component__summary-label">
                Total Calories
              </span>
              <span className="nutrition-component__summary-value">
                {nutrition?.calories || 0} kcal
              </span>
            </div>
            <div className="nutrition-component__summary-item">
              <span className="nutrition-component__summary-label">
                Macronutrients
              </span>
              <span className="nutrition-component__summary-value">
                P: {nutrition?.protein || 0}g | C: {nutrition?.carbs || 0}g | F:{' '}
                {nutrition?.fat || 0}g
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NutritionComponent;
