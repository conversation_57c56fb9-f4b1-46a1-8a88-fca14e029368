import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { InputAdornment } from '@mui/material';
import { getNutritionList } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './NutritionComponent.scss';

const NutritionComponent = ({ formData, dispatch }) => {
  // State for nutrition list management
  const [nutritionDatabase, setNutritionDatabase] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(true);

  // Local state for immediate UI updates
  const [localNutritions, setLocalNutritions] = useState(() => {
    // Extract nutrition from all ingredients
    const allNutritions = [];
    formData?.ingredients?.forEach?.((ingredient) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        allNutritions.push(...ingredient.nutrition);
      }
    });
    return allNutritions;
  });

  // Use local state for immediate UI updates
  const nutritions = localNutritions;

  // Sync local state with prop changes
  useEffect(() => {
    // Extract nutrition from all ingredients
    const allNutritions = [];
    formData?.ingredients?.forEach?.((ingredient) => {
      if (ingredient?.nutrition && Array.isArray(ingredient.nutrition)) {
        allNutritions.push(...ingredient.nutrition);
      }
    });
    setLocalNutritions(allNutritions);
  }, [formData?.ingredients]);

  // Fetch nutrition options from API
  useEffect(() => {
    const fetchNutritions = async () => {
      try {
        setLoading(true);
        const response = await getNutritionList();

        // Transform API response to component format
        const transformedNutritions =
          response?.nutrition?.map((nutrition) => ({
            id: nutrition?.id,
            attribute_title: nutrition?.attribute_title,
            description: nutrition?.attribute_description || '',
            unit: 'g', // Default unit, can be customized
            icon: 'Activity', // Default icon
          })) || [];

        setNutritionDatabase(transformedNutritions);
      } catch (error) {
        console.error('Error fetching nutritions:', error);
        setApiMessage('error', 'Failed to load nutrition options');
        setNutritionDatabase([]);
      } finally {
        setLoading(false);
      }
    };

    fetchNutritions();
  }, []);

  // Helper function to update ingredients array with new nutrition data
  const updateIngredientsWithNutrition = (updatedNutritions) => {
    const updatedIngredients = [...(formData?.ingredients || [])];

    // If no ingredients exist, create a default one for nutrition
    if (updatedIngredients.length === 0) {
      updatedIngredients.push({
        id: 'nutrition-container',
        name: 'Nutrition Information',
        nutrition: updatedNutritions,
      });
    } else {
      // Update the first ingredient with nutrition data
      // or find existing nutrition container
      let nutritionIndex = updatedIngredients.findIndex(
        (ing) => ing.id === 'nutrition-container' || ing.nutrition
      );

      if (nutritionIndex === -1) {
        // Add nutrition to first ingredient if no nutrition container exists
        nutritionIndex = 0;
      }

      updatedIngredients[nutritionIndex] = {
        ...updatedIngredients[nutritionIndex],
        nutrition: updatedNutritions,
      };
    }

    return updatedIngredients;
  };

  // Filter nutrition suggestions based on search
  const filteredSuggestions =
    nutritionDatabase?.filter?.((nutrition) =>
      nutrition?.attribute_title
        ?.toLowerCase?.()
        ?.includes?.(searchTerm?.toLowerCase?.() || '')
    ) || [];

  // Add nutrition to the list
  const addNutrition = (nutritionData) => {
    const newNutrition = {
      ...nutritionData,
      value: 0,
      unit: nutritionData?.unit || 'g',
    };

    const updatedNutritions = [...(nutritions || []), newNutrition];

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Dispatch to parent
    if (dispatch) {
      const updatedIngredients =
        updateIngredientsWithNutrition(updatedNutritions);
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          ingredients: updatedIngredients,
        },
      });
    }

    setSearchTerm('');
    setShowSuggestions(false);
  };

  // Update nutrition value
  const updateNutrition = (index, field, value) => {
    if (!nutritions?.[index]) return;

    const updatedNutritions = [...(nutritions || [])];
    updatedNutritions[index] = {
      ...updatedNutritions[index],
      [field]: field === 'value' ? parseFloat(value) || 0 : value,
    };

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Dispatch to parent
    if (dispatch) {
      const updatedIngredients =
        updateIngredientsWithNutrition(updatedNutritions);
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          ingredients: updatedIngredients,
        },
      });
    }
  };

  // Remove nutrition from list
  const removeNutrition = (index) => {
    const updatedNutritions = nutritions?.filter?.((_, i) => i !== index) || [];

    // Update local state immediately for UI responsiveness
    setLocalNutritions(updatedNutritions);

    // Dispatch to parent
    if (dispatch) {
      const updatedIngredients =
        updateIngredientsWithNutrition(updatedNutritions);
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: {
          ingredients: updatedIngredients,
        },
      });
    }
  };

  // Get unique nutritions with combined values
  const getUniqueNutritions = () => {
    const nutritionMap = new Map();

    nutritions?.forEach?.((nutrition) => {
      const key = nutrition?.id || nutrition?.attribute_title;
      if (nutritionMap.has(key)) {
        // Combine values for same nutrition type
        const existing = nutritionMap.get(key);
        existing.value += nutrition?.value || 0;
      } else {
        nutritionMap.set(key, { ...nutrition });
      }
    });

    return Array.from(nutritionMap.values());
  };

  const uniqueNutritions = getUniqueNutritions();

  // Handle search input
  const handleSearchChange = (e) => {
    const value = e?.target?.value || '';
    setSearchTerm(value);
    setShowSuggestions(value?.length >= 2);
  };

  // Handle suggestion click
  const handleSuggestionClick = (nutrition) => {
    // Check if nutrition already exists
    const exists = nutritions?.some?.(
      (n) =>
        n?.id === nutrition?.id ||
        n?.attribute_title === nutrition?.attribute_title
    );

    if (!exists) {
      addNutrition(nutrition);
    } else {
      setApiMessage('warning', 'This nutrition is already added');
      setSearchTerm('');
      setShowSuggestions(false);
    }
  };

  if (loading) {
    return (
      <div className="nutrition-component">
        <div className="nutrition-component__loading">
          <Icon name="Loader2" size={24} color="var(--color-primary)" />
          <span>Loading nutrition options...</span>
        </div>
      </div>
    );
  }
  console.log('uniqueNutritions', nutritions);

  return (
    <div className="nutrition-component">
      {/* Header */}
      <div className="nutrition-component__header">
        <div className="nutrition-component__header-content">
          <Icon name="BarChart3" size={20} color="var(--color-primary)" />
          <div>
            <h3 className="nutrition-component__title">
              Nutrition Information
            </h3>
            <p className="nutrition-component__description">
              Add nutritional values per serving
            </p>
          </div>
        </div>
      </div>

      {/* Search and Add Nutrition */}
      <div className="nutrition-component__search">
        <div className="nutrition-component__search-container">
          <CustomTextField
            placeholder="Search nutrition to add..."
            value={searchTerm}
            onChange={handleSearchChange}
            onFocus={() => setShowSuggestions(searchTerm?.length >= 2)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Icon
                    name="Search"
                    size={16}
                    color="var(--text-color-slate-gray)"
                  />
                </InputAdornment>
              ),
            }}
            fullWidth
          />

          {/* Suggestions Dropdown */}
          {showSuggestions && filteredSuggestions?.length > 0 && (
            <div className="nutrition-component__suggestions">
              {filteredSuggestions?.slice(0, 5)?.map?.((nutrition) => (
                <button
                  key={nutrition?.id}
                  onClick={() => handleSuggestionClick(nutrition)}
                  className="nutrition-component__suggestion-item"
                >
                  <Icon
                    name="Activity"
                    size={16}
                    color="var(--color-primary)"
                  />
                  <span>{nutrition?.attribute_title}</span>
                  <Icon
                    name="Plus"
                    size={14}
                    color="var(--text-color-slate-gray)"
                  />
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Nutrition List */}
      {(nutritions?.length || 0) > 0 && (
        <div className="nutrition-component__list">
          <h4 className="nutrition-component__list-title">
            Added Nutritions ({nutritions?.length || 0})
          </h4>
          <div className="nutrition-component__items">
            {nutritions?.map?.((nutrition, index) => (
              <div
                key={`${nutrition?.id}-${index}`}
                className="nutrition-component__item"
              >
                <div className="nutrition-component__item-info">
                  <Icon
                    name="Activity"
                    size={16}
                    color="var(--color-primary)"
                  />
                  <span className="nutrition-component__item-name">
                    {nutrition?.attribute_title}
                  </span>
                </div>

                <div className="nutrition-component__item-controls">
                  <CustomTextField
                    type="number"
                    value={nutrition?.value || ''}
                    onChange={(e) =>
                      updateNutrition(index, 'value', e?.target?.value || '')
                    }
                    placeholder="0"
                    inputProps={{ step: 0.1, min: 0 }}
                    size="small"
                    style={{ width: '80px' }}
                  />
                  <span className="nutrition-component__item-unit">
                    {nutrition?.unit_title || 'g'}
                  </span>
                  <button
                    onClick={() => removeNutrition(index)}
                    className="nutrition-component__item-remove"
                    aria-label={`Remove ${nutrition?.id}`}
                  >
                    <Icon name="X" size={14} color="currentColor" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Unique Nutritions Summary */}
      {uniqueNutritions?.length > 0 &&
        uniqueNutritions?.length !== nutritions?.length && (
          <div className="nutrition-component__summary">
            <h4 className="nutrition-component__summary-title">
              Combined Nutrition Summary ({uniqueNutritions?.length})
            </h4>
            <div className="nutrition-component__summary-items">
              {uniqueNutritions?.map?.((nutrition) => (
                <div
                  key={nutrition?.id || nutrition?.attribute_title}
                  className="nutrition-component__summary-item"
                >
                  <span className="nutrition-component__summary-label">
                    {nutrition?.attribute_title}
                  </span>
                  <span className="nutrition-component__summary-value">
                    {nutrition?.value?.toFixed?.(1) || 0}{' '}
                    {nutrition?.unit || 'g'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

      {/* Empty State */}
      {(nutritions?.length || 0) === 0 && (
        <div className="nutrition-component__empty">
          <Icon
            name="Activity"
            size={48}
            color="var(--text-color-slate-gray)"
          />
          <h4>No Nutrition Added</h4>
          <p>Search and add nutrition information for this recipe</p>
        </div>
      )}
    </div>
  );
};

export default NutritionComponent;
