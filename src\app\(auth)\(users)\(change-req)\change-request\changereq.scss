@import '@/styles/variable.scss';

.change-request {
  .change-request-section {
    background-color: var(--color-white);
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
    padding: var(--spacing-xxl) var(--spacing-3xl) var(--spacing-5xl);
    border-radius: var(--border-radius-lg);
    // margin-top: 24px;
    @media (max-width: 1200px) {
      padding: var(--spacing-xxl) var(--spacing-2xl);
      .folder-desc-divider {
        margin: var(--spacing-sm) var(--spacing-none) !important;
      }
    }

    @media (max-width: 480px) {
      padding: var(--spacing-xxl) var(--spacing-lg);
      .upload-text {
        font-size: var(--font-size-xs) !important;
        line-height: var(--line-height-sm) !important;
      }
    }
  }
  .add-edit-change-request {
    background-color: var(--color-white);
    padding: var(--spacing-xxl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-xs);
    position: relative;
  }
  .bg-branch-transparent {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    width: auto;
    word-break: break-word;

    .cr-branch-name {
      background-color: color-mix(in srgb, var(--list-branch-color) 30%, white);
      color: var(--list-branch-color);
      padding: var(--spacing-tiny) var(--spacing-sm);
      border-radius: var(--border-radius-xs);
    }
  }
  .Upload-files {
    svg {
      fill: var(--color-primary);
      line,
      path {
        stroke: var(--color-primary);
      }
    }
    .upload-icon-wrap {
      path {
        stroke: none;
      }
    }
  }
  .upload-sec {
    margin-bottom: var(--spacing-xxl);
  }
  .selected-files {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 640px;
    margin-bottom: var(--spacing-sm);
    svg {
      font-size: var(--icon-size-xsm);
      cursor: pointer;
      // fill: var(--icon-color-primary);
      path {
        // stroke: var(--icon-color-primary);
      }
    }
    .file-name {
      width: calc(100% - 10px - 24px);
      p {
        word-break: break-all;
      }
    }
  }
  .selected-view-files {
    margin-bottom: var(--spacing-xxs) !important;
    svg {
      font-size: var(--icon-size-xsm);
      cursor: pointer;
    }
  }
  .cr-accordion {
    &::before {
      position: initial !important;
    }
    .MuiAccordionSummary-root {
      min-height: 48px;
      margin-top: var(--spacing-lg);
    }
    .accordion-heading .MuiAccordionSummary-content {
      display: flex;
      align-items: center;
      margin: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm)
        var(--spacing-none);
      .subject-section {
        display: flex;
        align-items: center;
      }
      .date-text {
        color: var(--text-light-dark);
        width: 100%;
        max-width: fit-content;
      }
      .Mui-expanded {
        margin: var(--spacing-md) var(--spacing-none);
      }
    }
  }
  .cr-accordion.MuiPaper-root {
    // border: 1px solid $color-primary;
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-xl);
    // border-bottom-left-radius: 20px;
    // border-bottom-right-radius: 20px;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    &:last-of-type {
      border-bottom-left-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md);
    }
  }
  .folder-desc-divider {
    border-bottom: var(--normal-sec-border);
    margin: var(--spacing-3xl) 0 !important;
  }
  .actions-cr {
    width: 80px;
  }
}
.staff-filter {
  // padding: 20px 20px;
  .staff-filter-button {
    display: flex;
    column-gap: 10px;
    margin-top: 24px;
  }
  .check-box-form {
    .check-box {
      .check-icon {
        path {
          fill: $color-primary;
        }
      }
    }
  }
}
.cr-details-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-xs);
  height: 100%;
  @media (max-width: 1200px) {
    padding: var(--spacing-xxl) var(--spacing-2xl);
    .folder-desc-divider {
      margin: var(--spacing-lg) var(--spacing-none) !important;
    }
  }
  @media (max-width: 480px) {
    padding: var(--spacing-xxl) var(--spacing-lg);
  }
}
.cr-filter-button {
  display: flex;
  column-gap: var(--spacing-sm);
  margin-top: var(--spacing-xxl);
}
.pt27 {
  padding-top: var(--spacing-xxl) !important;
}
.remark-width {
  max-width: 245px;
  width: 100%;
  @media (max-width: 575px) {
    max-width: 100%;
  }
}
.dsr-page-section {
  background-color: var(--color-white);
  padding: var(--spacing-xxl) var(--spacing-3xl) var(--spacing-5xl);
  border-radius: var(--border-radius-lg);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  @media (max-width: 1200px) {
    padding: var(--spacing-xxl) var(--spacing-2xl);
    .folder-desc-divider {
      margin: var(--spacing-lg) var(--spacing-none) !important;
    }
  }
  @media (max-width: 480px) {
    padding: var(--spacing-xxl) var(--spacing-lg);
  }
  .p0 {
    padding: var(--spacing-none) !important;
  }
  .dsr-tabs {
    .tab-list-sec {
      border-bottom: var(--normal-sec-border);
      .tab-name {
        text-transform: none;
        font-family: var(--font-family-primary);
        font-size: var(--font-size-base);
        line-height: var(--line-height-md);
        font-weight: var(--font-weight-regular);
        color: var(--text-color-black);
        opacity: var(--opacity-10);
        width: 50%;
        text-transform: uppercase;
      }
      .MuiTabs-indicator {
        background-color: var(--color-primary);
        height: 3px;
      }
      .Mui-selected {
        color: var(--text-color-primary);
      }
    }
  }
}
.details-user-icon {
  .MuiAvatar-root {
    height: 50px;
    width: 50px;
  }
}
