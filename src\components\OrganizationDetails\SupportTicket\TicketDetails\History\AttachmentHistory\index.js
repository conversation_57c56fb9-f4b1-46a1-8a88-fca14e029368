import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  // Utility function to format time
  const formatDate = (date) => {
    const options = { day: '2-digit', month: 'short' };
    return date.toLocaleDateString('en-US', options);
  };

  const formatCreatedTime = (date) => {
    const options = {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    };
    return date.toLocaleString('en-US', options);
  };

  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Divider orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Divider orientation="vertical" className="vertical-divider" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                <CalendarMonthIcon className="calender-icon" />
                <Typography component="p" className="header-date">
                  {formatDate(entry.date)}
                </Typography>
              </Box>
              <Typography component="p" className="time-wrap">
                {formatCreatedTime(entry.date)}
              </Typography>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon className="circle-wrap" />
                <Typography className="file-status-wrap">
                  {entry.fileStatus}
                </Typography>
              </Box>
              <Typography className="file-name">
                <span className="file-name-text">File Name </span>
                {entry.fileName}
              </Typography>
              <Typography className="attachment-type-wrap">
                <span className="attachment-type-text">Attachment Type </span>
                {entry.attachmentType}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
