import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './IngredientsCard.scss';

const IngredientsCard = ({ ingredients }) => {
  return (
    <div className="ingredients-card">
      <div className="ingredients-card__header">
        <p className="ingredients-card__title">
          <Icon name="ShoppingCart" size={20} color="currentColor" />
          <span>Ingredients</span>
        </p>
      </div>

      <div className="ingredients-card__content">
        <div className="ingredients-card__list">
          {ingredients.map((ingredient) => (
            <div key={ingredient?.id} className="ingredients-card__item">
              <div className="ingredients-card__item-header">
                <p className="ingredients-card__item-name">
                  {ingredient?.ingredient_name}
                </p>
                <p className="ingredients-card__item-cost">
                  ${ingredient?.ingredient_cost?.toFixed(2)}
                </p>
              </div>

              <div className="ingredients-card__item-details">
                <div className="ingredients-card__detail">
                  <span className="ingredients-card__detail-label">
                    Quantity:
                  </span>
                  <span className="ingredients-card__detail-value">
                    {ingredient?.ingredient_quantity}
                    <span className="ingredients-card__measure-title">
                      {ingredient?.measure_title}
                    </span>
                  </span>
                </div>
                <div className="ingredients-card__detail">
                  <span className="ingredients-card__detail-label">
                    Wastage:
                  </span>
                  <span className="ingredients-card__detail-value">
                    {ingredient?.ingredient_wastage}%
                  </span>
                </div>
              </div>

              <div className="ingredients-card__item-methods">
                {ingredient.is_preparation_method === 1 && (
                  <div className="ingredients-card__method">
                    <span className="ingredients-card__method-label">
                      Prep:
                    </span>
                    <span className="ingredients-card__method-value">
                      {ingredient?.preparation_method_title || 'Not specified'}
                    </span>
                  </div>
                )}
                {ingredient.is_ingredient_cooking_method === 1 && (
                  <div className="ingredients-card__method">
                    <span className="ingredients-card__method-label">
                      Cook:
                    </span>
                    <span className="ingredients-card__method-value">
                      {ingredient?.ingredient_cooking_method_title ||
                        'Not specified'}
                    </span>
                  </div>
                )}
                {ingredient.ingredient_description && (
                  <div className="ingredients-card__method">
                    <span className="ingredients-card__method-label">
                      Notes:
                    </span>
                    <span className="ingredients-card__method-value">
                      {ingredient?.ingredient_description}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default IngredientsCard;
