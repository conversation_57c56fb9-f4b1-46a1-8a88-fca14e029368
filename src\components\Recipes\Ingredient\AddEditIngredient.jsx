'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Divider } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
import {
  getCurrencySymbol,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { useRouter, useSearchParams } from 'next/navigation';
import { useRecipeMeasures } from '@/hooks/useRecipeMeasures';
import { staticOptions } from '@/helper/common/staticOptions';
import {
  getAllergenList,
  getNutritionList,
  getDietaryList,
  createIngredient,
  updateIngredient,
  getIngredientList,
  getIngredientById,
} from '@/services/recipeService';
import TopBackPageButton from '@/components/TopBackPage/TopBackPage';
import AuthContext from '@/helper/authcontext';
import BasicDetails from './components/BasicDetails';
import ConversionSection from './components/ConversionSection';
import NutritionInfo from './components/NutritionInfo';
import AllergenInfo from './components/AllergenInfo';
import DietarySuitability from './components/DietarySuitability';
import ContentLoader from '@/components/UI/ContentLoader';
import './ingredient.scss';

export default function AddEditIngredient({ isUpdate }) {
  const { authState } = useContext(AuthContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const ingredientId = searchParams.get('id');
  const [loader, setLoader] = useState(false);
  const [singleData, setSingleData] = useState('');
  const [nutritionData, setNutritionData] = useState([]);
  const [allergenData, setAllergenData] = useState([]);
  const [dietaryData, setDietaryData] = useState([]);
  const [categoryData, setCategoryData] = useState([]);
  const { unitsOfMeasureOptions } = useRecipeMeasures();
  const isDefault = singleData?.isDefault || false;

  const currency = getCurrencySymbol(authState?.currency_details);

  const initialValues = {
    ingredient_name: singleData?.ingredient_name || '',
    ingredient_description: singleData?.ingredient_description || '',
    ingredient_status: singleData?.ingredient_status || 'active',
    units_of_measure:
      singleData?.unit?.unit_title || unitsOfMeasureOptions[0]?.value || 'g',
    ingredient_cost: singleData?.cost_per_unit || '',
    ingredient_wastage: singleData?.waste_percentage || '',
    category_tags: singleData?.categories?.[0]?.id || '',
    conversions:
      singleData?.conversions?.length > 0
        ? singleData.conversions.map((conv) => ({
            from_value: conv?.from_measure_value || '',
            from_unit: conv?.fromUnit?.unit_title || '',
            to_value: conv?.to_measure_value || '',
            to_unit: conv?.toUnit?.unit_title || '',
          }))
        : [{ from_value: '', from_unit: '', to_value: '', to_unit: '' }],
    // Dynamic nutrition fields based on API data
    ...nutritionData?.reduce((acc, field) => {
      const existingNutrition = singleData?.nutrition_attributes?.find(
        (n) => n?.id === field?.id
      );
      // If nutrition exists in the response, use its values (even if null/empty)
      acc[`nutrition_${field?.id}`] = existingNutrition
        ? existingNutrition?.unit || ''
        : '';
      acc[`nutrition_measure_${field?.id}`] = existingNutrition
        ? existingNutrition?.unit_of_measure || 1
        : 1;
      return acc;
    }, {}),
    // Dynamic dietary fields based on API data
    ...dietaryData?.reduce((acc, item) => {
      const isDietarySelected = singleData?.dietary_attributes?.some(
        (d) => d?.id === item?.id
      );
      acc[`dietary_${item?.id}`] = isDietarySelected || false;
      return acc;
    }, {}),
    // Dynamic allergen fields based on API data
    ...allergenData?.reduce((acc, item) => {
      const isAllergenSelected = singleData?.allergy_attributes?.some(
        (a) => a?.id === item?.id
      );
      acc[`allergen_${item?.id}`] = isAllergenSelected || false;
      return acc;
    }, {}),
  };

  // API function to get nutrition data
  const fetchNutritionData = async () => {
    try {
      const response = await getNutritionList();

      setNutritionData(response?.nutrition);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setNutritionData([]);
    }
  };

  // API function to get allergen data
  const fetchAllergenData = async () => {
    try {
      const response = await getAllergenList(
        '',
        1,
        { status: 'active' },
        1000,
        { key: 'attribute_title', value: 'ASC' }
      );

      setAllergenData(response?.allergens);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setAllergenData([]);
    }
  };

  // API function to get dietary data
  const fetchDietaryData = async () => {
    try {
      const response = await getDietaryList(
        '',
        '',
        { status: 'active' },
        '',
        ''
      );
      const dietaryOptions =
        response?.dietary?.map((item) => ({
          label: item?.attribute_title,
          value: item?.id,
          id: item?.id,
        })) || [];
      setDietaryData(dietaryOptions);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setDietaryData([]);
    }
  };

  // API function to get category data
  const fetchCategoryData = async () => {
    try {
      const response = await getIngredientList(
        '',
        1,
        { status: 'active' },
        1000,
        { key: 'category_name', value: 'ASC' }
      );
      const categoryOptions =
        response?.ingredients?.map((item) => ({
          label: item?.category_name,
          value: item?.id,
          id: item?.id,
        })) || [];
      setCategoryData(categoryOptions);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setCategoryData([]);
    }
  };

  // API function to get single ingredient data for editing
  const fetchIngredientData = async (ingredientId) => {
    try {
      const response = await getIngredientById(ingredientId);

      if (response?.ingredient) {
        const ingredient = response.ingredient;
        setSingleData(ingredient);
      } else {
        setApiMessage('error', 'Ingredient not found');
        router.push('/recipes/ingredients');
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch ingredient data'
      );
      router.push('/recipes/ingredients');
    }
  };

  // Fetch all API data on component mount
  useEffect(() => {
    fetchNutritionData();
    fetchAllergenData();
    fetchDietaryData();
    fetchCategoryData();
  }, []);

  useEffect(() => {
    if (isUpdate && ingredientId) {
      // Fetch ingredient data using the ID from search params
      fetchIngredientData(ingredientId);
    } else if (isUpdate) {
      setApiMessage('error', 'Ingredient ID not found in URL');
    } else {
      setSingleData('');
    }
  }, [isUpdate, ingredientId]);

  const handleRedirect = () => {
    router.back();
  };

  // Always show form, don't wait for all data to be loaded
  const shouldShowForm = !isUpdate || (isUpdate && singleData);

  return (
    <>
      <TopBackPageButton
        title={`${isUpdate ? 'Update' : 'Add'} Ingredient`}
        onBackClick={handleRedirect}
      />
      <Box className="section-right-content ingredient-form-container">
        {shouldShowForm ? (
          <Formik
            initialValues={initialValues}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              ingredient_name: Yup.string()
                .trim()
                .required('This field is required'),
              ingredient_status: Yup.string()
                .trim()
                .required('This field is required'),
              units_of_measure: Yup.string()
                .trim()
                .required('This field is required'),
              ingredient_cost: Yup.string()
                .trim()
                .required('This field is required'),
            })}
            onSubmit={async (requestData) => {
              setLoader(true);
              try {
                // Get selected categories (array of IDs)
                const selectedCategories = Array.isArray(
                  requestData?.category_tags
                )
                  ? requestData?.category_tags
                  : requestData?.category_tags
                    ? [requestData?.category_tags]
                    : [];

                // Get selected allergens (array of IDs)
                const selectedAllergens = allergenData
                  .filter((allergen) => requestData[`allergen_${allergen?.id}`])
                  .map((allergen) => allergen?.id);

                // Get selected dietary info (array of IDs)
                const selectedDietary = dietaryData
                  .filter((dietary) => requestData[`dietary_${dietary?.id}`])
                  .map((dietary) => dietary?.id);

                // Get nutrition data with proper structure
                const nutritions = nutritionData
                  .filter(
                    (nutrition) => requestData[`nutrition_${nutrition?.id}`]
                  )
                  .map((nutrition) => ({
                    attributes_id: nutrition?.id,
                    unit_of_measure:
                      requestData[`nutrition_measure_${nutrition?.id}`] || 1,
                    unit:
                      parseFloat(requestData[`nutrition_${nutrition?.id}`]) ||
                      0,
                  }));

                // Get conversions data
                const conversions = (requestData?.conversions || [])
                  ?.filter(
                    (conversion) =>
                      conversion?.from_value &&
                      conversion?.from_unit &&
                      conversion?.to_value &&
                      conversion?.to_unit
                  )
                  ?.map((conversion) => {
                    // Find IDs for from_unit and to_unit
                    const fromUnitOption = unitsOfMeasureOptions?.find(
                      (unit) => unit?.value === conversion?.from_unit
                    );
                    const toUnitOption = unitsOfMeasureOptions?.find(
                      (unit) => unit?.value === conversion?.to_unit
                    );

                    return {
                      from_measure: fromUnitOption?.id || 1, // Use ID instead of value
                      from_measure_value:
                        parseFloat(conversion?.from_value) || 0,
                      to_measure: toUnitOption?.id || 1, // Use ID instead of value
                      to_measure_value: parseFloat(conversion?.to_value) || 0,
                    };
                  });

                // Find unit_of_measure ID from unitsOfMeasureOptions
                const selectedUnit = unitsOfMeasureOptions?.find(
                  (unit) => unit?.value === requestData?.units_of_measure
                );
                const unitOfMeasureId = selectedUnit?.id || 1; // Default to 1 if not found

                // ingredient_name,
                //   ingredient_description,
                //   ingredient_status,
                //   waste_percentage,
                //   unit_of_measure,
                //   cost_per_unit,
                //   categories,
                //   nutrition_attributes,
                //   allergy_attributes,
                //   dietary_attributes,
                //   conversions;

                let sendData = {
                  ingredient_name: requestData?.ingredient_name?.trim(),
                  ingredient_description:
                    requestData?.ingredient_description?.trim() || '',
                  ingredient_status: requestData?.ingredient_status || 'active',
                  cost_per_unit: parseFloat(requestData?.ingredient_cost) || 0,
                  waste_percentage:
                    parseFloat(requestData?.ingredient_wastage) || 0,
                  unit_of_measure: unitOfMeasureId,
                  categories: selectedCategories,
                  nutrition_attributes: nutritions,
                  allergy_attributes: selectedAllergens,
                  dietary_attributes: selectedDietary,
                  conversions: conversions,
                };

                let response;
                if (isUpdate && singleData?.id) {
                  response = await updateIngredient(singleData.id, sendData);
                  setApiMessage('success', response?.message);
                } else {
                  response = await createIngredient(sendData);
                  setApiMessage('success', response?.message);
                }

                setLoader(false);
                // Redirect back to ingredients list
                setTimeout(() => {
                  router.push('/recipes/ingredients');
                }, 1500);
              } catch (error) {
                setLoader(false);
                setApiMessage('error', error?.response?.data?.message);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              setFieldValue,
              dirty,
              isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <BasicDetails
                  values={values}
                  errors={errors}
                  touched={touched}
                  handleBlur={handleBlur}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  isDefault={isDefault}
                  unitsOfMeasureOptions={unitsOfMeasureOptions}
                  ingredientCategories={categoryData}
                  currency={currency}
                />
                <Divider className="mt16" />
                <ConversionSection
                  values={values}
                  errors={errors}
                  touched={touched}
                  handleBlur={handleBlur}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  isDefault={isDefault}
                  unitsOfMeasureOptions={unitsOfMeasureOptions}
                />
                <Divider className="mt16" />
                <NutritionInfo
                  values={values}
                  errors={errors}
                  touched={touched}
                  handleBlur={handleBlur}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  isDefault={isDefault}
                  nutritionFields={nutritionData}
                  nutritionOptions={staticOptions?.NUTRITION_OPTIONS}
                />
                <Divider className="mt16" />
                <AllergenInfo
                  values={values}
                  setFieldValue={setFieldValue}
                  isDefault={isDefault}
                  allergens={allergenData}
                />
                <Divider className="mt16" />
                <DietarySuitability
                  values={values}
                  setFieldValue={setFieldValue}
                  isDefault={isDefault}
                  dietarySuitability={dietaryData}
                />

                <Box className="form-actions-btn">
                  <CustomButton
                    fullWidth
                    variant="outlined"
                    title="Cancel"
                    onClick={handleRedirect}
                  />
                  <CustomButton
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!(dirty && isValid) || loader}
                    title={`${loader ? 'Save...' : 'Save'}`}
                  />
                </Box>
              </Form>
            )}
          </Formik>
        ) : (
          <ContentLoader />
        )}
      </Box>
    </>
  );
}
