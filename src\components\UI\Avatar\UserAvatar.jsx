import HeaderImage from '@/components/UI/ImageSecurity';
import React from 'react';

const UserAvatar = ({ name, src, classname }) => {
  const stringToBrightColor = (string) => {
    let hash = 0;
    let i;

    for (i = 0; i < string?.length; i += 1) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    let color = '#';

    for (i = 0; i < 3; i += 1) {
      let value = (hash >> (i * 8)) & 0xff;
      // Make the color bright by ensuring each color component is at least 128 and at most 255
      value = Math.min(value + 128, 255);
      color += `00${value.toString(16)}`.slice(-2);
    }

    return color;
  };

  const invertColor = (hex) => {
    hex = hex.slice(1); // Remove the '#'
    const r = (255 - parseInt(hex.slice(0, 2), 16))
      .toString(16)
      .padStart(2, '0');
    const g = (255 - parseInt(hex.slice(2, 4), 16))
      .toString(16)
      .padStart(2, '0');
    const b = (255 - parseInt(hex.slice(4, 6), 16))
      .toString(16)
      .padStart(2, '0');
    return `#${r}${g}${b}`;
  };

  const stringAvatar = (name) => {
    const brightColor = stringToBrightColor(name);
    const darkColor = invertColor(brightColor);
    return {
      sx: {
        color: '#FFFFFF',
        bgcolor: darkColor,
      },
      children:
        name?.split(' ')?.[0]?.[0] && name?.split(' ')[1]?.[0]
          ? `${name?.split(' ')?.[0]?.[0].toUpperCase()}${name
              ?.split(' ')[1]?.[0]
              .toUpperCase()}`
          : `${name?.split(' ')?.[0]?.[0].toUpperCase()}`,
    };
  };

  return (
    <div className={classname ? classname : ''}>
      <HeaderImage
        imageUrl={src}
        {...stringAvatar(name ? name : 'N V')}
        type="avtar"
      />
      {/* <Avatar src={src} /> */}
    </div>
  );
};

export default UserAvatar;
