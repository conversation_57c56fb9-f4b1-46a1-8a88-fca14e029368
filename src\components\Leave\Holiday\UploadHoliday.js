'use client';

import React, { useEffect, useState } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter } from 'next/navigation';
import PreLoader from '@/components/UI/Loader';
import { useDropzone } from 'react-dropzone';
import PermMediaIcon from '@mui/icons-material/PermMedia';
import { Download } from '@/helper/common/images';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CustomButton from '@/components/UI/CustomButton';

const UploadHoliday = ({ holidayid }) => {
  const router = useRouter();
  const [acceptedMedia, setAcceptedMedia] = useState([]);
  const [loader, setLoader] = useState(false);
  const [holidayList, setHolidayList] = useState([]);

  const {
    getRootProps: getRootPropsMultiple,
    getInputProps: getInputPropsMultiplegetRootProps,
    // acceptedFiles,
  } = useDropzone({
    accept: {
      'application/vnd.ms-excel': [], // .xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [], // .xlsx
    },
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      // var totalFiles = _.concat(acceptedMedia, acceptedFile);
      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload an Excel file only.');
        setAcceptedMedia([]);
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        setAcceptedMedia(acceptedFile);
      }
    },
  });

  const getHolidayList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `${
          URLS?.GET_HOLIDAY_LIST
        }?search=&page=&holiday_policy_year=&holidayTypeStatus=&size=10`
      );

      if (status === 200) {
        setHolidayList(data);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    getHolidayList();
  }, []);

  const ImportHoliday = async () => {
    const body = new FormData();
    body.append('holiday_xlsx', acceptedMedia?.[0]);
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status } = await axiosInstance.post(
        URLS?.IMPORT_HOLIDAY + `${holidayid}`,
        body,
        config
      );
      if (status === 200) {
        router.back();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const uploadedmedia = (type, name) => {
    const filename = name;
    if (type.includes('pdf') || type.includes('doc')) {
      return (
        <Box className="image-sec">
          {/* <PictureAsPdfIcon /> */}
          <Typography className="title-text file-name text-ellipsis">
            {filename}
          </Typography>
        </Box>
      );
    }
  };
  const download = async () => {
    try {
      if (!holidayList?.upload_link) {
        setApiMessage('error', 'No upload link provided.');
        return;
      }
      const response = await fetch(holidayList?.upload_link);

      if (!response.ok) {
        setApiMessage('error', response.statusText);
        return;
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'Holiday.xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  return (
    <Box className="pt24 Upload-holidays">
      <Box className="d-flex align-center w100 justify-space-between gap-10">
        <Typography className="title-text">
          Before importing data, ensure the file follows the required format for
          accuracy and consistency. Download the sample file as a reference and
          match the file name, date, and structure to avoid errors.
        </Typography>
        {loader && <PreLoader />}
        <Tooltip
          arrow
          title={
            <Typography className="sub-title-text">Download Sample</Typography>
          }
          classes={{
            tooltip: 'info-tooltip-container',
          }}
        >
          <Box className="primary-small-icon d-flex align-center justify-center cursor-pointer">
            <Download
              className="svg-icon"
              onClick={() => {
                download();
              }}
            />
          </Box>
        </Tooltip>
      </Box>
      <Box className="pt16 w100">
        <Box className="upload-sec cursor-pointer w100 text-align">
          <Box
            {...getRootPropsMultiple({ className: 'dropzone' })}
            className="upload-area"
          >
            <PermMediaIcon />
            <input {...getInputPropsMultiplegetRootProps()} />
            <Typography className="title-text upload-text">
              Drop your Holiday here
            </Typography>
          </Box>
        </Box>
        {acceptedMedia && acceptedMedia?.length > 0 ? (
          <Box className="">
            {acceptedMedia?.map((item, i) => (
              <Box className="uploaded-media-sec">
                {uploadedmedia(item?.type, item?.name)}
                <DeleteOutlineIcon
                  className="cursor-pointer"
                  onClick={() => {
                    const files = acceptedMedia?.filter(
                      (id, index) => i !== index
                    );
                    setAcceptedMedia(files);
                  }}
                />
              </Box>
            ))}
          </Box>
        ) : (
          <></>
        )}
      </Box>
      <Box className="form-actions-btn">
        <CustomButton
          variant="contained"
          title={`${loader ? 'Saving...' : 'Import'}`}
          disabled={loader}
          fullWidth={false}
          onClick={() => {
            ImportHoliday();
          }}
        />
      </Box>
    </Box>
  );
};

export default UploadHoliday;
