import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';

export const branchService = {
  getBranchList: async (
    search = '',
    pageNo = 1,
    size = '',
    branchStatus = 'active'
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_LIST +
          `?search=${search}&page=${pageNo}&size=${size}&branchStatus=${branchStatus}`
      );
      if (status === 200) {
        return data?.data?.map((branch) => ({
          id: branch?.id,
          name: branch?.branch_name,
          color: branch?.branch_color,
          has_user_meta: true,
        }));
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  // NOTE: For health and safety module
  getCategoryBranchList: async (
    categoryId = '',
    search = '',
    pageNo = '',
    size = ''
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CATEGORY_BRANCH_LIST +
          `${categoryId}?search=${search}&page=${pageNo}&size=${size}`
      );
      if (status === 200) {
        return data?.data?.map((branch) => ({
          id: branch?.id,
          name: branch?.branch_name,
          color: branch?.branch_color,
          is_health_and_safety: branch?.is_health_and_safety,
        }));
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  getBranchDetails: async (branchId) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_BRANCH + `${branchId}`
      );
      if (status === 200 || status === 201) {
        return data?.data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getBranchHistory: async (branchId) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_HISTORY + `${branchId}`
      );
      if (status === 200 || status === 201) {
        return data?.data;
      }
      return [];
    } catch (error) {
      throw error;
    }
  },

  updateBranch: async (branchId, branchData) => {
    try {
      const ApiUrl = branchId
        ? URLS.UPDATE_BRANCH + branchId
        : URLS.UPDATE_BRANCH;
      const { status, data } = await axiosInstance.put(ApiUrl, branchData);
      if (status === 200 || status === 201) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  updateCategoryBranchAssignments: async (categoryId, data) => {
    try {
      const { status, data: responseData } = await axiosInstance.post(
        URLS?.ADD_MULTIPLE_CATEGORY_BRANCH,
        data
      );
      if (status === 200 || status === 202) {
        return responseData;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
