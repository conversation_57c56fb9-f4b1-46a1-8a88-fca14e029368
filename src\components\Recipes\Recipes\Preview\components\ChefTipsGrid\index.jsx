import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './ChefTipsGrid.scss';

const ChefTipsGrid = ({ recipeData }) => {
  return (
    <div className="chef-tips-grid">
      {/* Head Chef Tips */}
      <div className="chef-tips-grid__card chef-tips-grid__card-wrap">
        <div className="chef-tips-grid__header">
          <p className="chef-tips-grid__title chef-tips-grid__head-chef-title-text">
            <Icon name="ChefHat" size={18} color="currentColor" />
            <span>Head Chef Tips</span>
          </p>
        </div>
        <div className="chef-tips-grid__content">
          <p className="chef-tips-grid__text">
            {recipeData?.recipe_head_chef_tips}
          </p>
        </div>
      </div>

      {/* FOH Tips */}
      <div className="chef-tips-grid__card chef-tips-grid__foh-wrap">
        <div className="chef-tips-grid__header">
          <p className="chef-tips-grid__title chef-tips-grid__foh-title-wrap">
            <Icon name="Users" size={18} color="currentColor" />
            <span>Front of House</span>
          </p>
        </div>
        <div className="chef-tips-grid__content">
          <p className="chef-tips-grid__text">
            {recipeData?.recipe_head_chef_tips}
          </p>
        </div>
      </div>
    </div>
  );
};

export default ChefTipsGrid;
