import React from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { useDropzone } from 'react-dropzone';
import PermMediaIcon from '@mui/icons-material/PermMedia';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import CustomButton from '@/components/UI/CustomButton';
import { Download } from '@/helper/common/images';
import { setApiMessage } from '@/helper/common/commonFunctions';

const ExcelFileImporter = ({
  acceptedMedia = [],
  setAcceptedMedia,
  onCancel,
  onImport,
  title = 'Upload Excel File',
  description = 'Before importing data, ensure the file follows the required format for accuracy and consistency. Download the sample file as a reference and match the file name, date, and structure to avoid errors.',
  onDownloadSample,
  showDownloadSample = true,
  dropzoneConfig = {},
  dropText = 'Drop your Excel file here',
  disabled = false,
}) => {
  // Default dropzone configuration
  const defaultDropzoneConfig = {
    accept: {
      'application/vnd.ms-excel': [], // .xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [], // .xlsx
    },
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);

      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload an Excel file only.');
        setAcceptedMedia([]);
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        setAcceptedMedia(acceptedFile);
      }
    },
    ...dropzoneConfig, // Allow custom configuration to override defaults
  };

  const { getRootProps, getInputProps } = useDropzone(defaultDropzoneConfig);

  // Render uploaded file
  const uploadedMedia = (name) => {
    const filename = name;
    return (
      <Box className="image-sec">
        <Typography className="title-text file-name text-ellipsis">
          {filename}
        </Typography>
      </Box>
    );
  };

  // Handle file removal
  const handleRemoveFile = (indexToRemove) => {
    const files = acceptedMedia?.filter((_, index) => index !== indexToRemove);
    setAcceptedMedia(files);
  };

  return (
    <Box className="import-modal-content">
      <Box className="upload-section">
        <Typography className="other-field-label mb16">{title}</Typography>
        <Box className="d-flex align-center w100 justify-space-between gap-10">
          <Typography className="title-text">{description}</Typography>
          {showDownloadSample && (
            <Tooltip
              arrow
              title={
                <Typography className="sub-title-text">
                  Download Sample
                </Typography>
              }
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box
                className="primary-small-icon d-flex align-center justify-center cursor-pointer"
                onClick={onDownloadSample}
              >
                <Download className="svg-icon" />
              </Box>
            </Tooltip>
          )}
        </Box>
        <Box className="upload-sec cursor-pointer w100 text-align mt16">
          <Box
            {...getRootProps({ className: 'dropzone' })}
            className="upload-area"
          >
            <PermMediaIcon />
            <input {...getInputProps()} />
            <Typography className="title-text upload-text">
              {dropText}
            </Typography>
          </Box>
        </Box>
        {acceptedMedia && acceptedMedia?.length > 0 ? (
          <Box className="uploaded-files-section mt16">
            {acceptedMedia?.map((item, i) => (
              <Box
                key={i}
                className="uploaded-media-sec d-flex align-center justify-between"
              >
                {uploadedMedia(item?.name)}
                <DeleteOutlineIcon
                  className="cursor-pointer"
                  onClick={() => handleRemoveFile(i)}
                />
              </Box>
            ))}
          </Box>
        ) : null}
      </Box>
      <Box className="modal-actions d-flex justify-end gap-sm mt24">
        <CustomButton variant="outlined" title="Cancel" onClick={onCancel} />
        <CustomButton
          title="Import"
          onClick={onImport}
          disabled={disabled || !acceptedMedia || acceptedMedia.length === 0}
        />
      </Box>
    </Box>
  );
};

export default ExcelFileImporter;
