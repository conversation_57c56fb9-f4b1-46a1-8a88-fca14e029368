'use client';

import React, { useContext, useState, useEffect } from 'react';
import { Box, Typography, InputAdornment, IconButton } from '@mui/material';
import CustomButton from '@/components/UI/button';
import { useRouter } from 'next/navigation';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { Formik, Form } from 'formik';
import { CustomTextField } from '@/components/UI/CommonField/index';
import {
  saveToStorage,
  removeFromStorage,
  fetchFromStorage,
} from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import axiosInstance from '@/helper/axios/axiosInstance';
import AuthContext from '@/helper/authcontext';
import { URLS } from '@/helper/constants/urls';
import PreLoader from '@/components/UI/Loader';
import { setApiMessage } from '@/helper/common/commonFunctions';
import * as Yup from 'yup';
import './login.scss';
import AppBrandLogo from '@/components/UI/AppBrandLogo';

export default function Login() {
  const router = useRouter();
  const { setAuthState } = useContext(AuthContext);
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [loader, setLoader] = useState(false);
  const [IPAddress, setIPAddress] = useState('');

  // GET IP ADDRESS
  const getIPAddress = async () => {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      if (response.ok) {
        const data = await response.json();
        const deviceData = { ip: data?.ip };

        const locationResp = await axiosInstance.get(
          `https://ipinfo.io/${data?.ip}/geo?token=f958fa1525009d`
        );
        if (locationResp?.data) {
          const locationData = locationResp?.data;
          deviceData.address =
            `${locationData?.city},${locationData?.region},${locationData?.country},${locationData?.postal}` ||
            '';
          deviceData.location = locationData?.loc || '';
        }
        setIPAddress(data?.ip);
        saveToStorage(identifiers?.DEVICEDATA, deviceData);
      } else {
        // console.error('Failed to fetch IP address:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching IP address:', error);
    }
  };
  useEffect(() => {
    fetchFromStorage(identifiers?.DEVICEDATA) === undefined && getIPAddress();
  }, []);
  return (
    <>
      {loader && <PreLoader />}
      <Box className="login-page d-flex justify-center align-center">
        <Box className="login-block d-flex justify-center">
          <AppBrandLogo
            className="text-align"
            onClick={() => router.push('/login')}
          />
          <Typography
            variant="h2"
            className="h2 fw700 main-heading pt16 pb56 text-align"
          >
            Login
          </Typography>
          <Formik
            initialValues={{
              email: '',
              password: '',
            }}
            enableReinitialize={true}
            validationSchema={Yup.object().shape({
              email: Yup.string()
                .required('This field is required')
                .matches(
                  /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
                  'Please enter valid email'
                ),
              password: Yup.string()
                .trim()
                .required('This field is required')
                .min(8, 'Password must be at least 8 characters')
                .matches(
                  /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
                  'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'
                ),
              // .min(8, 'Password length must be minimum 8 character')
            })}
            onSubmit={async (requestData) => {
              let isDeviceId = fetchFromStorage(identifiers?.DEVICEID);
              let sendData = {
                user_email: requestData?.email,
                user_password: requestData?.password,
                client_ip: IPAddress,
                webAppToken: isDeviceId,
              };
              try {
                setLoader(true);
                const { status, data } = await axiosInstance.post(
                  URLS.USER_LOGIN,
                  sendData
                );

                if (status === 200) {
                  if (data?.status) {
                    setApiMessage('success', data?.message);
                    saveToStorage(identifiers?.AUTH_DATA, data?.user_data);
                    saveToStorage(identifiers?.NORMAL_LOGIN, true);
                    setTimeout(() => {
                      if (data?.user_data?.user_status === 'pending') {
                        setAuthState({ Email: requestData?.email });
                        saveToStorage(identifiers?.EMAIL, requestData?.email);
                        router.push('/resetpassword');
                      } else {
                        router.push('/chart-dashboard');
                        saveToStorage('IsjustLogin', true);
                        removeFromStorage(identifiers?.EMAIL);
                      }
                    }, 1000);
                  } else {
                    setApiMessage('error', data?.message);
                  }
                  setLoader(false);
                }
              } catch (error) {
                setApiMessage('error', error?.response?.data?.message);
                setLoader(false);
              }
            }}
          >
            {({
              errors,
              touched,
              handleBlur,
              values,
              handleSubmit,
              handleChange,
              // dirty,
              // isValid,
            }) => (
              <Form onSubmit={handleSubmit}>
                <Box>
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    id="email"
                    name="email"
                    value={values.email}
                    className="w100"
                    label="Email address"
                    variant="filled"
                    placeholder="Enter email address"
                    error={Boolean(touched.email && errors.email)}
                    helperText={touched.email && errors.email}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
                <Box className="pt40">
                  <CustomTextField
                    InputLabelProps={{
                      shrink: true,
                    }}
                    id="password"
                    name="password"
                    value={values.password}
                    label="Password"
                    placeholder="Enter password"
                    type={showEnterPassword ? 'text' : 'password'}
                    variant="filled"
                    className={
                      touched.password && errors.password
                        ? 'w100 password-textfield password-error'
                        : 'w100 password-textfield'
                    }
                    error={Boolean(touched.password && errors.password)}
                    helperText={touched.password && errors.password}
                    onBlur={handleBlur}
                    onChange={handleChange}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end" className="eye-icon">
                          <IconButton
                            disableRipple
                            onClick={() =>
                              setShowCurrentPassword(!showEnterPassword)
                            }
                          >
                            <Box className="eye-wrap">
                              {showEnterPassword ? (
                                <VisibilityIcon />
                              ) : (
                                <VisibilityOffIcon />
                              )}
                            </Box>
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                  <Typography
                    variant="h6"
                    className="p14 pt16  forgot-wrap cursor-pointer "
                    textAlign="end"
                  >
                    <Typography
                      variant="span"
                      className="forgot-text"
                      onClick={() => {
                        saveToStorage(identifiers?.EMAIL, values?.email);
                        setAuthState({ Email: values?.email });
                        router.push('/forgot-password');
                      }}
                    >
                      {' '}
                      Forgot password?
                    </Typography>
                  </Typography>

                  <Box className="pt24" textAlign="center">
                    <CustomButton
                      fullWidth
                      className="p16"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#39596e"
                      colorhover="#FFFFFF"
                      title="Login"
                      isLogin={true}
                    />
                  </Box>
                </Box>
              </Form>
            )}
          </Formik>
        </Box>
      </Box>
    </>
  );
}
