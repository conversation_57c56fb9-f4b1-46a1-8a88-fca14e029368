@import '@/styles/variable.scss';

.attachment-wrap {
  max-height: calc(100vh - 200px - var(--banner-height));
  overflow: scroll;

  .attachment-history-wrap {
    position: relative;

    .devider-wrap {
      padding: 0px 20px 0px 50px;
    }

    .attachment-item {
      .header-date-wrap {
        padding: 15px 0px;
        position: sticky;
        top: 0px;
        background: white;

        .header-date {
          font-weight: 700;
        }

        .calender-icon {
          background-color: white;
          position: absolute;
          left: -28px;
          fill: $color-Dark-30;
          height: 15px;
          width: 15px;
        }
      }

      .name-text-wrap {
        .file-status-wrap {
          font-weight: 700;
        }

        .circle-wrap {
          position: absolute;
          left: 48px;
          fill: $color-Dark-30;
          height: 5px;
          width: 5px;
        }
      }

      .file-name {
        font-size: 15px;

        .file-name-text {
          color: $color-Dark-30;
          display: inline-block;
          padding-right: 10px;
        }
      }

      .time-wrap {
        font-size: 15px;
        color: $color-Dark-30;
      }

      .attachment-type-wrap {
        font-size: 15px;

        .attachment-type-text {
          display: inline-block;
          padding-right: 10px;
          color: $color-Dark-30;
        }
      }
    }
  }
}
