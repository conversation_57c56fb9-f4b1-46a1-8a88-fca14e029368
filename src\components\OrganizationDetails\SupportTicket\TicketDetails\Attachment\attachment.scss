@import '@/styles/variable.scss';

.attachment-wrap {

    .empty-attachment {
        padding: 20px 0px;

        .no-attachment {
            font-family: "Poppins", sans-serif !important;
        }

        .upload-attachment {
            font-family: "Poppins", sans-serif !important;
        }

        .browse-files-wrap {
            padding-top: 20px;

            .browse-files {
                padding: 4px 16px !important;
                font-size: 15px !important;
                font-weight: 500;

                &:hover {
                    color: white !important;
                }
            }
        }

    }

    .media-previews {
        .add-file-wrap {
            border-bottom: 1px solid $color-Dark-10;
            padding: 10px 0px;

            .add-file {
                font-size: 15px !important;
                font-weight: 400;
                background-color: transparent;
                color: $color-Dark-50 !important;
                padding: 0px !important;
                border: none;

                &:hover {
                    color: $color-Dark-100 !important;
                    box-shadow: none !important;
                }

                @media(max-width: 575px) {
                    font-size: 14px !important;
                }
            }
        }

        .preview-container {
            padding-top: 15px;

            .file-name-wrap {

                @media(max-width:1024px) {
                    gap: 10px !important;
                }

                @media(max-width:575px) {
                    gap: 3px !important;
                }

            }

            .file-name {
                @media(max-width:1305px) {
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                    cursor: pointer;
                    max-width: 40ch;
                }

                @media(max-width:575px) {
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    overflow: hidden;
                    cursor: pointer;
                    max-width: 15ch;
                    font-size: 14px;
                }
            }

            .media-icon-wrap {
                border: 1px solid $color-primary;
                width: 40px;
                height: 40px;
                border-radius: 40px;
                padding: 8px;

                .icon-wrap {
                    fill: $color-primary;
                    height: 21px;
                    width: 21px;

                    @media(max-width:575px) {
                        height: 15px;
                        width: 15px;
                        gap: 3px !important;
                    }
                }

                @media(max-width:575px) {
                    padding: 1px;
                    width: 25px;
                    height: 25px;
                }
            }

            @media(max-width:1250px) {
                padding: 15px 0px 0px 0px !important;
                gap: 2px
            }
        }

        .more-item-icon {
            line-height: 0px;
            display: none;

            .more-item {
                fill: $color-primary;
            }

            @media(max-width:991px) {
                display: block;
            }
        }

        .icons-wrap {
            line-height: 0px;

            .eye-icon,
            .download-icon,
            .delete-icon {
                fill: $color-primary;
                height: 21px;
                width: 21px;

                @media(max-width:575px) {
                    height: 17px;
                    width: 17px;
                }
            }

            @media(max-width:991px) {
                display: none !important;
            }
        }
    }
}