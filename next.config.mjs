/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'namastevillage.theeasyaccess.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.youtube.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.shutterstock.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.giftpro.co.uk',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'staging.namastevillage.theeasyaccess.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.tasteofhome.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'www.simplyrecipes.com',
        port: '',
        pathname: '/**',
      },
    ],
    domains: [
      'staging.namastevillage.theeasyaccess.com',
      'www.shutterstock.com',
      'www.tasteofhome.com',
      'www.simplyrecipes.com',
      'images.giftpro.co.uk',
    ],
  },
  experimental: {
    forceSwcTransforms: true,
  },
};
export default nextConfig;
