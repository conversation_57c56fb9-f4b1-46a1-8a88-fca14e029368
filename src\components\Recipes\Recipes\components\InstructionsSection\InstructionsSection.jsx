import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import CustomImageUploader from '@/components/ImageUpload/CustomImageUploader';
import CustomEditor from '@/components/UI/CustomEditor';
import './InstructionsSection.scss';

const InstructionsSection = ({ formData, dispatch, validationErrors = {} }) => {
  const [draggedStep, setDraggedStep] = useState(null);
  const [dragOverStep, setDragOverStep] = useState(null);

  // Local state for instructions to ensure immediate UI updates
  const [localInstructions, setLocalInstructions] = useState(() => {
    // Handle both formData.instructions and formData directly being an array
    return Array.isArray(formData) ? formData : formData?.instructions || [];
  });

  // Sync local state with prop changes
  useEffect(() => {
    // Handle both formData.instructions and formData directly being an array
    const instructionsData = Array.isArray(formData)
      ? formData
      : formData?.instructions || [];
    setLocalInstructions(instructionsData);
  }, [formData, formData?.instructions]);

  // Use local instructions for immediate UI updates
  const instructions = localInstructions;

  const addStep = () => {
    const newStep = {
      id: Date.now(),
      stepNumber: (instructions?.length || 0) + 1,
      description: '',
      image: null,
      isOptional: false,
    };

    const updatedInstructions = [...(instructions || []), newStep];

    // Update local state immediately for UI responsiveness
    setLocalInstructions(updatedInstructions);

    if (dispatch) {
      dispatch({ type: 'UPDATE_INSTRUCTIONS', payload: updatedInstructions });
    }
  };

  const updateStep = async (index, field, value) => {
    if (!instructions?.[index]) return;

    const updatedInstructions = [...(instructions || [])];
    updatedInstructions[index] = {
      ...updatedInstructions[index],
      [field]: value,
    };

    // Update local state immediately for UI responsiveness
    setLocalInstructions(updatedInstructions);

    if (dispatch) {
      dispatch({ type: 'UPDATE_INSTRUCTIONS', payload: updatedInstructions });
    }
  };

  const removeStep = (index) => {
    const updatedInstructions =
      instructions?.filter?.((_, i) => i !== index) || [];
    // Renumber steps
    const renumberedInstructions =
      updatedInstructions?.map?.((step, i) => ({
        ...step,
        stepNumber: i + 1,
      })) || [];

    // Update local state immediately for UI responsiveness
    setLocalInstructions(renumberedInstructions);

    if (dispatch) {
      dispatch({
        type: 'UPDATE_INSTRUCTIONS',
        payload: renumberedInstructions,
      });
    }
  };

  const moveStep = (fromIndex, toIndex) => {
    if (!instructions?.[fromIndex] || fromIndex === toIndex) return;

    const updatedInstructions = [...(instructions || [])];
    const [movedStep] = updatedInstructions.splice(fromIndex, 1);
    updatedInstructions.splice(toIndex, 0, movedStep);

    // Renumber steps
    const renumberedInstructions =
      updatedInstructions?.map?.((step, i) => ({
        ...step,
        stepNumber: i + 1,
      })) || [];

    // Update local state immediately for UI responsiveness
    setLocalInstructions(renumberedInstructions);

    if (dispatch) {
      dispatch({
        type: 'UPDATE_INSTRUCTIONS',
        payload: renumberedInstructions,
      });
    }
  };

  const handleDragStart = (e, index) => {
    setDraggedStep(index);
    if (e?.dataTransfer) {
      e.dataTransfer.effectAllowed = 'move';
    }
  };

  const handleDragOver = (e, index) => {
    e?.preventDefault?.();
    setDragOverStep(index);
  };

  const handleDragLeave = () => {
    setDragOverStep(null);
  };

  const handleDrop = (e, index) => {
    e?.preventDefault?.();
    if (draggedStep !== null && draggedStep !== index) {
      moveStep(draggedStep, index);
    }
    setDraggedStep(null);
    setDragOverStep(null);
  };

  const handleImageUpload = (index, file) => {
    if (file && instructions?.[index]) {
      const mockUrl = URL.createObjectURL(file);
      const imageData = {
        id: Date.now(),
        name: file?.name || 'image',
        url: mockUrl,
        size: file?.size || 0,
        file: file, // Store the actual file for potential upload
      };
      updateStep(index, 'image', imageData);
    }
  };

  const removeStepImage = (index) => {
    if (instructions?.[index]) {
      updateStep(index, 'image', null);
    }
  };

  // Get image preview URL for CustomImageUploader
  const getImagePreview = (step) => {
    return step?.image?.url || null;
  };

  // Get acceptFiles format for CustomImageUploader
  const getAcceptFiles = (step) => {
    if (step?.image?.url) {
      return {
        link: step.image.url,
        isAdded: true,
      };
    }
    return null;
  };

  return (
    <div className="instructions-section">
      {/* Section Header */}
      <div className="instructions-section__header">
        <div className="instructions-section__header-content">
          <h3 className="instructions-section__title">Cooking Instructions</h3>
          <p className="instructions-section__description">
            Add step-by-step instructions for your recipe
          </p>
        </div>
      </div>

      {/* Instructions List */}
      <div className="instructions-section__list">
        {(instructions?.length || 0) === 0 ? (
          <div className="instructions-section__empty-state">
            <Icon
              name="List"
              size={48}
              className="instructions-section__empty-icon"
            />
            <h4 className="instructions-section__empty-title">
              No instructions added yet
            </h4>
            <p className="instructions-section__empty-description">
              Start by adding your first cooking step
            </p>
            <CustomButton
              onClick={addStep}
              startIcon={<Icon name="Plus" size={16} />}
            >
              Add First Step
            </CustomButton>
          </div>
        ) : (
          instructions?.map?.((step, index) => (
            <div
              key={step?.id || index}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, index)}
              className={`instructions-section__step ${
                dragOverStep === index
                  ? 'instructions-section__step--drag-over'
                  : ''
              } ${
                draggedStep === index
                  ? 'instructions-section__step--dragging'
                  : ''
              }`}
            >
              {/* Step Content - Horizontal Layout */}
              <div className="instructions-section__step-content">
                {/* Drag Handle */}
                <div className="d-flex align-center justify-center gap-sm">
                  <div className="instructions-section__step-controls">
                    <Icon
                      name="GripVertical"
                      size={16}
                      className="instructions-section__drag-handle"
                    />
                  </div>

                  {/* Step Number */}
                  <div className="instructions-section__step-number">
                    {step?.stepNumber || index + 1}
                  </div>
                </div>
                {/* Step Image */}
                <div className="instructions-section__step-image">
                  <CustomImageUploader
                    imagePreview={getImagePreview(step)}
                    acceptFiles={getAcceptFiles(step)}
                    placeholder=""
                    onDropAccepted={(file) => handleImageUpload(index, file)}
                    onRemoveImage={() => removeStepImage(index)}
                    uploadClassName="step-image-upload"
                    previewClassName="step-image-preview"
                  />
                </div>

                {/* Step Description */}
                <div className="instructions-section__step-description">
                  <CustomEditor
                    content={step?.description || ''}
                    setContent={(content) =>
                      updateStep(index, 'description', content)
                    }
                    height="120px"
                    toolbarType="recipe-instructions"
                  />
                </div>

                {/* Delete Button */}
                <div className="instructions-section__step-actions">
                  <button
                    onClick={() => removeStep(index)}
                    className="instructions-section__remove-button"
                    aria-label={`Remove step ${step?.stepNumber || index + 1}`}
                  >
                    <Icon name="Trash2" size={16} color="currentColor" />
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Add Step Button */}
      {(instructions?.length || 0) > 0 && (
        <div className="instructions-section__add-button">
          <CustomButton
            onClick={addStep}
            startIcon={<Icon name="Plus" size={16} />}
          >
            Add Step
          </CustomButton>
        </div>
      )}

      {/* Instructions Summary */}
      {(instructions?.length || 0) > 0 && (
        <div className="instructions-section__summary">
          <h4 className="instructions-section__summary-title">
            Instructions Summary
          </h4>
          <div className="instructions-section__summary-grid">
            <div className="instructions-section__summary-item">
              <div className="instructions-section__summary-count">
                {instructions?.length || 0}
              </div>
              <div className="instructions-section__summary-label">
                Total Steps
              </div>
            </div>
            <div className="instructions-section__summary-item">
              <div className="instructions-section__summary-count">
                {instructions?.filter?.((step) => step?.image)?.length || 0}
              </div>
              <div className="instructions-section__summary-label">
                Steps with Images
              </div>
            </div>
            {/* <div className="instructions-section__summary-item">
              <div className="instructions-section__summary-count">
                {instructions?.filter?.((step) => step?.isOptional)?.length ||
                  0}
              </div>
              <div className="instructions-section__summary-label">
                Optional Steps
              </div>
            </div> */}
          </div>
        </div>
      )}

      {validationErrors?.instructions && (
        <div className="instructions-section__error">
          <Icon name="AlertCircle" size={16} />
          <span className="other-field-error-text">
            {validationErrors.instructions}
          </span>
        </div>
      )}
    </div>
  );
};

export default InstructionsSection;
