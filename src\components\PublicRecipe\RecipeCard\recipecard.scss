body {
  .recipe-card-section {
    margin-bottom: 30px;
    .recipe-card-wrap {
      max-width: 1260px;
      margin: 0 auto;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-2xl);
      padding: var(--spacing-6xl) var(--spacing-6xl) var(--spacing-none);
      .recipe-card {
        background-color: var(--color-white);
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        .recipe-card-image-wrap {
          height: 100%;
          max-height: 221px;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .recipe-card-content {
          padding: var(--spacing-xxl);
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          .recipe-card-title {
            margin-bottom: var(--spacing-lg);
          }
          .recipe-card-price-wrap {
            .recipe-card-from {
              color: var(--text-color-slate-gray);
              .recipe-card-price {
                display: flex;
                // flex-direction: column-reverse;
                flex-grow: 1;
              }
            }
          }
          .recipe-card-no-from {
            display: flex;
            flex-direction: column-reverse;
            flex-grow: 1;
          }
        }
      }
      @media (max-width: 991px) {
        grid-template-columns: repeat(2, 1fr);
      }
      @media (max-width: 767px) {
        max-width: 460px;
        grid-template-columns: repeat(1, 1fr);
        padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-none);
      }
    }
  }
}
