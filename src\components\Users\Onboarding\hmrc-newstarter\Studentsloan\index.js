'use client';

import React from 'react';
import {
  Box,
  Typography,
  FormGroup,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

export default function StudentsLoan({
  touched,
  errors,
  values,
  // handleChange,
  setFieldValue,
  // handleBlur,
  plan,
  planchecked,
  setPlanChecked,
  ViewAccessOnly,
}) {
  const planList = [
    {
      id: 1,
      name: 'Plan 1',
    },
    {
      id: 2,
      name: 'Plan 2',
    },
    {
      id: 3,
      name: 'Plan 4',
    },
    {
      id: 4,
      name: 'Postgraduate Loan (England & Wales Only)',
    },
  ];

  const handleplan = (value) => {
    const currentIndex = planchecked.indexOf(value);
    const newChecked = [...planchecked];
    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }
    setPlanChecked(newChecked);
    plan(newChecked);
  };

  const clearPlan = () => {
    setPlanChecked([]);
    plan([]);
  };

  return (
    <Box>
      <Box className="pt16">
        <Typography className="field-label">
          Do you have a student or postgraduate loan?{' '}
          <span className="required">*</span>
        </Typography>
        <FormGroup className={'checkbox-details checkbox-form pt8'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-text title-text"
            name="sl_pg"
            checked={values?.sl_pg === 'yes'}
            onChange={(e) => {
              setFieldValue('sl_pg', e.target.checked ? 'yes' : '');
            }}
            disabled={ViewAccessOnly || true}
            label={'Yes'}
          />
          <Typography className="title-text">Go to next question</Typography>
          {/* 12 */}
        </FormGroup>
        <FormGroup className={'checkbox-details checkbox-form pt8'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-text title-text"
            name="sl_pg"
            checked={values?.sl_pg === 'no'}
            onChange={(e) => {
              setFieldValue('sl_pg', e.target.checked ? 'no' : '');
            }}
            disabled={ViewAccessOnly || true}
            label={'No'}
          />
          <Typography className="title-text">
            Go straight to the Declaration
          </Typography>
        </FormGroup>
        {touched.sl_pg && errors.sl_pg && (
          <Typography variant="body2" className="other-field-error-text">
            {errors.sl_pg}
          </Typography>
        )}
      </Box>
      {values?.sl_pg === 'yes' && (
        <>
          <Box className="pt32">
            <Typography className="title-text fw600" id="sl_stmt">
              Do any of the following statements apply:
            </Typography>
            <Box className="pt8">
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  You’re still studying on a course that your student loan
                  relates to
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  You completed or left any course after the start of the
                  current tax year, which started on 6 April
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  You/ve already repaid your loan in full
                </Typography>
              </Box>
              <Box className="payment-section">
                <Box className="dots"></Box>
                <Typography className="title-text">
                  You’re paying the student Loans Company by Direct Debit from
                  your bank to manage your end of loan repayments.
                </Typography>
              </Box>
            </Box>
            <FormGroup
              className={
                ViewAccessOnly
                  ? 'checkbox-details checkbox-form pt16'
                  : 'checkbox-details pt16'
              }
            >
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                  />
                }
                className="check-box-text title-text"
                name="sl_stmt"
                checked={values?.sl_stmt === 'yes'}
                onChange={(e) => {
                  setFieldValue('sl_stmt', e.target.checked ? 'yes' : '');
                  e.target.checked && clearPlan();
                }}
                label={'Yes'}
                disabled={ViewAccessOnly}
              />
              <Typography className="title-text">
                Go straight to the declaration
              </Typography>
            </FormGroup>
            <FormGroup
              className={
                ViewAccessOnly
                  ? 'checkbox-details checkbox-form pt16'
                  : 'checkbox-details pt16'
              }
            >
              <FormControlLabel
                control={
                  <Checkbox
                    className="check-box "
                    icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                    checkedIcon={<CheckBoxIcon className="check-icon" />}
                  />
                }
                className="check-box-text title-text"
                name="sl_stmt"
                checked={values?.sl_stmt === 'no'}
                onChange={(e) => {
                  setFieldValue('sl_stmt', e.target.checked ? 'no' : '');
                }}
                label={'No'}
                disabled={ViewAccessOnly}
              />
              <Typography className="title-text">
                Go to next question
              </Typography>
            </FormGroup>
            {touched.sl_stmt && errors.sl_stmt && (
              <Typography variant="body2" className="other-field-error-text">
                {errors.sl_stmt}
              </Typography>
            )}
          </Box>
          {values?.sl_stmt === 'no' && (
            <Box className="pt32">
              <Typography className="title-text fw600">
                To avoid repaying more than you need to, tick the correct
                student loan or loans that you have Use the Guidance on the
                right to help you.
              </Typography>
              <Typography className="title-text pt8" id="sl_plan">
                Please tick all that apply
              </Typography>
              {planList &&
                planList.length > 0 &&
                planList?.map((item) => {
                  return (
                    <FormGroup
                      className={
                        ViewAccessOnly
                          ? 'plan-checkbox-details checkbox-form pt16'
                          : 'plan-checkbox-details pt16'
                      }
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            className="check-box "
                            icon={
                              <CheckBoxOutlineBlankIcon className="uncheck-icon" />
                            }
                            checkedIcon={
                              <CheckBoxIcon className="check-icon" />
                            }
                          />
                        }
                        className="check-box-text  title-text"
                        name="sl_plan"
                        // checked={values?.sl_plan === item?.name}
                        // onChange={(e) => {
                        //   setFieldValue('sl_plan', item?.name);
                        // }}
                        disabled={ViewAccessOnly}
                        checked={planchecked?.includes(item?.id)}
                        onChange={() => {
                          // setFieldValue('sl_plan', item?.name);
                          handleplan(item?.id);
                        }}
                        label={item?.name}
                      />
                    </FormGroup>
                  );
                })}
              {touched.sl_plan && errors.sl_plan && (
                <Typography variant="body2" className="other-field-error-text">
                  {errors.sl_plan}
                </Typography>
              )}
            </Box>
          )}
        </>
      )}

      <Box>
        <Typography className="body-text pt16">Declaration</Typography>
        <FormGroup className={'form-checkbox pt8'}>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form form-row title-text max-content"
            name="confirmation"
            checked={values?.confirmde}
            onChange={(e) => {
              setFieldValue('confirmde', e.target.checked);
            }}
            disabled={ViewAccessOnly}
            label="I confirm that the information I’ve given on this form is correct. *"
          />
        </FormGroup>
        {touched.confirmde && errors.confirmde && (
          <Typography variant="body2" className="other-field-error-text">
            {errors.confirmde}
          </Typography>
        )}
      </Box>
    </Box>
  );
}
