'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import FormQuestion from '@/components/UI/FormQuestion';
import LaunchIcon from '@mui/icons-material/Launch';
import CancelIcon from '@mui/icons-material/Cancel';
import Personalinfo from './personalinfo';
import NextOfkin from './nextofkin';
import PersonalHealthDetails from './personalHealth';
import ProffesionalDetails from './proffesionaldetails';
import BankDetails from './bankdetails';
import EmployeeStatement from './EmployeeStmt';
import StudentsLoan from './Studentsloan';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import FileUpload from '@/components/Users/<USER>/right-to-work/uploadFile';
import HeaderImage from '@/components/UI/ImageSecurity';
import '../onboarding.scss';
import './hmrc.scss';

export default function HmrcNewstarter({
  isUserScreen,
  UserDetails,
  UserId,
  isMyProfile,
  formNewStarterHMRC,
  getOnboardingChecklist,
  ViewAccessOnly,
  countryList,
}) {
  const formikRef = useRef(null);
  const [newStarterDetails, setNewStartDetails] = useState('');
  const [newStarterHMRCDetails, setNewStartHMRCDetails] = useState('');
  const [planchecked, setPlanChecked] = useState([]);

  const [postLoan, setPostLoan] = useState();
  const [p45details, setP45Details] = useState();
  const [p45, setP45] = useState();

  const [loader, setLoader] = useState(false);

  const setFormUserdetails = () => {
    setTimeout(() => {
      formikRef.current.setFieldValue(
        'pi_fname',
        UserDetails?.user_first_name ? UserDetails?.user_first_name : ''
      );
      formikRef.current.setFieldValue(
        'pi_mname',
        UserDetails?.user_middle_name ? UserDetails?.user_middle_name : ''
      );
      formikRef.current.setFieldValue(
        'pi_lname',
        UserDetails?.user_last_name ? UserDetails?.user_last_name : ''
      );
      formikRef.current.setFieldValue(
        'pi_email',
        UserDetails?.user_email ? UserDetails?.user_email : ''
      );

      formikRef.current.setFieldValue(
        'pi_phoneNo',
        UserDetails?.user_phone_number ? UserDetails?.user_phone_number : ''
      );
      formikRef.current.setFieldValue(
        'pi_hphoneNo',
        UserDetails?.user_phone_number ? UserDetails?.user_phone_number : ''
      );
      formikRef.current.setFieldValue(
        'pi_country',
        UserDetails?.country ? UserDetails?.country : ''
      );
      formikRef.current.setFieldValue(
        'pi_gender',
        UserDetails?.user_gender
          ? UserDetails?.user_gender
          : UserDetails?.user_gender_other
            ? UserDetails?.user_gender_other
            : ''
      );
      formikRef.current.setFieldValue(
        'ph_gender',
        UserDetails?.user_gender
          ? UserDetails?.user_gender
          : UserDetails?.user_gender_other
            ? UserDetails?.user_gender_other
            : ''
      );
      formikRef.current.setFieldValue(
        'ph_marital',
        UserDetails?.marital_status
          ? UserDetails?.marital_status
          : UserDetails?.marital_status_other
            ? UserDetails?.marital_status_other
            : ''
      );
    }, 600);
  };
  const onchangeP45 = (value) => {
    setP45Details(value);
    setP45();
    setNewStartHMRCDetails();
    setPlanChecked([]);
    if (value === 'no') {
      setTimeout(() => {
        formikRef.current.setFieldValue('sl_pg', value);
      }, 600);
      setFormUserdetails();
    }
  };
  const handleChangeData = (dataValue) => {
    setP45(dataValue);
  };
  const onchangePostLoan = (value) => {
    setPostLoan(value);
    setP45();
    setNewStartHMRCDetails();
    setPlanChecked([]);

    if (value === 'yes') {
      setTimeout(() => {
        formikRef.current.setFieldValue('sl_pg', value);
      }, 600);
      setFormUserdetails();
    }
  };
  const OnboardingForms = async () => {
    setLoader(true);
    const body = new FormData();
    p45 && p45?.[0] && body.append('hmrc_p45_form', p45?.[0]);
    const method = isMyProfile ? 'post' : 'put';
    const ApiUrl = isMyProfile
      ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${2}`
      : URLS.UPDATE_ONBOARDING_FORM +
        `?form_user_id=${UserId}&checklist_id=${2}`;
    body.append('has_student_or_pg_loan', postLoan === 'yes' ? true : false);
    p45details &&
      body.append('has_p45_form', p45details === 'yes' ? true : false);
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    try {
      const { status, data } = await axiosInstance[method](
        ApiUrl,
        body,
        config
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getOnboardingChecklist(UserId);
        } else {
          setApiMessage('error', data?.message);
        }
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (
      UserDetails &&
      ((formNewStarterHMRC?.has_student_or_pg_loan !== undefined &&
        formNewStarterHMRC?.has_student_or_pg_loan !== null) ||
        (formNewStarterHMRC?.has_p45_form !== undefined &&
          formNewStarterHMRC?.has_p45_form !== null))
    ) {
      setNewStartDetails(UserDetails);
      setNewStartHMRCDetails(formNewStarterHMRC);
      if (
        formNewStarterHMRC?.has_student_or_pg_loan !== null &&
        formNewStarterHMRC?.has_student_or_pg_loan !== undefined
      ) {
        formNewStarterHMRC?.has_student_or_pg_loan
          ? setPostLoan('yes')
          : setPostLoan('no');
      }

      if (
        formNewStarterHMRC?.has_p45_form !== null &&
        formNewStarterHMRC?.has_p45_form !== undefined
      ) {
        formNewStarterHMRC?.has_p45_form
          ? setP45Details('yes')
          : setP45Details('no');
      }

      if (
        formNewStarterHMRC?.has_student_or_pg_loan === true ||
        formNewStarterHMRC?.has_p45_form === false
      ) {
        setPlanChecked(
          formNewStarterHMRC?.load_guidance
            ? formNewStarterHMRC?.load_guidance?.split(',').map(Number)
            : []
        );
        setTimeout(() => {
          formikRef.current.setFieldValue(
            'pi_fname',
            UserDetails?.user_first_name ? UserDetails?.user_first_name : ''
          );
          formikRef.current.setFieldValue(
            'pi_mname',
            UserDetails?.user_middle_name ? UserDetails?.user_middle_name : ''
          );
          formikRef.current.setFieldValue(
            'pi_lname',
            UserDetails?.user_last_name ? UserDetails?.user_last_name : ''
          );
          formikRef.current.setFieldValue(
            'pi_email',
            UserDetails?.user_email ? UserDetails?.user_email : ''
          );

          formikRef.current.setFieldValue(
            'pi_phoneNo',
            UserDetails?.user_phone_number ? UserDetails?.user_phone_number : ''
          );
          formikRef.current.setFieldValue(
            'pi_hphoneNo',
            UserDetails?.user_phone_number ? UserDetails?.user_phone_number : ''
          );
          formikRef.current.setFieldValue(
            'pi_gender',
            UserDetails?.user_gender
              ? UserDetails?.user_gender
              : UserDetails?.user_gender_other
                ? UserDetails?.user_gender_other
                : ''
          );
          formikRef.current.setFieldValue(
            'pi_country',
            UserDetails?.country ? UserDetails?.country : ''
          );
          formikRef.current.setFieldValue(
            'ph_gender',
            UserDetails?.user_gender
              ? UserDetails?.user_gender
              : UserDetails?.user_gender_other
                ? UserDetails?.user_gender_other
                : ''
          );
          formikRef.current.setFieldValue(
            'ph_marital',
            UserDetails?.marital_status
              ? UserDetails?.marital_status
              : UserDetails?.marital_status_other
                ? UserDetails?.marital_status_other
                : ''
          );
          if (
            formNewStarterHMRC?.load_guidance &&
            (formNewStarterHMRC?.has_student_or_pg_loan ||
              formNewStarterHMRC?.has_p45_form)
          ) {
            formikRef.current.setFieldValue(
              'sl_plan',
              formNewStarterHMRC?.load_guidance
                ? formNewStarterHMRC?.load_guidance?.split(',').map(Number)
                : []
            );
          }
        }, 600);
      } else if (
        formNewStarterHMRC?.has_student_or_pg_loan === false &&
        formNewStarterHMRC?.has_p45_form === true
      ) {
        formNewStarterHMRC?.hmrc_p45_form &&
          formNewStarterHMRC?.hmrc_p45_form_link &&
          setP45({
            name: formNewStarterHMRC?.hmrc_p45_form,
            url: formNewStarterHMRC?.hmrc_p45_form_link,
            isApiData: true,
          });
      }
    } else {
      setNewStartDetails('');
      setNewStartHMRCDetails('');
      setPostLoan();
      setP45Details();
      setPlanChecked([]);
      setP45();
    }
  }, [UserDetails, formNewStarterHMRC]);

  const plan = (newChecked) => {
    formikRef.current.setFieldValue('sl_plan', newChecked);
  };
  const removeValue = async (state) => {
    if (state?.isApiData) {
      // DeleteUploadedFile(state?.name, setState);
    } else {
      setP45();
    }
  };
  const download = async (urlLink, name) => {
    const response = await fetch(urlLink);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', name);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  const FormSection = ({ title, children, isUserScreen }) => (
    <>
      <Typography
        className={`${isUserScreen ? 'body-text text-underline fw600' : 'body-text text-underline fw600'} ${title !== 'Personal information' ? 'pt32' : ''}`}
      >
        {title}
      </Typography>
      {children}
    </>
  );
  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };
  const FormContent = ({
    isUserScreen,
    values,
    errors,
    touched,
    handleChange,
    setFieldValue,
    handleBlur,
    ViewAccessOnly,
    plan,
    planchecked,
    setPlanChecked,
    countryList,
  }) => (
    <Box
      className={
        isUserScreen ? 'right-to-work-page' : 'page-section right-to-work-page'
      }
    >
      <FormSection title="Personal information" isUserScreen={isUserScreen}>
        <Personalinfo
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
          countryList={countryList}
        />
      </FormSection>

      <FormSection title="Bank details" isUserScreen={isUserScreen}>
        <BankDetails
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <FormSection title="Next of kin details" isUserScreen={isUserScreen}>
        <NextOfkin
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <FormSection
        title="Additional personal and health details"
        isUserScreen={isUserScreen}
      >
        <PersonalHealthDetails
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <FormSection title="Professional details" isUserScreen={isUserScreen}>
        <ProffesionalDetails
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <FormSection title="Employee statement" isUserScreen={isUserScreen}>
        <EmployeeStatement
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <FormSection
        title="Student loans (Only valid for UK)"
        isUserScreen={isUserScreen}
      >
        <StudentsLoan
          touched={touched}
          errors={errors}
          values={values}
          handleChange={handleChange}
          setFieldValue={setFieldValue}
          handleBlur={handleBlur}
          plan={plan}
          planchecked={planchecked}
          setPlanChecked={setPlanChecked}
          ViewAccessOnly={ViewAccessOnly}
        />
      </FormSection>

      <Box className="pt16">
        <CustomButton
          variant="contained"
          className="red-button"
          type="submit"
          title={`${loader ? 'Saving...' : 'Save'}`}
          fullWidth={false}
          onClick={async () => {
            if (formikRef?.current) {
              const errors = await formikRef.current.validateForm();
              if (Object.keys(errors).length > 0) {
                scrollToError(errors);
                formikRef.current.setSubmitting(false);
              }
            }
          }}
          disabled={ViewAccessOnly || loader || !values?.confirmde}
        />
      </Box>
    </Box>
  );

  return (
    <>
      <Box className={!isUserScreen && 'page-container'}>
        <FormQuestion
          question="Do you have a Student Loan/Postgraduate Loan? (ONLY UK Citizen)"
          ViewAccessOnly={ViewAccessOnly}
          setValue={onchangePostLoan}
          Value={postLoan}
          keyName="studentLoan"
        />
        {postLoan === 'no' && (
          <FormQuestion
            question="Do you have a P45 form?"
            ViewAccessOnly={ViewAccessOnly}
            setValue={onchangeP45}
            Value={p45details}
            keyName="studentp45"
          />
        )}
        {postLoan === 'yes' || p45details === 'no' ? (
          <Box className="pt16 hmrc-newstart-section">
            <Formik
              innerRef={formikRef}
              initialValues={{
                pi_fname: newStarterDetails?.user_first_name
                  ? newStarterDetails?.user_first_name
                  : '',
                pi_mname: newStarterDetails?.user_middle_name
                  ? newStarterDetails?.user_middle_name
                  : '',
                pi_lname: newStarterDetails?.user_last_name
                  ? newStarterDetails?.user_last_name
                  : '',
                pi_email: newStarterDetails?.user_email
                  ? newStarterDetails?.user_email
                  : '',
                pi_phoneNo: newStarterDetails?.user_phone_number
                  ? newStarterDetails?.user_phone_number
                  : '',
                pi_hphoneNo: newStarterDetails?.emergency_contact
                  ? newStarterDetails?.emergency_contact
                  : '',
                ph_gender: newStarterDetails?.user_gender
                  ? newStarterDetails?.user_gender
                  : newStarterDetails?.user_gender_other
                    ? newStarterDetails?.user_gender_other
                    : '',
                ph_marital: newStarterDetails?.marital_status
                  ? newStarterDetails?.marital_status
                  : newStarterDetails?.marital_status_other
                    ? newStarterDetails?.marital_status_other
                    : '',
                pi_country: newStarterDetails?.country
                  ? newStarterDetails?.country
                  : '',
                pi_gender: newStarterDetails?.user_gender
                  ? newStarterDetails?.user_gender
                  : newStarterDetails?.user_gender_other
                    ? newStarterDetails?.user_gender_other
                    : '',
                pi_passport: newStarterHMRCDetails?.passport_no
                  ? newStarterHMRCDetails?.passport_no
                  : '',
                pi_issuingAuth: newStarterHMRCDetails?.issued_date
                  ? dayjs(newStarterHMRCDetails?.issued_date).format(
                      'YYYY-MM-DD'
                    )
                  : '',
                pi_workpermit: newStarterHMRCDetails?.permit_type_other
                  ? newStarterHMRCDetails?.permit_type_other
                  : '',
                workpermit: newStarterHMRCDetails?.permit_type
                  ? newStarterHMRCDetails?.permit_type
                  : '',
                pi_validity: newStarterHMRCDetails?.validity
                  ? dayjs(newStarterHMRCDetails?.validity).format('YYYY-MM-DD')
                  : '',
                insurance: newStarterHMRCDetails?.insurance_number
                  ? newStarterHMRCDetails?.insurance_number
                  : '',
                nk_name1: newStarterHMRCDetails?.kin1_name
                  ? newStarterHMRCDetails?.kin1_name
                  : '',
                nk_relationship1: newStarterHMRCDetails?.kin1_relation
                  ? newStarterHMRCDetails?.kin1_relation
                  : '',
                nk_phoneNo1: newStarterHMRCDetails?.kin1_mobile_number
                  ? newStarterHMRCDetails?.kin1_mobile_number
                  : '',
                nk_address1: newStarterHMRCDetails?.kin1_address
                  ? newStarterHMRCDetails?.kin1_address
                  : '',
                nk_name2: newStarterHMRCDetails?.kin2_name
                  ? newStarterHMRCDetails?.kin2_name
                  : '',
                nk_relationship2: newStarterHMRCDetails?.kin2_relation
                  ? newStarterHMRCDetails?.kin2_relation
                  : '',
                nk_phoneNo2: newStarterHMRCDetails?.kin2_mobile_number
                  ? newStarterHMRCDetails?.kin2_mobile_number
                  : '',
                nk_address2: newStarterHMRCDetails?.kin2_address
                  ? newStarterHMRCDetails?.kin2_address
                  : '',
                pf_name1: newStarterHMRCDetails?.professional1_name_contact
                  ? newStarterHMRCDetails?.professional1_name_contact
                  : '',
                pf_description1:
                  newStarterHMRCDetails?.professional1_role_description
                    ? newStarterHMRCDetails?.professional1_role_description
                    : '',
                pf_name2: newStarterHMRCDetails?.professional2_name_contact
                  ? newStarterHMRCDetails?.professional2_name_contact
                  : '',
                pf_description2:
                  newStarterHMRCDetails?.professional2_role_description
                    ? newStarterHMRCDetails?.professional2_role_description
                    : '',
                pf_start_date1: newStarterHMRCDetails?.professional1_start_date
                  ? dayjs(
                      newStarterHMRCDetails?.professional1_start_date
                    ).format('YYYY-MM-DD')
                  : null,
                pf_end_date1: newStarterHMRCDetails?.professional1_end_date
                  ? dayjs(newStarterHMRCDetails?.professional1_end_date).format(
                      'YYYY-MM-DD'
                    )
                  : null,
                pf_start_date2: newStarterHMRCDetails?.professional2_start_date
                  ? dayjs(
                      newStarterHMRCDetails?.professional2_start_date
                    ).format('YYYY-MM-DD')
                  : null,
                pf_end_date2: newStarterHMRCDetails?.professional2_end_date
                  ? dayjs(newStarterHMRCDetails?.professional2_end_date).format(
                      'YYYY-MM-DD'
                    )
                  : null,
                bi_acname: newStarterHMRCDetails?.bank_account_name
                  ? newStarterHMRCDetails?.bank_account_name
                  : '',
                bi_acnumber: newStarterHMRCDetails?.bank_account_number
                  ? newStarterHMRCDetails?.bank_account_number
                  : '',
                bi_sortcode: newStarterHMRCDetails?.bank_sort_code
                  ? newStarterHMRCDetails?.bank_sort_code
                  : '',
                bi_bankdetails: newStarterHMRCDetails?.bank_society_name
                  ? newStarterHMRCDetails?.bank_society_name
                  : '',
                bi_address: newStarterHMRCDetails?.bank_address
                  ? newStarterHMRCDetails?.bank_address
                  : '',
                ph_health: newStarterHMRCDetails?.medical_disability
                  ? 'yes'
                  : newStarterHMRCDetails?.medical_disability === false
                    ? 'no'
                    : '',
                ph_healthdetails:
                  newStarterHMRCDetails?.medical_disability_detail
                    ? newStarterHMRCDetails?.medical_disability_detail
                    : '',
                confirmde: newStarterHMRCDetails?.is_current_information
                  ? newStarterHMRCDetails?.is_current_information
                  : '',
                es_job: newStarterHMRCDetails?.another_job
                  ? 'yes'
                  : newStarterHMRCDetails?.another_job === false
                    ? 'no'
                    : '',
                es_payments: newStarterHMRCDetails?.private_pension
                  ? 'yes'
                  : newStarterHMRCDetails?.private_pension === false
                    ? 'no'
                    : '',
                es_payments_april: newStarterHMRCDetails?.payment_from
                  ? 'yes'
                  : newStarterHMRCDetails?.payment_from === false
                    ? 'no'
                    : '',
                statementa: newStarterHMRCDetails?.statementA
                  ? 'yes'
                  : newStarterHMRCDetails?.statementA === false
                    ? 'no'
                    : '',
                statementb: newStarterHMRCDetails?.statementB
                  ? 'yes'
                  : newStarterHMRCDetails?.statementB === false
                    ? 'no'
                    : '',
                statementc: newStarterHMRCDetails?.statementC
                  ? 'yes'
                  : newStarterHMRCDetails?.statementC === false
                    ? 'no'
                    : '',
                sl_pg: newStarterHMRCDetails?.postgraduate_loan
                  ? 'yes'
                  : newStarterHMRCDetails?.postgraduate_loan === false
                    ? 'no'
                    : '',
                sl_stmt: newStarterHMRCDetails?.statement_apply
                  ? 'yes'
                  : newStarterHMRCDetails?.statement_apply === false
                    ? 'no'
                    : '',
                sl_plan: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                pi_fname: Yup.string()
                  .trim()
                  .required('This field is required'),
                // pi_mname: Yup.string()
                //   .trim()
                //   .required('This field is required'),
                pi_lname: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_validity: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_country: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_gender: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_email: Yup.string()
                  .required('This field is required')
                  .matches(
                    /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
                    'Please enter valid email'
                  ),
                pi_phoneNo: Yup.string()
                  .trim()
                  .required('This field is required')
                  .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
                pi_hphoneNo: Yup.string()
                  .trim()
                  .required('This field is required')
                  .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
                pi_passport: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_issuingAuth: Yup.string()
                  .trim()
                  .required('This field is required'),
                pi_workpermit: Yup.string().test(
                  'required-if-yes',
                  'This field is required',
                  function (value) {
                    const ph_health = this.parent.pi_workpermit;
                    if (ph_health === 'Other') {
                      return !!value;
                    }
                    return true;
                  }
                ),
                workpermit: Yup.string()
                  .trim()
                  .required('This field is required'),

                // Bank details
                bi_acname: Yup.string()
                  .trim()
                  .required('This field is required'),
                bi_acnumber: Yup.string()
                  .trim()
                  .required('This field is required')
                  .min(8, 'Account number must be at least 8 characters'),

                bi_sortcode: Yup.string()
                  .trim()
                  .required('This field is required')
                  .matches(/^\d{2}-\d{2}-\d{2}$/, 'Invalid sort code'),

                ph_health: Yup.string()
                  .trim()
                  .required('This field is required'),
                ph_healthdetails: Yup.string().test(
                  'required-if-yes',
                  'This field is required',
                  function (value) {
                    const ph_health = this.parent.ph_health;
                    if (ph_health === 'yes') {
                      return !!value;
                    }
                    return true;
                  }
                ),
                ph_gender: Yup.string()
                  .trim()
                  .required('This field is required'),
                ph_marital: Yup.string()
                  .trim()
                  .required('This field is required'),
                confirmde: Yup.string()
                  .trim()
                  .required('This field is required'),
                es_job: Yup.string().trim().required('This field is required'),
                es_payments: Yup.string()
                  .trim()
                  .required('This field is required'),
                es_payments_april: Yup.string().test(
                  'required-if-yes',
                  'This field is required',
                  function (value) {
                    const es_job = this.parent.es_job;
                    const es_payments = this.parent.es_payments;
                    if (es_job !== 'yes' && es_payments !== 'yes') {
                      return !!value;
                    }
                    return true;
                  }
                ),
                statementc: Yup.string().test(
                  'required-if-yes',
                  'At least one of statement A, statement B or statement C must be provided',
                  function (value) {
                    const statementa = this.parent.statementa;
                    const statementb = this.parent.statementb;
                    if (!statementb && !statementa) {
                      return !!value;
                    }
                    return true;
                  }
                ),
                sl_pg: Yup.string().trim().required('This field is required'),
                sl_stmt: Yup.string().test(
                  'required-if-yes',
                  'This field is required',
                  function (value) {
                    const sl_pg = this.parent.sl_pg;
                    if (sl_pg === 'yes') {
                      return !!value;
                    }
                    return true;
                  }
                ),
                sl_plan: Yup.array()
                  .test(
                    'required-if-yes',
                    'This field is required',
                    function (value) {
                      const { sl_pg, sl_stmt } = this.parent;
                      if (sl_pg === 'yes' && sl_stmt === 'no') {
                        return !!value && value.length > 0;
                      }
                      return true;
                    }
                  )
                  .test(
                    'min-length-if-yes',
                    'At least one role must be selected',
                    function (value) {
                      const { sl_pg, sl_stmt } = this.parent;
                      if (sl_pg === 'yes' && sl_stmt === 'no') {
                        return value && value.length >= 1;
                      }
                      return true;
                    }
                  ),
              })}
              onSubmit={async (requestData) => {
                setLoader(true);
                let sendData = {
                  ...(!isMyProfile && {
                    user_first_name: requestData?.pi_fname,
                    user_middle_name: requestData?.pi_mname,
                    user_last_name: requestData?.pi_lname,
                    user_email: requestData?.pi_email,
                    ...(requestData?.pi_phoneNo && {
                      user_phone_number: requestData?.pi_phoneNo.toString(),
                    }),
                    ...(requestData?.pi_hphoneNo && {
                      emergency_contact: requestData?.pi_hphoneNo.toString(),
                    }),
                    birth_country: requestData?.pi_country,
                    // date_of_birth: requestData?.pf_date,
                    ...(requestData?.ph_gender &&
                      (requestData?.ph_gender === 'male' ||
                        requestData?.ph_gender == 'female') && {
                        user_gender: requestData?.ph_gender,
                      }),
                    ...(requestData?.ph_gender &&
                      requestData?.ph_gender !== 'male' &&
                      requestData?.ph_gender !== 'female' && {
                        user_gender_other: requestData?.ph_gender,
                      }),
                    ...(requestData?.ph_marital &&
                      (requestData?.ph_marital === 'single' ||
                        requestData?.ph_marital === 'married') && {
                        marital_status: requestData?.ph_marital,
                      }),
                    ...(requestData?.ph_marital &&
                      requestData?.ph_marital !== 'single' &&
                      requestData?.ph_marital !== 'married' && {
                        marital_status_other: requestData?.ph_marital,
                      }),
                  }),
                  //New starter form
                  bank_account_name: requestData?.bi_acname
                    ? requestData?.bi_acname
                    : null,
                  bank_account_number: requestData?.bi_acnumber
                    ? requestData?.bi_acnumber
                    : null,
                  bank_sort_code: requestData?.bi_sortcode
                    ? requestData?.bi_sortcode
                    : null,
                  bank_society_name: requestData?.bi_bankdetails
                    ? requestData?.bi_bankdetails
                    : null,
                  bank_address: requestData?.bi_address
                    ? requestData?.bi_address
                    : null,
                  kin1_name: requestData?.nk_name1
                    ? requestData?.nk_name1
                    : null,
                  kin1_relation: requestData?.nk_relationship1
                    ? requestData?.nk_relationship1
                    : null,
                  kin1_address: requestData?.nk_address1
                    ? requestData?.nk_address1
                    : null,
                  kin1_mobile_number: requestData?.nk_phoneNo1
                    ? requestData?.nk_phoneNo1.toString()
                    : null,
                  kin2_name: requestData?.nk_name2
                    ? requestData?.nk_name2
                    : null,
                  kin2_relation: requestData?.nk_relationship2
                    ? requestData?.nk_relationship2
                    : null,
                  kin2_address: requestData?.nk_address2
                    ? requestData?.nk_address2
                    : null,
                  kin2_mobile_number: requestData?.nk_phoneNo2
                    ? requestData?.nk_phoneNo2.toString()
                    : null,
                  professional1_name_contact: requestData?.pf_name1
                    ? requestData?.pf_name1
                    : null,
                  professional1_role_description: requestData?.pf_description1
                    ? requestData?.pf_description1
                    : null,
                  professional2_name_contact: requestData?.pf_name2
                    ? requestData?.pf_name2
                    : null,
                  professional2_role_description: requestData?.pf_description2
                    ? requestData?.pf_description2
                    : null,
                  professional1_start_date: requestData?.pf_start_date1
                    ? requestData?.pf_start_date1
                    : null,
                  professional1_end_date: requestData?.pf_end_date1
                    ? requestData?.pf_end_date1
                    : null,
                  professional2_start_date: requestData?.pf_start_date2
                    ? requestData?.pf_start_date2
                    : null,
                  professional2_end_date: requestData?.pf_end_date2
                    ? requestData?.pf_end_date2
                    : null,
                  passport_no: requestData?.pi_passport,
                  issued_date: dayjs(requestData?.pi_issuingAuth).format(
                    'YYYY-MM-DD'
                  ),
                  permit_type: requestData?.workpermit,
                  ...(requestData?.workpermit === 'Other' && {
                    permit_type_other: requestData?.pi_workpermit,
                  }),
                  ...(requestData?.workpermit !== 'Other' && {
                    permit_type_other: '',
                  }),
                  validity: dayjs(requestData?.pi_validity).format(
                    'YYYY-MM-DD'
                  ),
                  medical_disability:
                    requestData?.ph_health === 'yes' ? true : false,
                  medical_disability_detail: requestData?.ph_healthdetails,
                  //Hmrc form
                  insurance_number: requestData?.insurance,
                  postgraduate_loan:
                    requestData?.sl_pg && requestData?.sl_pg === 'yes'
                      ? true
                      : false,
                  statement_apply:
                    requestData?.sl_stmt && requestData?.sl_stmt === 'yes'
                      ? true
                      : false,
                  is_current_information: requestData?.confirmde ? true : false,
                  another_job:
                    requestData?.es_job && requestData?.es_job === 'yes'
                      ? true
                      : false,
                  country: requestData?.country,
                  private_pension:
                    requestData?.es_payments &&
                    requestData?.es_payments === 'yes'
                      ? true
                      : false,
                  payment_from:
                    requestData?.es_payments_april &&
                    requestData?.es_payments_april === 'yes'
                      ? true
                      : false,

                  ...(requestData?.sl_stmt &&
                    requestData?.sl_stmt === 'no' && {
                      load_guidance: requestData?.sl_plan.toString(),
                    }),
                  statementA:
                    requestData?.statementa && requestData?.statementa === 'yes'
                      ? true
                      : false,
                  statementB:
                    requestData?.statementb && requestData?.statementb === 'yes'
                      ? true
                      : false,
                  statementC:
                    requestData?.statementc && requestData?.statementc === 'yes'
                      ? true
                      : false,
                  has_student_or_pg_loan: postLoan === 'yes' ? true : false,
                  ...(p45details && {
                    has_p45_form: p45details === 'yes' ? true : false,
                  }),
                };
                const method = isMyProfile ? 'post' : 'put';
                const ApiUrl = isMyProfile
                  ? URLS.CREATE_ONBOARDING_FORM + `?checklist_id=${2}`
                  : URLS.UPDATE_ONBOARDING_FORM +
                    `?form_user_id=${UserId}&checklist_id=${2}`;
                try {
                  const { status, data } = await axiosInstance[method](
                    ApiUrl,
                    sendData
                  );

                  if (status === 200) {
                    if (data?.status) {
                      setApiMessage('success', data?.message);
                      getOnboardingChecklist(UserId);
                    } else {
                      setApiMessage('error', data?.message);
                    }
                    setLoader(false);
                  }
                } catch (error) {
                  setLoader(false);
                  setApiMessage('error', error?.response?.data?.message);
                }
              }}
            >
              {({
                errors,
                touched,
                handleBlur,
                values,
                handleSubmit,
                handleChange,
                setFieldValue,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <FormContent
                    isUserScreen={isUserScreen}
                    values={values}
                    errors={errors}
                    touched={touched}
                    handleChange={handleChange}
                    setFieldValue={setFieldValue}
                    handleBlur={handleBlur}
                    ViewAccessOnly={ViewAccessOnly}
                    plan={plan}
                    planchecked={planchecked}
                    setPlanChecked={setPlanChecked}
                    countryList={countryList}
                  />
                </Form>
              )}
            </Formik>
          </Box>
        ) : p45details === 'yes' ? (
          <>
            {' '}
            <Box className={'right-to-work-checklist rtwc-upload-grid mt24'}>
              {p45 ? (
                <Box className="upload-download-sec rtwc-upload h100">
                  <Box className="title-section">
                    <Box className="title">
                      <Typography className="body-text fw600">
                        P45
                        <span className="color-red">*</span>
                      </Typography>
                      <Typography className="sub-title-text">
                        If Applicable
                      </Typography>
                    </Box>
                    <CustomButton
                      variant="contained"
                      className="red-button"
                      title={'Download'}
                      fullWidth={false}
                      disabled={!p45?.isApiData}
                      onClick={() => {
                        download(p45?.url, p45?.name);
                      }}
                    />
                  </Box>

                  <Box className="action-sec">
                    <HeaderImage
                      type="url"
                      imageUrl={p45?.isApiData && p45?.url}
                      Content={
                        <Typography
                          className={
                            p45?.isApiData
                              ? 'p14 fw400  cursor-pointer' //text-underline
                              : 'p14 fw400'
                          }
                        >
                          {p45?.[0]?.name
                            ? p45?.[0]?.name
                            : p45?.name
                              ? p45?.name
                              : ''}
                        </Typography>
                      }
                    />
                    {p45?.isApiData && (
                      <>
                        {' '}
                        <HeaderImage
                          type="url"
                          imageUrl={p45?.url}
                          Content={<LaunchIcon className="view-icon" />}
                          className="d-flex align-center"
                        />
                      </>
                    )}
                    {!p45?.isApiData && (
                      <CancelIcon
                        className="delete-icon"
                        onClick={() => removeValue(p45)}
                      />
                    )}
                  </Box>
                </Box>
              ) : (
                <FileUpload
                  name={'P45 '}
                  isRequire={true}
                  Subname={'If Applicable'}
                  handleChangeData={handleChangeData}
                  ViewAccessOnly={ViewAccessOnly}
                />
              )}
            </Box>
            <Box className={`pt32 ${!isUserScreen ? 'text-align' : ''}`}>
              <CustomButton
                variant="contained"
                className="red-button"
                title={`${loader ? 'Saving...' : 'Save'}`}
                onClick={OnboardingForms}
                fullWidth={false}
                disabled={!p45 || ViewAccessOnly}
              />
            </Box>
          </>
        ) : (
          <></>
        )}
      </Box>
    </>
  );
}
