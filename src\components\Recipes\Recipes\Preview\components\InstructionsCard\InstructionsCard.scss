.instructions-card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  border: var(--border-width-xs) solid var(--color-light-gray);
  box-shadow: var(--box-shadow-xs);

  &__header {
    padding: var(--spacing-lg) var(--spacing-xxl);
    border-bottom: var(--border-width-xs) solid var(--color-light-gray);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__content {
    padding: var(--spacing-lg);
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    border-left: var(--border-width-lg) solid var(--color-primary);
    padding-left: var(--spacing-md);
  }

  &__step-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
  }

  &__step-number {
    width: var(--spacing-xxl);
    height: var(--spacing-xxl);
    background-color: var(--text-color-white);
    color: var(--color-primary);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    border: var(--normal-sec-border);
  }

  &__step-content {
    flex: 1;
    display: flex;
    gap: var(--spacing-sm);
  }

  &__step-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-md);
  }

  &__details {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }

  &__detail {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);

    &--timing {
      background-color: var(--color-warning-opacity);
    }

    &--equipment {
      background-color: var(--color-success-opacity);
    }
  }

  &__detail-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
  }

  &__detail-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &--timing {
      color: var(--color-warning);
    }

    &--equipment {
      color: var(--color-success);
    }
  }

  &__detail-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
  }

  // Chef Notes & Quality Check
  &__note {
    margin-top: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);

    &--chef {
      background-color: var(--color-primary-opacity);
    }

    &--quality {
      background-color: var(--color-success-opacity);
    }
  }

  &__note-header {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  &__note-icon {
    flex-shrink: 0;
    margin-top: var(--spacing-xs);
  }

  &__note-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);

    &--chef {
      color: var(--color-primary);
    }

    &--quality {
      color: var(--color-success);
    }
  }

  &__note-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    margin-top: var(--spacing-xs);
  }

  &__image-wrap {
    line-height: 0px;
  }

  &__image {
    height: 80px;
    width: 80px;
  }
}
