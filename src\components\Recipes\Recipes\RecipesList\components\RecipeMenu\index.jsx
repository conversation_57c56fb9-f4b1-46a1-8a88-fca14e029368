import React, { useState, useRef, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './recipemenu.scss';

const RecipeMenu = ({
  recipe,
  onDuplicate,
  onDelete,
  onBookmark,
  onExport,
  position = 'right',
  onEdit,
  onView,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef(null);

  const menuItems = [
    {
      label: 'Edit Recipe',
      icon: 'Edit3',
      action: () => onEdit?.(recipe),
    },
    {
      label: 'View Details',
      icon: 'Eye',
      action: () => onView?.(recipe),
    },
    {
      label: recipe?.is_bookmarked ? 'Remove Bookmark' : 'Add Bookmark',
      icon: recipe?.is_bookmarked ? 'BookmarkMinus' : 'BookmarkPlus',
      action: () => onBookmark?.(recipe),
    },
    {
      label: 'Duplicate Recipe',
      icon: 'Copy',
      action: () => onDuplicate?.(recipe),
    },
    {
      label: 'Export Recipe',
      icon: 'Download',
      action: () => onExport?.(recipe),
    },
    {
      label: 'Delete Recipe',
      icon: 'Trash2',
      action: () => onDelete?.(recipe),
      variant: 'danger',
    },
  ];

  const handleMenuToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleMenuItemClick = (action) => {
    action();
    setIsOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef?.current && !menuRef.current.contains(event?.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="recipe-menu" ref={menuRef}>
      <button
        onClick={handleMenuToggle}
        className="recipe-menu__trigger"
        aria-label="Recipe options"
      >
        <Icon name="MoreVertical" size={16} />
      </button>

      {isOpen && (
        <div
          className={`recipe-menu__dropdown recipe-menu__dropdown--${position}`}
        >
          {menuItems.map((item, index) => (
            <button
              key={index}
              onClick={() => handleMenuItemClick(item.action)}
              className={`recipe-menu__item ${
                item.variant === 'danger' ? 'recipe-menu__item--danger' : ''
              }`}
            >
              <Icon name={item.icon} size={16} />
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecipeMenu;
