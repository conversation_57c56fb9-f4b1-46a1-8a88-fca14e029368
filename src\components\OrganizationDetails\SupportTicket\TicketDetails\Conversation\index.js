import { EmptyConversationIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import React from 'react';
import './conversation.scss';

export default function Conversation() {
  return (
    <Box className="convesation-wrap d-flex flex-col align-center justify-center  text-align pb32">
      <EmptyConversationIcon className="conversation-icon" />
      <Box>
        <Typography component="p" className="conversation-text">
          No Conversation available
        </Typography>
      </Box>
    </Box>
  );
}
