@import '@/styles/variable.scss';

.details-wrap {
    box-shadow: 0px 0px 2px $color-Dark-50;
    border-radius: 8px;
    padding: 20px;

    .ticket-details-wrap {

        .ticket-name {
            font-family: "Poppins", sans-serif !important;
            font-weight: 500;
            padding-bottom: 10px;

            @media(max-width:575px) {
                font-size: 20px;
            }
        }

        .detail-wrap {
            gap: 20px;

            .name-wrap,
            .time-wrap {
                font-family: "Poppins", sans-serif !important;
                font-size: 16px;
                color: $color-Dark-50;
            }

            .timer-icon {
                width: 18px;
                height: 18px;
                fill: $color-primary ;
            }

            .id-wrap {
                font-family: "Poppins", sans-serif !important;
                width: auto;
                box-shadow: 0px 0px 2px $color-Dark-20;
                padding: 0px 3px;
                font-size: 15px;
                color: black;
                border-radius: 3px;
                line-height: 20px
            }

            .name-wrap {
                display: inline-block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 10ch;
                cursor: pointer;

                @media(max-width:575px) {
                    max-width: 100%;
                    font-size: 15px;
                }
            }

            @media(max-width:575px) {
                flex-direction: column;
                align-items: flex-start !important;
            }
        }

    }

    .ticket-tab-handler {
        padding-top: 10px;

        .tabs-wrap {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ededed;

            .report-tabs {
                display: flex;
                align-items: center;
                justify-content: space-between;
                // border-bottom: 1px solid red;
                overflow: auto;

                .tab-list-sec {
                    .tab-name {
                        text-transform: none;
                        font-family: $PrimaryFont;
                        font-size: 14px;
                        line-height: 20px;
                        font-weight: 400;
                        color: #000000;
                        opacity: 1;
                        width: max-content;
                        padding: 5px;

                        @media (max-width: 1200px) {
                            width: max-content;
                            // min-width: 50%;
                        }
                    }

                    .MuiTabs-flexContainer {
                        align-items: center;
                        // justify-content: space-between;
                    }

                    .MuiTabs-indicator {
                        background-color: $color-primary;
                        height: 3px;
                    }

                    .Mui-selected {
                        color: $color-primary !important;
                    }

                    .MuiTabs-scroller {
                        display: block !important;
                    }

                    .MuiTabScrollButton-root {
                        display: block !important;

                        .MuiSvgIcon-root {
                            margin: 14px;
                        }
                    }
                }
            }
        }
    }

    @media(max-width:575px) {
        padding: 20px 20px 20px 10px;
        gap: 2px;
    }
}