'use client';

import React, { useEffect, useState, useContext } from 'react';
import { Box, Typography, Tab, Divider } from '@mui/material';
// import { Check } from '@mui/icons-material';
import AuthContext from '@/helper/authcontext';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';
import StorageIcon from '@mui/icons-material/Storage';
import CustomButton from '@/components/UI/CustomButton';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { formatDuration, setApiMessage } from '@/helper/common/commonFunctions';
import { TabContext, TabList, TabPanel } from '@mui/lab';
import Billing from '@/components/OrganizationDetails/Billing';
import PreLoader from '@/components/UI/Loader';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import moment from 'moment';
import './plandetails.scss';

export default function PlanDetails() {
  const { authState, setPlanDetail } = useContext(AuthContext);
  const [planDetails, setPlanDetails] = useState([]);
  const [tabVal, setTabVal] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [deleteSubscriptionModal, setDeleteSubscriptionModal] = useState(false);
  const [deleteSubscriptionData, setDeleteSubscriptionData] = useState('');
  const currency =
    authState?.currency_details && authState?.currency_details?.symbol
      ? authState?.currency_details?.symbol
      : '£';

  // const planFeatures = [
  //   { description: 'Enjoy all the features without restrictions.' },
  //   { description: 'Get unlimited access to every hub and feature.' },
  //   { description: 'Tools to easily manage your team and business.' },
  // ];

  const getPurchasedPlan = async () => {
    setIsLoading(true);
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_ALL_PURCHASED_SUBSCRIPTION_PLAN
      );
      if (status === 200) {
        const sortedData = data?.data?.sort((a, b) => {
          const statusOrder = { active: 0, queued: 1, inactive: 2 };
          return statusOrder[a.sp_status] - statusOrder[b.sp_status];
        });
        setPlanDetails(sortedData);
        setIsLoading(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setIsLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      const { data, status } = await axiosInstance.put(
        ORG_URLS?.CANCEL_SUB_PLAN + `/${deleteSubscriptionData?.plan_id}`
      );
      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          getPurchasedPlan();
          setDeleteSubscriptionModal(false);
          setDeleteSubscriptionData(null);
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (tabVal === 1 || tabVal === '1') {
      getPurchasedPlan();
    }
  }, [tabVal]);

  const planDetailsList = [
    { id: 1, name: 'My Plans' },
    { id: 2, name: 'Plans & Pricing' },
  ];

  const tabChangeHandler = (event, newValue) => {
    setTabVal(newValue);
  };
  const getTotalStorage = () => {
    const totalStorage = planDetails?.reduce((acc, curr) => {
      return acc + curr?.total_storage;
    }, 0);
    return totalStorage;
  };
  const getStorageSize = () => {
    const storageSize = planDetails?.find(
      (curr) => curr?.subscription_plans?.sub_storage_size
    );
    return storageSize?.subscription_plans?.sub_storage_size;
  };

  useEffect(() => {
    if (planDetails?.length > 0) {
      const totalStorage = planDetails?.reduce((acc, curr) => {
        return acc + curr?.total_storage;
      }, 0);
      setPlanDetail({ total_storage: totalStorage });
    }
  }, [planDetails]);

  return (
    <>
      <Box className="pb24">
        <TabContext value={String(tabVal)}>
          <Box className="tabs-wrap">
            <Box className="">
              <Box className="report-tabs">
                <TabList
                  variant="scrollable"
                  scrollButtons="auto"
                  onChange={tabChangeHandler}
                  aria-label="action tabs"
                  className="tab-list-sec"
                >
                  {planDetailsList?.map((obj, index) => {
                    return (
                      <Tab
                        key={index}
                        label={obj?.name}
                        value={String(obj?.id)}
                        className="tab-name"
                      />
                    );
                  })}
                </TabList>
              </Box>
            </Box>
            <Box className="">
              {Number(tabVal) === 1 && (
                <CustomButton
                  className=""
                  variant="contained"
                  title="Change plan"
                  onClick={() => {
                    setTabVal(2);
                  }}
                />
              )}
            </Box>
          </Box>
          <TabPanel value="1" className="pl0 pr0 pb0">
            {planDetails?.length > 0 && getTotalStorage() ? (
              <Box className="plan-details-container pb0">
                <Box className="plan-card plan-storage-wrap">
                  <Box className="employees-range mb0">
                    <StorageIcon className="employees-icon" />
                    <Typography className="plan-remaining-data storage">
                      {getTotalStorage()}{' '}
                      {getStorageSize() ? getStorageSize() : ''}
                    </Typography>
                    <Typography className="plan-remaining-data remaining">
                      ( Total storage as per active plans )
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ) : (
              <></>
            )}
            <Box className="plan-details-container">
              {isLoading ? (
                <PreLoader />
              ) : planDetails?.length > 0 ? (
                planDetails?.map((plans, index) => {
                  const today = moment();
                  const endDate = moment(plans?.sp_end_date);
                  const daysLeft = endDate.diff(today, 'days');

                  const isDowngradePlan =
                    !!planDetails?.find(
                      (item) =>
                        item?.subscription_plans?.subs_is_free_trial === 0 &&
                        item?.subscription_plans?.subs_plan_category ===
                          plans?.subscription_plans?.subs_plan_category &&
                        item?.subscription_plans?.subs_plan_cost <
                          plans?.subscription_plans?.subs_plan_cost
                    ) || false;

                  return (
                    <Box className="plan-card" key={index}>
                      <span className={`active-plan ${plans?.sp_status}`}>
                        {plans?.sp_status === 'inactive'
                          ? 'Canceled'
                          : plans?.sp_status}{' '}
                        Plan
                      </span>

                      <Box className="plan-header">
                        <Typography className="plan-name">
                          {plans?.subscription_plans?.subs_plan_name}
                        </Typography>
                        {plans?.subscription_plans?.subs_is_free_trial ===
                          0 && (
                          <Typography className="plan-type">
                            {plans?.subscription_plans?.subs_plan_category}{' '}
                            <span className="plan-sub-type">
                              (
                              {plans?.subscription_plans?.subs_plan_type ===
                              'one_time'
                                ? 'One Time'
                                : plans?.subscription_plans?.subs_plan_type}
                              )
                            </span>
                          </Typography>
                        )}
                        <Typography className="plan-description">
                          {plans?.subscription_plans?.subs_plan_description}
                        </Typography>
                      </Box>

                      <Box className="plan-price">
                        <Typography className="price">
                          {currency +
                            ' ' +
                            plans?.subscription_plans?.subs_plan_cost?.toFixed(
                              2
                            )}
                          {plans?.subscription_plans?.subs_total_days > 0 && (
                            <span className="duration">
                              /{' '}
                              {formatDuration(
                                plans?.subscription_plans?.subs_total_days
                              )}
                            </span>
                          )}
                        </Typography>
                      </Box>
                      {plans?.sp_status === 'active' &&
                        plans?.subscription_plans?.subs_is_free_trial === 1 && (
                          <Typography
                            component="p"
                            className="content-text next-payment-text"
                          >
                            Your free trial will end in{' '}
                            <span className="bold-date-text">
                              {daysLeft} day{daysLeft > 1 ? 's' : ''}
                            </span>{' '}
                            on{' '}
                            <span className="bold-date-text">
                              {moment(plans?.sp_end_date).format(
                                'Do MMMM YYYY'
                              )}
                            </span>
                            . Upgrade anytime to continue enjoying all features
                            without interruption.
                          </Typography>
                        )}

                      {plans?.sp_status === 'active' &&
                        plans?.subscription_plans?.subs_is_free_trial === 0 &&
                        plans?.subscription_plans?.subs_plan_type ===
                          'recursive' && (
                          <Typography
                            component="p"
                            className="content-text next-payment-text"
                          >
                            The next payment will be charged and your plan will
                            renew on{' '}
                            <span className="bold-date-text">
                              {moment(plans?.sp_end_date).format(
                                'Do MMMM YYYY'
                              )}
                            </span>
                            .
                          </Typography>
                        )}
                      {plans?.sp_status === 'queued' && (
                        <Typography
                          component="p"
                          className="content-text next-payment-text"
                        >
                          A downgrade is scheduled.
                          <br />
                          Your new plan will take effect on{' '}
                          <span className="bold-date-text">
                            {moment(plans?.sp_end_date).format('Do MMMM YYYY')}
                          </span>
                          , right after your current billing cycle ends.
                        </Typography>
                      )}
                      {isDowngradePlan &&
                      plans?.sp_status === 'inactive' &&
                      plans?.subscription_plans?.subs_plan_type !==
                        'one_time' ? (
                        <Typography
                          component="p"
                          className="content-text renew-payment-text"
                        >
                          Your subscription has been canceled. Your current plan
                          stays active until{' '}
                          <span className="bold-date-text">
                            {moment(plans?.sp_end_date).format('Do MMMM YYYY')}
                          </span>
                          . Your plan will be downgraded on{' '}
                          <span className="bold-date-text">
                            {moment(plans?.sp_end_date).format('Do MMMM YYYY')}
                          </span>
                          . You’ll move to your selected plan automatically.
                        </Typography>
                      ) : (
                        <>
                          {isDowngradePlan &&
                            plans?.subscription_plans?.subs_plan_type !==
                              'one_time' && (
                              <Typography
                                component="p"
                                className="content-text renew-payment-text"
                              >
                                Your plan will be downgraded in{' '}
                                <span className="bold-date-text">
                                  {daysLeft} day
                                  {daysLeft > 1 ? 's' : ''}
                                </span>{' '}
                                — on{' '}
                                <span className="bold-date-text">
                                  {moment(plans?.sp_end_date).format(
                                    'Do MMMM YYYY'
                                  )}
                                </span>
                                . <br />
                                You’ll move to your selected plan automatically
                                when the time comes.
                              </Typography>
                            )}
                          {plans?.sp_status === 'inactive' && (
                            <Typography
                              component="p"
                              className="content-text renew-payment-text"
                            >
                              Your subscription has been canceled.
                              <br />
                              Your current plan will stay active until{' '}
                              <span className="bold-date-text">
                                {moment(plans?.sp_end_date).format(
                                  'Do MMMM YYYY'
                                )}
                              </span>
                              . Feel free to resubscribe anytime before then!
                            </Typography>
                          )}
                        </>
                      )}
                      {plans?.sp_status !== 'queued' && (
                        <Typography className="plan-remaining-data">
                          Remaining:
                          <span className="remaining">
                            {' '}
                            {plans?.remaining_data || 0}/
                            {plans?.sp_limit_max || 0}{' '}
                            {plans?.subscription_plans?.subs_plan_category ===
                            'core'
                              ? 'Employees'
                              : plans?.subscription_plans?.sub_storage_size +
                                ' Storage'}
                          </span>
                        </Typography>
                      )}

                      <Box className="plan-benefits">
                        <Box className="plan-emp-benefites">
                          <Box className="employees-range">
                            {plans?.subscription_plans?.subs_plan_category ===
                            'core' ? (
                              <>
                                <GroupOutlinedIcon className="employees-icon" />
                                <Typography className="employees">
                                  {plans?.subscription_plans?.subs_limit_min}
                                  {plans?.subscription_plans?.subs_limit_max &&
                                    ' - '}
                                  {plans?.subscription_plans?.subs_limit_max}{' '}
                                  Employees
                                </Typography>
                              </>
                            ) : (
                              <>
                                <StorageIcon className="employees-icon" />
                                <Typography className="employees storage">
                                  {plans?.subscription_plans?.subs_limit_min}
                                  {plans?.subscription_plans?.subs_limit_max &&
                                    ' - '}
                                  {plans?.subscription_plans?.subs_limit_max}{' '}
                                  {plans?.subscription_plans?.sub_storage_size}
                                </Typography>
                              </>
                            )}
                          </Box>
                          <Box className="employees-range">
                            {plans?.subscription_plans?.subs_plan_category ===
                            'core' ? (
                              <>
                                <StorageIcon className="employees-icon" />
                                <Typography className="employees storage">
                                  {/* {plans?.subscription_plans?.subs_limit_min}
                                  {plans?.subscription_plans
                                    ?.sub_storage_value && ' - '} */}
                                  {plans?.total_storage}{' '}
                                  {plans?.subscription_plans?.sub_storage_size}
                                </Typography>
                              </>
                            ) : (
                              <></>
                            )}
                          </Box>
                        </Box>

                        <Typography className="benefits-title">
                          Plan Benefits
                        </Typography>
                        <Box
                          className="benefit-item"
                          dangerouslySetInnerHTML={{
                            __html:
                              plans?.subscription_plans?.subs_plan_content,
                          }}
                        />
                        {/* {planFeatures?.map((benefit, index) => (
                          <Box key={index} className="benefit-item">
                            <Check className="check-icon" />
                            <Typography className="benefit-text">
                              {benefit?.description}
                            </Typography>
                          </Box>
                        ))} */}
                      </Box>

                      {plans?.sp_status === 'active' &&
                        plans?.subscription_plans?.subs_is_free_trial === 0 &&
                        plans?.subscription_plans?.subs_plan_type !==
                          'one_time' && (
                          <>
                            <Divider />
                            <CustomButton
                              fullWidth
                              variant="outlined"
                              className="cancel-button"
                              onClick={() => {
                                setDeleteSubscriptionData(plans);
                                setDeleteSubscriptionModal(true);
                              }}
                            >
                              Cancel{' '}
                              {plans?.subscription_plans?.subs_plan_type ===
                              'one_time'
                                ? 'Plan'
                                : 'Subscription'}
                            </CustomButton>
                          </>
                        )}
                    </Box>
                  );
                })
              ) : (
                <Box className="no-data-found w100">
                  <Typography className="no-data-text">
                    No data found
                  </Typography>
                </Box>
              )}
            </Box>
          </TabPanel>
          <TabPanel value="2" className="pl0 pr0 pb0">
            <Billing setTabVal={setTabVal} />
          </TabPanel>
        </TabContext>
        {deleteSubscriptionModal && (
          <DialogBox
            open={deleteSubscriptionModal}
            handleClose={() => {
              setDeleteSubscriptionModal(false);
              setDeleteSubscriptionData(null);
            }}
            className="delete-modal"
            title="Delete Plan"
            dividerClass="delete-modal-divider"
            content={
              <DeleteModal
                handleCancel={() => {
                  setDeleteSubscriptionModal(false);
                  setDeleteSubscriptionData(null);
                }}
                handleConfirm={() => handleCancelSubscription()}
                text="Are you sure you wish to delete this plan?"
              />
            }
          />
        )}
      </Box>
    </>
  );
}
