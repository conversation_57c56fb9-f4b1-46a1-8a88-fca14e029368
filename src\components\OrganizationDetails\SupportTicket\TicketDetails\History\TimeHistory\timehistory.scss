@import '@/styles/variable.scss';

.container {
  position: relative;
  max-height: calc(100vh - 200px- var(--banner-height));
  overflow: scroll;

  .time-history-wrap {
    .history-item {
      .header-date-wrap {
        padding: 15px 0px;
        position: sticky;
        top: 0px;
        background: white;

        .header-date {
          font-weight: 700;
        }

        .calender-icon {
          background-color: white;
          position: absolute;
          left: -28px;
          fill: $color-Dark-30;
          height: 15px;
          width: 15px;
        }
      }

      .name-text-wrap {
        .name-wrap {
          font-weight: 700;
        }

        .circle-wrap {
          position: absolute;
          left: 48px;
          fill: $color-Dark-30;
          height: 5px;
          width: 5px;
        }
      }

      .time-wrap {
        color: $color-Dark-30;
      }

      .time-spent {
        display: inline-block;
        color: $color-Dark-30;
        padding-right: 5px;
      }

      .exicuted-on {
        display: inline-block;
        color: $color-Dark-30;
        padding-right: 5px;
      }
    }

    .devider-wrap {
      padding: 0px 20px 0px 50px;
    }
  }
}
