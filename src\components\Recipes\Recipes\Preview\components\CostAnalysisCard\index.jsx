import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './CostAnalysisCard.scss';

const CostAnalysisCard = ({ recipeData }) => {
  const laborCost = 24.0;
  const totalCost = recipeData.totalCost + laborCost;
  const costPerPortion = totalCost / recipeData.totalPortions;

  return (
    <div className="cost-analysis-card">
      <div className="cost-analysis-card__header">
        <p className="cost-analysis-card__title">
          <Icon name="Calculator" size={20} color="currentColor" />
          <span>Cost Analysis</span>
        </p>
      </div>

      <div className="cost-analysis-card__content">
        <div className="cost-analysis-card__breakdown">
          <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Ingredient Cost:</p>
            <p className="cost-analysis-card__value">
              ${recipeData.totalCost?.toFixed(2)}
            </p>
          </div>

          <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Labor Estimate:</p>
            <p className="cost-analysis-card__value">${laborCost.toFixed(2)}</p>
          </div>

          <div className="cost-analysis-card__item cost-analysis-card__item--total">
            <p className="cost-analysis-card__label cost-analysis-card__label--total">
              Total Recipe Cost:
            </p>
            <p className="cost-analysis-card__value cost-analysis-card__value--total">
              ${totalCost.toFixed(2)}
            </p>
          </div>

          <div className="cost-analysis-card__item">
            <p className="cost-analysis-card__label">Cost Per Portion:</p>
            <p className="cost-analysis-card__value cost-analysis-card__value--accent">
              ${costPerPortion.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostAnalysisCard;
