import React from 'react';
import {
  Box,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Typography,
} from '@mui/material';
import CheckBoxIcon from '@mui/icons-material/CheckBox';
import CheckBoxOutlineBlankIcon from '@mui/icons-material/CheckBoxOutlineBlank';

export default function FormQuestion({
  setValue,
  Value,
  ViewAccessOnly,
  question,
  keyName,
}) {
  return (
    <Box className="gender-section">
      <Typography className="title-text" id={keyName}>
        {question}
      </Typography>
      <Box className="form-checkbox">
        <FormGroup id="question">
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
              />
            }
            className="check-box-form title-text"
            onChange={() => {
              setValue('yes');
            }}
            name={keyName ? keyName : 'ph_health'}
            checked={Value === 'yes'}
            value={Value}
            label={'Yes'}
            disabled={ViewAccessOnly}
          />
        </FormGroup>
        <FormGroup>
          <FormControlLabel
            control={
              <Checkbox
                className="check-box "
                icon={<CheckBoxOutlineBlankIcon className="uncheck-icon" />}
                checkedIcon={<CheckBoxIcon className="check-icon" />}
                onChange={() => {
                  setValue('no');
                }}
              />
            }
            className="check-box-form title-text"
            name={keyName ? keyName : 'ph_health'}
            checked={Value === 'no'}
            value={Value}
            label={'No'}
            disabled={ViewAccessOnly}
          />
        </FormGroup>
      </Box>
    </Box>
  );
}
