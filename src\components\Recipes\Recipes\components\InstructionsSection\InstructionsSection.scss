// Instructions Section Component Styles
.instructions-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Header Section
  &__header {
    margin-bottom: var(--spacing-lg);
  }

  &__header-content {
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin-bottom: var(--spacing-xs);
  }

  // Empty State
  &__empty-state {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-lg);
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
  }

  &__empty-icon {
    margin: 0 auto var(--spacing-lg);
    color: var(--color-light-gray);
  }

  &__empty-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__empty-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-lg);
  }

  // Instructions List
  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  // Individual Step Item
  &__step {
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    transition: all 0.15s ease-out;
    cursor: move;

    &:hover {
      box-shadow: var(--box-shadow-xs);
    }

    &--dragging {
      opacity: 0.5;
    }

    &--drag-over {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }
  }

  // Step Content - Horizontal Layout
  &__step-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    width: 100%;

    @media (max-width: 767px) {
      flex-direction: column;
      gap: var(--spacing-md);
    }
  }

  // Step Controls (Drag Handle)
  &__step-controls {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: var(--spacing-xxl);
    justify-content: center;

    @media (max-width: 767px) {
      width: auto;
    }
  }

  &__drag-handle {
    color: var(--text-color-slate-gray);
    cursor: move;
  }

  // Step Number
  &__step-number {
    width: var(--spacing-2xl);
    height: var(--spacing-2xl);
    background-color: transparent;
    color: var(--text-color-black);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
    flex-shrink: 0;
    border: var(--border-width-xs) solid var(--border-color-light-gray);

    @media (max-width: 767px) {
      width: var(--spacing-xl);
      height: var(--spacing-xl);
      font-size: var(--font-size-sm);
    }
  }

  // Step Image
  &__step-image {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    border-radius: var(--border-radius-md);
    overflow: hidden;

    @media (max-width: 767px) {
      width: 80px;
      height: 80px;
    }

    // Custom styles for the image uploader in step context
    .step-image-upload {
      height: 100px;
      min-height: 100px;
      border-radius: var(--border-radius-md);

      .dropzone {
        height: 100px;
        min-height: 100px;
        border-radius: var(--border-radius-md);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-sm);

        .upload-text {
          font-size: var(--font-size-xxs);
          text-align: center;
          margin-top: var(--spacing-xs);
        }

        svg {
          width: var(--spacing-lg);
          height: var(--spacing-lg);
        }
      }

      @media (max-width: 767px) {
        height: 80px;
        min-height: 80px;

        .dropzone {
          height: 80px;
          min-height: 80px;
        }
      }
    }

    .step-image-preview {
      height: 100px;
      border-radius: var(--border-radius-md);
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      @media (max-width: 767px) {
        height: 80px;
      }
    }
  }

  // Step Description
  &__step-description {
    flex: 1;
    min-width: 0; // Allows text to wrap properly
  }

  // Step Actions (Delete Button)
  &__step-actions {
    display: flex;
    align-items: flex-start;
    flex-shrink: 0;
    padding-top: var(--spacing-xs);
  }

  &__optional-toggle {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xxs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;

    &--optional {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      &:hover {
        background-color: var(--color-warning);
        color: var(--text-color-white);
      }
    }

    &--required {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger);
        color: var(--text-color-white);
      }
    }
  }

  &__remove-button {
    padding: var(--spacing-sm);
    color: var(--text-color-slate-gray);
    background: none;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      color: var(--color-danger);
      background-color: var(--color-danger-opacity);
    }
  }

  // Add Button
  &__add-button {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
  }

  // Form Grids
  &__grid-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__field-label {
    display: block;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
  }

  // Summary Section
  &__summary {
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid rgba(19, 94, 150, 0.2);
    border-radius: var(--border-radius-md);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__summary-item {
    display: flex;
    flex-direction: column;
  }

  &__summary-count {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Error Display
  &__error {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-danger);
  }
}
