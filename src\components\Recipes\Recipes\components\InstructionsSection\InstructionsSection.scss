// Instructions Section Component Styles
.instructions-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  // Header Section
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: var(--spacing-lg);

    @media (max-width: 767px) {
      flex-direction: column;
      align-items: stretch;
    }
  }

  &__header-content {
    display: flex;
    flex-direction: column;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
  }

  &__description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-top: var(--spacing-tiny);
  }

  // Empty State
  &__empty-state {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-lg);
    border: var(--border-width-sm) dashed var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
  }

  &__empty-icon {
    margin: 0 auto var(--spacing-lg);
    color: var(--color-light-gray);
  }

  &__empty-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  &__empty-description {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-lg);
  }

  // Instructions List
  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  // Individual Step Item
  &__step {
    background-color: var(--color-white);
    border: var(--border-width-xs) solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    transition: all 0.15s ease-out;
    cursor: move;

    &:hover {
      box-shadow: var(--box-shadow-xs);
    }

    &--dragging {
      opacity: 0.5;
    }

    &--drag-over {
      border-color: var(--color-primary);
      background-color: var(--color-primary-opacity);
    }
  }

  &__step-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-lg);
  }

  &__step-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__step-number {
    // width: var(--spacing-2xl);
    // height: var(--spacing-2xl);
    background-color: var(--color-primary);
    color: var(--text-color-white);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    padding: var(--spacing-xsm) var(--spacing-md);
    font-family: var(--font-family-primary);
  }

  &__step-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__drag-handle {
    color: var(--text-color-slate-gray);
    cursor: move;
  }

  &__step-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-color-primary);
    background: transparent;
    border: none;
    outline: none;
    padding: var(--field-padding);
    border-radius: var(--field-radius);
    transition: all 0.15s ease-out;

    &:focus {
      background-color: var(--color-white);
      border: var(--field-border);
    }
  }

  &__step-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__optional-toggle {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xxs);
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    transition: all 0.15s ease-out;
    border: none;
    cursor: pointer;

    &--optional {
      background-color: var(--color-warning-opacity);
      color: var(--color-warning);
      &:hover {
        background-color: var(--color-warning);
        color: var(--text-color-white);
      }
    }

    &--required {
      background-color: var(--color-danger-opacity);
      color: var(--color-danger);

      &:hover {
        background-color: var(--color-danger);
        color: var(--text-color-white);
      }
    }
  }

  &__remove-button {
    padding: var(--spacing-sm);
    color: var(--color-danger);
    background: none;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.15s ease-out;

    &:hover {
      background-color: var(--color-danger-opacity);
    }
  }

  // Form Grids
  &__grid-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  &__field-label {
    display: block;
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
    margin-bottom: var(--spacing-sm);
  }

  // Summary Section
  &__summary {
    padding: var(--spacing-lg);
    background-color: var(--color-primary-opacity);
    border: var(--border-width-xs) solid rgba(19, 94, 150, 0.2);
    border-radius: var(--border-radius-md);
  }

  &__summary-title {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
  }

  &__summary-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);

    @media (min-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &__summary-item {
    display: flex;
    flex-direction: column;
  }

  &__summary-count {
    font-family: var(--font-family-primary);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
  }

  &__summary-label {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xxs);
    color: var(--text-color-slate-gray);
  }

  // Error Display
  &__error {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--color-danger);
  }
}
