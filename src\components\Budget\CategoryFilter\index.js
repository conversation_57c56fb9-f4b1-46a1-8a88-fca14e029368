'use client';
import React, { useState, useMemo } from 'react';
import { Box } from 'intergalactic/flex-box';
import Select from 'intergalactic/select';
import { FilterTrigger } from 'intergalactic/base-trigger';
import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
import ClearIcon from '@mui/icons-material/Clear';
import { Tooltip, Typography } from '@mui/material';
import LightDarkSwitch from '@/components/UI/Switch';
import Checkbox from '@mui/material/Checkbox';
import '@/components/DSR/Reports/Generals/generalpage.scss';
import '@/components/DSR/Reports/CategoryFilter/categoryfilter.scss';
import './category.scss';

export default function BudgetCategoryFilter({
  selectedMainCategories,
  selectedSubcategories,
  setSelectedMainCategories,
  // setSelectedSubcategories,
  selectedToggle,
  setSelectedToggle,
  dsrCatData,
  placeholder,
  isSignleSelect,
  isCreate,
}) {
  const [visible, setVisible] = useState(false);
  const [loading] = useState(false);

  // Filter category data based on search
  const categoryData = useMemo(() => {
    return dsrCatData?.map((paymentType) => ({
      label: paymentType?.payment_type_title,
      value: paymentType?.id,
      catList: paymentType?.catList || [],
    }));
  }, [dsrCatData]);

  // Function to handle visibility change of the dropdown.
  const handleChangeVisible = (visible) => {
    setVisible(visible);
    // if (visible) {
    //   setLoading(true);
    //   setTimeout(() => setLoading(false), 1000);
    // }
  };
  const getCategoryAndSubcategoryIds = (category) => {
    let categoryAndSubcategoryIds = [category?.id];
    if (category?.catList && category?.catList?.length > 0) {
      category?.catList?.forEach((subcat) => {
        categoryAndSubcategoryIds?.push(subcat?.id);
        if (subcat?.catList && subcat?.catList?.length > 0) {
          subcat?.catList?.forEach((nestedSubcat) => {
            categoryAndSubcategoryIds?.push(nestedSubcat?.id);
          });
        }
      });
    }
    return categoryAndSubcategoryIds;
  };
  const toggleCategoryExpand = (categoryId) => {
    let selectedmain = selectedToggle ? selectedToggle : [];
    if (selectedToggle?.includes(categoryId)) {
      selectedmain = selectedmain?.filter((id) => id !== categoryId);
    } else {
      selectedmain = [...selectedmain, categoryId];
    }
    setSelectedToggle(selectedmain);
  };
  // Function to toggle selection of a main category.
  const handleMainCategoryToggle = (mainCategoryId) => {
    let selectedmain = selectedMainCategories ? selectedMainCategories : [];
    if (selectedMainCategories?.includes(mainCategoryId)) {
      selectedmain = selectedmain?.filter((id) => id !== mainCategoryId);
    } else {
      selectedmain = isSignleSelect
        ? [mainCategoryId]
        : [...selectedmain, mainCategoryId];
    }

    const selectedMainCategoryData = dsrCatData?.find(
      (paymentType) => paymentType?.id === mainCategoryId
    );

    const allCategoryAndSubcategoryIds =
      selectedMainCategoryData?.catList?.flatMap((category) =>
        getCategoryAndSubcategoryIds(category)
      ) || [];

    let selectedSub = selectedSubcategories ? selectedSubcategories : [];
    if (selectedMainCategories?.includes(mainCategoryId)) {
      const updatedSubcategories = selectedSub?.filter((id) => {
        return !allCategoryAndSubcategoryIds.includes(id);
      });
      selectedSub = updatedSubcategories;
    } else {
      const updatedSubcategories = isSignleSelect
        ? [...new Set([...allCategoryAndSubcategoryIds])]
        : [...new Set([...selectedSub, ...allCategoryAndSubcategoryIds])];
      // const updatedSubcategories = [...allCategoryAndSubcategoryIds];
      selectedSub = updatedSubcategories;
    }
    // setSelectedSubcategories(selectedSub);
    setSelectedMainCategories(selectedmain, selectedSub);
  };

  const getSubcategoryIds = (category) => {
    let subcategoryIds = [category?.id];
    if (category?.catList && category?.catList?.length > 0) {
      category.catList?.forEach((subcat) => {
        subcategoryIds?.push(subcat?.id);
        if (subcat?.catList?.length > 0) {
          subcat.catList?.forEach((nestedSubcat) => {
            subcategoryIds?.push(nestedSubcat?.id);
          });
        }
      });
    }
    return subcategoryIds;
  };

  // Function to toggle the selection of a subcategory.
  const handleSubcategoryToggle = (subcategoryId, mainCategoryId) => {
    const selectedMainCategoryData = dsrCatData?.find(
      (paymentType) => paymentType?.id === mainCategoryId
    );
    const allSubcategoryIds =
      selectedMainCategoryData?.catList?.flatMap((category) =>
        getSubcategoryIds(category)
      ) || [];

    let updatedSubcategories = isSignleSelect
      ? selectedSubcategories.filter((id) => allSubcategoryIds.includes(id))
      : selectedSubcategories
        ? [...selectedSubcategories]
        : [];
    if (updatedSubcategories?.includes(subcategoryId)) {
      const index = updatedSubcategories?.indexOf(subcategoryId);
      if (index > -1) {
        updatedSubcategories?.splice(index, 1);

        selectedMainCategoryData?.catList?.forEach((category) => {
          category?.catList?.forEach((subcat) => {
            if (subcat?.id === subcategoryId && subcat?.catList) {
              subcat?.catList?.forEach((nestedSubcat) => {
                const nestedIndex = updatedSubcategories?.indexOf(
                  nestedSubcat?.id
                );
                if (nestedIndex > -1) {
                  updatedSubcategories?.splice(nestedIndex, 1);
                }
              });
            }
          });
        });
      }
    } else {
      updatedSubcategories?.push(subcategoryId);

      selectedMainCategoryData?.catList?.forEach((category) => {
        category.catList?.forEach((subcat) => {
          if (subcat.id === subcategoryId && subcat?.catList) {
            subcat?.catList?.forEach((nestedSubcat) => {
              if (!updatedSubcategories.includes(nestedSubcat?.id)) {
                updatedSubcategories?.push(nestedSubcat?.id);
              }
            });
          }
        });
      });
    }
    if (updatedSubcategories?.includes(subcategoryId)) {
      selectedMainCategoryData?.catList.forEach((category) => {
        if (category?.id === subcategoryId) {
          category?.catList?.forEach((subcat) => {
            if (!updatedSubcategories.includes(subcat?.id)) {
              updatedSubcategories = updatedSubcategories?.push(subcat?.id);
            }
          });
        } else if (
          category?.catList?.some((subcat) => subcat?.id === subcategoryId)
        ) {
          if (!updatedSubcategories.includes(category?.id)) {
            updatedSubcategories = updatedSubcategories?.push(category?.id);
          }
        }
      });
    } else {
      selectedMainCategoryData?.catList?.forEach((category) => {
        if (category?.id === subcategoryId) {
          category?.catList?.forEach((subcat) => {
            const subIndex = updatedSubcategories.indexOf(subcat?.id);
            if (subIndex > -1) {
              updatedSubcategories = updatedSubcategories?.splice(subIndex, 1);
            }
          });
        } else if (
          category?.catList?.some((subcat) => subcat?.id === subcategoryId)
        ) {
          const remainingSelectedChildren = category?.catList?.some(
            (subcat) =>
              (updatedSubcategories = updatedSubcategories.includes(subcat?.id))
          );
          if (!remainingSelectedChildren) {
            const parentIndex = updatedSubcategories?.indexOf(category?.id);
            if (parentIndex > -1) {
              updatedSubcategories = updatedSubcategories?.splice(
                parentIndex,
                1
              );
            }
          }
        }
      });
    }

    let selectedm = selectedMainCategories ? selectedMainCategories : [];
    const isAllUnchecked = allSubcategoryIds?.every(
      (id) => !updatedSubcategories.includes(id)
    );

    const isAnySelected = updatedSubcategories?.some((id) =>
      allSubcategoryIds.includes(id)
    );

    if (isAllUnchecked) {
      selectedm = selectedm?.filter((id) => id !== mainCategoryId);
    } else if (isAnySelected && !selectedm?.includes(mainCategoryId)) {
      selectedm = isSignleSelect
        ? [mainCategoryId]
        : [...selectedm, mainCategoryId];
    }
    setSelectedMainCategories(selectedm, updatedSubcategories);
    // setSelectedSubcategories(updatedSubcategories);
  };

  // Function to clear the selected main categories and subcategories.
  const handleClearSelection = () => {
    setSelectedMainCategories([], []);
    setSelectedToggle([]);
    // setSelectedSubcategories([]);
  };

  // Function to truncate the text if it exceeds a specified length.
  const truncateText = (text, maxLength) =>
    text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  return (
    <Select
      placeholder={placeholder ? placeholder : 'Categories'}
      multiselect
      value={selectedMainCategories}
      onVisibleChange={handleChangeVisible}
      visible={visible}
      onChange={selectedMainCategories}
    >
      <Select.Trigger
        className="intergalactic-multiselect intergalactic-multiselect-budget"
        tag={FilterTrigger}
      >
        {selectedMainCategories?.length === 1 ? (
          <Box className="d-flex justify-space-between">
            <Box className="d-flex align-center gap-5">
              <span className="branches-wrap">Category :</span>
              <Tooltip
                title={
                  categoryData?.find(
                    (category) => category?.value === selectedMainCategories[0]
                  )?.label || ''
                }
                arrow
              >
                <span className="label-wrap">
                  {truncateText(
                    categoryData?.find(
                      (category) =>
                        category?.value === selectedMainCategories[0]
                    )?.label || '',
                    15
                  )}
                </span>
              </Tooltip>
            </Box>
            <Box className="d-flex align-center gap-5">
              <ExpandMoreOutlinedIcon
                className="down-arrow-wrap"
                // fontSize="small"
              />
              <ClearIcon
                // fontSize="14px"
                onClick={handleClearSelection}
                className="clear-icon-wrap"
              />
            </Box>
          </Box>
        ) : selectedMainCategories && selectedMainCategories?.length > 1 ? (
          <Box className="multiple-category-wrap">
            <span className="categories-wrap">Categories</span>
            <span className="selected-count">
              {selectedMainCategories?.length}
            </span>
            <span className="selected-wrap">selected</span>
            <ExpandMoreOutlinedIcon
              className="down-arrow-wrap"
              // fontSize="small"
            />
            <ClearIcon
              // fontSize="14px"
              onClick={handleClearSelection}
              className="clear-icon-wrap"
            />
          </Box>
        ) : (
          'Select Categories'
        )}
      </Select.Trigger>

      <Select.Popper
        className="select-input-wrap"
        aria-label="Options with search"
      >
        {loading && (
          <Typography m="10px 8px" variant="body2">
            Loading...
          </Typography>
        )}
        {!loading && (
          <>
            <Select.List
              id="search-list"
              className="all-category-wrap all-category-wrap-budget"
            >
              {dsrCatData?.map((paymentType) => (
                <Box key={paymentType?.id}>
                  {!isCreate || isCreate ? (
                    <>
                      {/* (paymentType?.payment_type_status &&
                    paymentType?.payment_type_status !== 'inactive') || */}
                      <Box className="d-flex align-center pb8">
                        {/* Use MUI Checkbox here */}
                        <Checkbox
                          checked={
                            selectedMainCategories &&
                            selectedMainCategories?.length > 0 &&
                            selectedMainCategories?.includes(paymentType?.id)
                          }
                          onChange={() =>
                            handleMainCategoryToggle(paymentType?.id)
                          }
                        />
                        <Typography className="main-category-wrap" variant="h6">
                          {/* Truncate the title if it exceeds 15 characters */}
                          <span className="category-name text-ellipsis-line">
                            {paymentType?.payment_type_title?.length > 20
                              ? `${paymentType?.payment_type_title?.slice(
                                  0,
                                  15
                                )}...`
                              : paymentType?.payment_type_title}
                          </span>

                          {/* {paymentType?.payment_type_status &&
                          paymentType?.payment_type_status === 'inactive' ? (
                            <span className="failed Inter12 Inactive-axis ml4">
                              In-Active
                            </span>
                          ) : (
                            ''
                          )} */}

                          <Typography
                            className={`category-toggle ${selectedToggle?.includes(paymentType?.id) ? 'checked-toggle' : 'unchecked-toggle'}`}
                          >
                            <Tooltip
                              title="Separate"
                              arrow
                              placement="left"
                              classes={{
                                tooltip: 'tooltip-budget',
                              }}
                            >
                              <LightDarkSwitch
                                checked={selectedToggle?.includes(
                                  paymentType?.id
                                )}
                                onChange={() =>
                                  toggleCategoryExpand(paymentType?.id)
                                }
                              />
                            </Tooltip>
                          </Typography>
                        </Typography>
                      </Box>
                      {paymentType?.catList?.length > 0 && (
                        <ul>
                          {paymentType?.catList?.map((category) => (
                            <li key={category?.id}>
                              {isCreate || !isCreate ? (
                                <>
                                  {/* (category?.payment_type_category_status &&
                                  category?.payment_type_category_status !==
                                  'inactive') || */}
                                  <Box className="d-flex ml16 align-center pb8">
                                    <Checkbox
                                      checked={
                                        selectedSubcategories &&
                                        selectedSubcategories?.includes(
                                          category?.id
                                        )
                                      }
                                      onChange={() =>
                                        handleSubcategoryToggle(
                                          category?.id,
                                          paymentType?.id
                                        )
                                      }
                                    />
                                    <Typography
                                      className="sub-category-wrap"
                                      variant="body2"
                                    >
                                      {/* Truncate title if it exceeds 15 characters */}
                                      <span>
                                        {category?.payment_type_category_title
                                          ?.length > 20
                                          ? `${category?.payment_type_category_title?.slice(
                                              0,
                                              15
                                            )}...`
                                          : category?.payment_type_category_title}
                                      </span>
                                      {!isCreate &&
                                      category?.payment_type_category_status &&
                                      category?.payment_type_category_status ===
                                        'inactive' ? (
                                        <span className="failed Inter12 Inactive-axis ml4">
                                          In-Active
                                        </span>
                                      ) : (
                                        ''
                                      )}
                                    </Typography>
                                  </Box>
                                  {category?.catList?.length > 0 && (
                                    <ul>
                                      {/* (category?.payment_type_category_status &&
                                      category?.payment_type_category_status !==
                                      'inactive') || */}
                                      {isCreate || !isCreate
                                        ? category?.catList?.map(
                                            (subCategory) => (
                                              <li key={subCategory?.id}>
                                                <Box className="d-flex ml30 align-center pb8">
                                                  <Checkbox
                                                    checked={
                                                      selectedSubcategories &&
                                                      selectedSubcategories?.includes(
                                                        subCategory?.id
                                                      )
                                                    }
                                                    onChange={() =>
                                                      handleSubcategoryToggle(
                                                        subCategory?.id,
                                                        paymentType?.id
                                                      )
                                                    }
                                                  />
                                                  <Typography
                                                    className="sub-sub-category-wrap"
                                                    variant="body2"
                                                  >
                                                    {/* Truncate sub-category if it exceeds 15 characters */}
                                                    {subCategory
                                                      ?.first_field_value
                                                      ?.length > 20
                                                      ? `${subCategory?.first_field_value?.slice(
                                                          0,
                                                          15
                                                        )}...`
                                                      : subCategory?.first_field_value}
                                                  </Typography>
                                                </Box>
                                              </li>
                                            )
                                          )
                                        : ''}
                                    </ul>
                                  )}
                                </>
                              ) : (
                                ''
                              )}
                            </li>
                          ))}
                        </ul>
                      )}
                    </>
                  ) : (
                    ''
                  )}
                </Box>
              ))}
              {!dsrCatData?.length && (
                <Select.OptionHint>No categories found</Select.OptionHint>
              )}
            </Select.List>
            {/* <Box m="8px">
              <Button
                className="apply-btn"
                use="primary"
                w="100%"
                onClick={() => setVisible(false)}>
                Apply
              </Button>
            </Box> */}
          </>
        )}
      </Select.Popper>
    </Select>
  );
}
