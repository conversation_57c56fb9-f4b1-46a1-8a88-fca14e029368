'use client';

import React, { useState } from 'react';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
// import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/button';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import {
  Box,
  Typography,
  InputAdornment,
  IconButton,
  TextField,
} from '@mui/material';
import { Formik, Form } from 'formik';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { fetchFromStorage, removeFromStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import { ORG_URLS } from '@/helper/constants/urls';
import {
  EyeCloseIcon,
  EyeShowIcon,
  ForgotLeftVector,
  ForgotRightVector,
} from '@/helper/common/images';
import './resetpassword.scss';

const ResetPassword = () => {
  const router = useRouter();
  // const { authState, setAuthState } = useContext(AuthContext);
  const [showEnterPassword, setShowCurrentPassword] = useState(false);
  const [showrepeatPassword, setShowrepeatPassword] = useState(false);

  // const [loader, setLoader] = useState(false);
  return (
    <>
      <Box className="reset-wrap">
        <Box className="reset-container">
          <Typography variant="h3" className="main-heading heading-text">
            Update your password
          </Typography>
          <Typography variant="h6" className="forgot-content sub-heading-text">
            Please enter a new password and confirm password. Make sure you
            remember it always.
          </Typography>
          <Box className="">
            <Formik
              initialValues={{
                new_password: '',
                repeat_password: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                new_password: Yup.string()
                  .trim()
                  .required('New password is required')
                  // .min(8, 'Password length must be minimum 8 character'),
                  .min(8, 'Password must be at least 8 characters')
                  .matches(
                    /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]+$/,
                    'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character'
                  ),
                repeat_password: Yup.string()
                  .trim()
                  .required('Confirm new password is required')
                  .oneOf(
                    [Yup.ref('new_password'), null],
                    'The passwords you entered do not match'
                  ),
              })}
              onSubmit={async (requestData) => {
                let sendData;
                const user = fetchFromStorage(identifiers?.USER_ID);
                sendData = {
                  userId: user?.id ? user?.id : '',
                  password: requestData?.new_password,
                  // confirm_password: requestData?.repeat_password
                };

                const url = ORG_URLS.RESET_AUTH_PASSWORD;
                const method = 'put';
                try {
                  // setLoader(true);
                  const { status, data } = await axiosInstance[method](
                    url,
                    sendData
                  );

                  if (status === 200) {
                    if (data.status) {
                      setApiMessage('success', data?.message);
                      removeFromStorage(identifiers?.EMAIL);
                      removeFromStorage(identifiers?.USER_ID);
                      router.push('/org/login');
                    } else {
                      setApiMessage('error', data?.message);
                    }
                    // setLoader(false);
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                  // setLoader(false);
                }
              }}
            >
              {({
                errors,
                touched,
                handleBlur,
                values,
                handleSubmit,
                handleChange,
                // dirty,
                // isValid,
              }) => (
                <Form className="reset-password-form" onSubmit={handleSubmit}>
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Enter new password*
                    </Typography>
                    <TextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="new_password"
                      name="new_password"
                      value={values.new_password}
                      // label='Enter new password'
                      type={showEnterPassword ? 'text' : 'password'}
                      variant="standard"
                      className={
                        touched.new_password && errors.new_password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      error={Boolean(
                        touched.new_password && errors.new_password
                      )}
                      helperText={touched.new_password && errors.new_password}
                      onBlur={handleBlur}
                      placeholder="New password"
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton
                              disableRipple
                              onClick={() =>
                                setShowCurrentPassword(!showEnterPassword)
                              }
                            >
                              <Box className="eye-wrap">
                                {showEnterPassword ? (
                                  <EyeCloseIcon />
                                ) : (
                                  <EyeShowIcon />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </Box>

                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Confirm new password*
                    </Typography>
                    <TextField
                      InputLabelProps={{
                        shrink: true,
                      }}
                      id="repeat_password"
                      name="repeat_password"
                      value={values.repeat_password}
                      // label='Confirm new password'
                      placeholder="Confirm new password"
                      type={showrepeatPassword ? 'text' : 'password'}
                      variant="standard"
                      className={
                        touched.repeat_password && errors.repeat_password
                          ? 'w100 password-textfield password-error'
                          : 'w100 password-textfield'
                      }
                      error={Boolean(
                        touched.repeat_password && errors.repeat_password
                      )}
                      helperText={
                        touched.repeat_password && errors.repeat_password
                      }
                      onBlur={handleBlur}
                      onChange={handleChange}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end" className="eye-icon">
                            <IconButton
                              disableRipple
                              onClick={() =>
                                setShowrepeatPassword(!showrepeatPassword)
                              }
                            >
                              <Box className="eye-wrap">
                                {showrepeatPassword ? (
                                  <EyeCloseIcon />
                                ) : (
                                  <EyeShowIcon />
                                )}
                              </Box>
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                    <Box textAlign="center" className="signin-btn-wrap pt24">
                      <CustomButton
                        fullWidth
                        type="submit"
                        variant="contained"
                        background="#39596e"
                        className="signin-btn"
                        fontWeight="600"
                        colorhover="#FFFFFF"
                        isLogin={true}
                        backgroundhover="#39596e"
                        title="Change my password"
                      />
                    </Box>
                  </Box>
                </Form>
              )}
            </Formik>
          </Box>
        </Box>
        <Box className="left-vector">
          <ForgotLeftVector className="left-vector" />
        </Box>
        <Box className="right-vector">
          <ForgotRightVector className="left-vector" />
        </Box>
      </Box>
    </>
  );
};

export default ResetPassword;
