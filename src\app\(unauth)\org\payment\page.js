'use client';

import React, { useEffect, useState } from 'react';
import * as Yup from 'yup';

import { Box, Typography } from '@mui/material';
import CustomButton from '@/components/UI/button';
import { useRouter, useSearchParams } from 'next/navigation';
import { Formik, Form } from 'formik';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { ORG_URLS } from '@/helper/constants/urls';
import { setApiMessage, stripePromise } from '@/helper/common/commonFunctions';
import { LogInLeftVector, LogInRightVector } from '@/helper/common/images';
import { jwtDecode } from 'jwt-decode';
// import CustomSelect from '@/components/UI/selectbox';
import axiosInstanceStripe from '@/helper/axios/axiosInstanceStripe';
import { Config } from '@/helper/context/config';
import qs from 'querystring';
import moment from 'moment';
import CustomTextField from '@/components/UI/CustomTextField';
import {
  CustomCardCvcElement,
  CustomCardExpiryElement,
  CustomCardNumberElement,
} from '@/components/UI/CustomCardsElements/CardElementField';
import { Elements } from '@stripe/react-stripe-js';
import './payment.scss';
import '../login/login.scss';

const stripeAuthHeader = {
  'Content-Type': 'application/x-www-form-urlencoded',
  Authorization: `Bearer ${Config?.SecretKeyStripe}`,
};

const PublicPayment = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const decodedToken = token && jwtDecode(token);
  const [ProviderID, setProviderID] = useState();

  const getPaymetProviderDetails = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        ORG_URLS?.GET_ALL_PUBLIC_PAYMENT_PROVIDER + `?status=active`
      );
      if (status === 200) {
        const options = data?.data?.map((provider) => ({
          label: provider.provider_name,
          value: provider.id,
        }));
        options?.[0].value && setProviderID(options?.[0].value);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getPaymetProviderDetails();
  }, []);

  return (
    <Box className="login-page-wrap payment-publc-link-page">
      <Box className="login-wrap">
        <Box className="login-page login-pages">
          <Box className="login-block login-blocks">
            <Typography variant="h2" className="main-heading heading-text pb16">
              Public Payment
            </Typography>
            {/* <Box className="payment-details">
              <Typography className="payment-text fw600">
                Plan : <span className="fw400"> plan 1</span>
              </Typography>
              <Typography className="payment-text fw600">
                {planDetails?.subs_plan_duration === 'yearly'
                  ? 'Yearly Cost'
                  : 'Monthly Cost'}
                {' : '}
                <span className="fw400"> 4000</span>
              </Typography>
              {(decodedToken?.user_min || decodedToken?.user_min === 0) &&
                (decodedToken?.user_max || decodedToken?.user_max === 0) && (
                  <Typography className="payment-text fw600">
                    Employee's Limit:{' '}
                    <span className="fw400">
                      {' ' +
                        decodedToken?.user_min +
                        ' - ' +
                        decodedToken?.user_max}
                    </span>
                  </Typography>
                )}
            </Box> */}
            {/* <Box className="card-accordian-wrap">
              <Box className="d-flex card-wrap align-center">
                <Box className="details">
                  <Typography className="plan-name p16 fw600">
                    {decodedToken?.plan_name}
                  </Typography>
                  <Typography className="offer p14">
                    {decodedToken?.subs_plan_description}
                  </Typography>
                </Box>
              </Box>
              <Box className="pricing">
                <Typography className="amount p16 fw600">
                  {decodedToken?.plan_cost?.toFixed(2)}
                  <span className="year-wrap p14 fw400">
                    /
                    {decodedToken?.subs_plan_duration === 'Monthly'
                      ? 'Month'
                      : 'Year'}
                  </span>
                </Typography>
                <Typography className="duration p14 fw400">
                  {decodedToken?.user_min} - {decodedToken?.user_max} Employees{' '}
                  {decodedToken?.subs_is_free_trial ? (
                    <span className="year-wrap">/Free Trial</span>
                  ) : (
                    ''
                  )}
                </Typography>
              </Box>
            </Box> */}
            <Box className={`card selected`}>
              <Box className="card-accordian-wrap">
                <Box className="d-flex card-wrap">
                  <Box className="details">
                    <Typography className="plan-name sub-content-text">
                      {decodedToken?.plan_name}
                    </Typography>
                    <Typography className="offer content-text">
                      {decodedToken?.subs_plan_description}
                    </Typography>
                  </Box>
                </Box>
                <Box className="pricing">
                  <Typography className="sub-content-text">
                    {decodedToken?.plan_cost?.toFixed(2)}
                    <span className="sub-content-text">
                      /
                      {decodedToken?.subs_plan_duration === 'Weekly'
                        ? 'Weekly'
                        : decodedToken?.subs_plan_duration === 'Monthly'
                          ? 'Month'
                          : 'Year'}
                    </span>
                  </Typography>
                  <Typography className="duration p14 fw400">
                    <span className="year-wrap">
                      {decodedToken?.user_min} - {decodedToken?.user_max}{' '}
                      Employees
                    </span>
                    {/* Employees{' '} */}
                    {decodedToken?.subs_is_free_trial ? (
                      <span className="year-wrap">/Free Trial</span>
                    ) : (
                      ''
                    )}
                  </Typography>
                </Box>
              </Box>

              {/* Display full details when the plan is selected */}
              {/* <Box className="plan-offers-wrap">
                <Collapse
                  in={selectedPlan?.id === subscription?.id}
                  timeout="auto"
                  unmountOnExit
                >
                  <Box className="details-section">
                    {planFeatures?.map((feature, index) => (
                      <Box
                        className="detail-item d-flex align-center"
                        key={index}
                      >
                        <CheckCircleOutlineIcon className="check-icon" />
                        <Typography
                          component="p"
                          className="detail-text-wrap title-text "
                        >
                          {feature.description}
                        </Typography>
                      </Box>
                    ))}
                  </Box>
                </Collapse>
              </Box> */}
            </Box>
            {/* <Box className="provider-sec d-flex justify-space-between align-center">
              <Typography className="p16 fw600">Payment Provider</Typography>
              <CustomSelect
                name="userCard"
                placeholder="Payment provider"
                className="selected-wrap card-select"
                options={paymentProviderOptions}
                value={ProviderID ? ProviderID : ''}
                onChange={(e) => {
                  setProviderID(e?.target?.value);
                }}
              />
            </Box> */}
            <Formik
              initialValues={{
                cardHolderName: '',
                cardNumber: '',
                expDate: '',
                cvv: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                cardHolderName: Yup.string().required(
                  'Card Holder Name is required'
                ),
                cardNumber: Yup.string()
                  .required('Card Number is required')
                  .matches(/^[0-9]{16}$/, 'Card Number must be 16 digits'),
                expDate: Yup.string()
                  .required('Expiration Date is required')
                  .matches(
                    /^(0[1-9]|1[0-2])\/\d{2}$/,
                    'Exp Date must be in MM/YY format'
                  ),
                cvv: Yup.string()
                  .required('CVV is required')
                  .matches(/^[0-9]{3,4}$/, 'CVV must be 3 or 4 digits'),
              })}
              onSubmit={async (requestData) => {
                //   let sendData;
                //   sendData = {
                //     cardHolderName: requestData?.cardHolderName,
                //     cardNumber: requestData?.cardNumber,
                //     expDate: requestData?.expDate,
                //     cvv: requestData?.cvv
                //   };

                //   try {
                //     setLoader(true);
                //     const { status, data } = await axiosInstance.post(
                //       ORG_URLS.PUBLIC_LINK_PAYMENT,
                //       sendData
                //     );

                //     if (status === 200) {
                //       if (data?.status) {
                //         setApiMessage('success', data?.message);
                //         router.push('/org/payment-success');
                //       } else {
                //         setApiMessage('error', data?.message);
                //       }
                //       setLoader(false);
                //     }
                //   } catch (error) {
                //     setApiMessage('error', error?.response?.data?.message);
                //     setLoader(false);
                //   }
                // }

                let payload = qs.stringify({
                  'card[number]': requestData?.cardNumber,

                  'card[exp_month]': parseInt(
                    moment(requestData?.expDate, 'MM/YYYY').format('MM')
                  ),
                  'card[exp_year]': parseInt(
                    moment(
                      new Date().getFullYear().toString().slice(0, 2) +
                        requestData?.expDate?.split('/')[1]
                    ).format('YYYY')
                  ),
                  'card[cvc]': requestData?.cvv,
                });
                try {
                  const { data, status } = await axiosInstanceStripe.post(
                    Config?.StripeUrl,
                    payload,
                    {
                      headers: stripeAuthHeader,
                    }
                  );
                  if (status === 200) {
                    let payload = {
                      token: token,
                      first_name: requestData?.cardHolderName,
                      card_token: data?.id,
                      card_exp: requestData?.expDate,
                      fingerprint: data?.card?.fingerprint,
                      provider_id: ProviderID,
                      email: decodedToken?.email,
                    };
                    try {
                      const { data, status } = await axiosInstance.post(
                        ORG_URLS.PUBLIC_LINK_PAYMENT,
                        payload
                      );
                      if (status === 200) {
                        if (data?.status) {
                          router.push('/org/payment-success');
                          setApiMessage('success', data?.message);
                        } else {
                          setApiMessage('error', data?.message);
                        }
                      }
                    } catch (error) {
                      setApiMessage('error', error?.response?.data?.message);
                    }
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                }
              }}
            >
              {({
                errors,
                touched,
                // handleBlur,
                values,
                handleSubmit,
                handleChange,
                setFieldValue,
                // dirty,
                // isValid,
              }) => (
                <Form className="login-form-wrap mt16" onSubmit={handleSubmit}>
                  {/* <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Card Holder Name*
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="cardHolderName"
                      name="cardHolderName"
                      placeholder="Card Holder Name"
                      value={values?.cardHolderName}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={
                        touched?.cardHolderName &&
                        Boolean(errors?.cardHolderName)
                      }
                      helperText={
                        touched?.cardHolderName && errors?.cardHolderName
                      }
                      className="w100"
                      variant="standard"
                    />
                  </Box>
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Card Number*
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="cardNumber"
                      name="cardNumber"
                      placeholder="Card Number"
                      value={values?.cardNumber}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched?.cardNumber && Boolean(errors?.cardNumber)}
                      helperText={touched?.cardNumber && errors?.cardNumber}
                      className="w100"
                      variant="standard"
                    />
                  </Box>
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      Exp Date (MM/YY)*
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="expDate"
                      name="expDate"
                      placeholder="MM/YY"
                      className="w100"
                      variant="standard"
                      value={values?.expDate}
                      onChange={(e) => {
                        const formattedValue = formatExpDate(e.target.value);
                        setFieldValue('expDate', formattedValue);
                      }}
                      onBlur={handleBlur}
                      error={touched?.expDate && Boolean(errors?.expDate)}
                      helperText={touched?.expDate && errors?.expDate}
                    />
                  </Box>
                  <Box className="input-wrap">
                    <Typography variant="h6" className="un-auth-label-wrap">
                      CVV
                    </Typography>
                    <TextField
                      fullWidth
                      InputLabelProps={{ shrink: true }}
                      id="cvv"
                      name="cvv"
                      type="password"
                      placeholder="Enter CVV"
                      className="w100"
                      variant="standard"
                      value={values?.cvv}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={touched?.cvv && Boolean(errors?.cvv)}
                      helperText={touched?.cvv && errors?.cvv}
                    />
                  </Box> */}
                  <Box className="card-inputs">
                    <Box className="card-details-row">
                      <Box className="cardholder-field-container">
                        <CustomTextField
                          fullWidth
                          required
                          name="cardHolderName"
                          label="Card Holder Name"
                          placeholder="Card Holder Name"
                          value={values?.cardHolderName}
                          onChange={handleChange}
                          error={
                            touched?.cardHolderName &&
                            Boolean(errors?.cardHolderName)
                          }
                          helperText={
                            touched?.cardHolderName && errors?.cardHolderName
                          }
                        />
                      </Box>
                      <Box className="cardnumber-field-container">
                        <CustomCardNumberElement
                          label="Card Number"
                          required
                          placeholder="1234 1234 1234 1234"
                          error={
                            Boolean(values.cardNumberError) ||
                            (touched.cardNumberComplete &&
                              !values.cardNumberComplete)
                          }
                          helperText={
                            values.cardNumberError ||
                            (touched.cardNumberComplete &&
                              errors.cardNumberComplete)
                          }
                          onChange={(event) => {
                            setFieldValue('cardNumberComplete', event.complete);
                            setFieldValue(
                              'cardNumberError',
                              event.error ? event.error.message : ''
                            );
                          }}
                        />
                      </Box>
                    </Box>

                    <Box className="card-details-row">
                      <Box className="card-expiry-container">
                        <CustomCardExpiryElement
                          label="Exp Date (MM/YY)"
                          required
                          placeholder="MM/YY"
                          error={
                            Boolean(values.cardExpiryError) ||
                            (touched.cardExpiryComplete &&
                              !values.cardExpiryComplete)
                          }
                          helperText={
                            values.cardExpiryError ||
                            (touched.cardExpiryComplete &&
                              errors.cardExpiryComplete)
                          }
                          onChange={(event) => {
                            setFieldValue('cardExpiryComplete', event.complete);
                            setFieldValue(
                              'cardExpiryError',
                              event.error ? event.error.message : ''
                            );
                          }}
                        />
                      </Box>

                      <Box className="card-cvc-container">
                        <CustomCardCvcElement
                          label="CVV"
                          required
                          placeholder="CVV"
                          error={
                            Boolean(values.cardCvcError) ||
                            (touched.cardCvcComplete && !values.cardCvcComplete)
                          }
                          helperText={
                            values.cardCvcError ||
                            (touched.cardCvcComplete && errors.cardCvcComplete)
                          }
                          onChange={(event) => {
                            setFieldValue('cardCvcComplete', event.complete);
                            setFieldValue(
                              'cardCvcError',
                              event.error ? event.error.message : ''
                            );
                          }}
                        />
                      </Box>
                    </Box>
                  </Box>
                  <Box className="log-in-btn-wrap mt32" textAlign="center">
                    <CustomButton
                      fullWidth
                      className="log-in-btn"
                      type="submit"
                      fontWeight="600"
                      variant="contained"
                      background="#39596e"
                      backgroundhover="#39596e"
                      colorhover="#FFFFFF"
                      title="Pay Now"
                      isLogin={true}
                    />
                  </Box>
                </Form>
              )}
            </Formik>
          </Box>
        </Box>
      </Box>
      <Box className="left-vector">
        <LogInLeftVector />
      </Box>
      <Box className="right-vector">
        <LogInRightVector />
      </Box>
    </Box>
  );
};

export default function Login(props) {
  // // Define the appearance object with flat theme
  // const appearance = {
  //   theme: 'flat',
  // };

  // // Create options object with appearance
  // const elementsOptions = {
  //   appearance,
  // };

  return (
    <Elements
      stripe={stripePromise}
      // options={elementsOptions}
    >
      <PublicPayment {...props} />
    </Elements>
  );
}
