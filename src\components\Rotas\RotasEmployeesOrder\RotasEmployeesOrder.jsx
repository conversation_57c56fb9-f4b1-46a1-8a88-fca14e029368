'use client';
import React, { useEffect, useState } from 'react';
import { Box, Typography } from '@mui/material';
import ArrowBackOutlinedIcon from '@mui/icons-material/ArrowBackOutlined';
import ReorderOutlinedIcon from '@mui/icons-material/ReorderOutlined';
import CustomButton from '@/components/UI/CustomButton';
import { useRouter } from 'next/navigation';
import axiosInstanceOrg from '@/helper/axios/axiosInstanceOrg';
import { ROTA_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import '../rotas.scss';
import './rotasEmployeesOrder.scss';

const RotasEmployeesOrder = () => {
  const navigate = useRouter();

  const [staffListData, setStaffListData] = useState([]);
  const [isLoader, setIsLoader] = useState(true);

  // Get staff list
  const getStaffList = async (isReorder = true) => {
    !isReorder && setIsLoader(true);
    try {
      const { status, data } = await axiosInstanceOrg.get(
        ROTA_URLS?.SHIFT_STAFF_LIST + `?isAdmin=false&isRotaList=true`
      );

      if (status === 200) {
        setIsLoader(false);
        setStaffListData(data?.userList);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setStaffListData([]);
      setIsLoader(false);
    }
  };

  // Handle drag end
  const handleDragEnd = async (result) => {
    if (!result.destination) return;

    const items = Array.from(staffListData);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const sendData = {
      userId: Number(result.draggableId),
      sortOrder: result.destination.index,
    };

    try {
      const { status, data } = await axiosInstanceOrg.post(
        ROTA_URLS?.UPDATE_USER_ORDER,
        sendData
      );

      if (status === 200) {
        setApiMessage('success', data?.message);
        setStaffListData(items);
        getStaffList(false);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getStaffList();
  }, []);

  return (
    <Box className="rota-order-shifts-container">
      <Box className="dashboard-filter-section">
        <Box className="rota-order-filter-heading">
          <Typography variant="h5" className="rota-order-filter-title">
            Order Employees
          </Typography>

          <CustomButton
            variant="outlined"
            startIcon={<ArrowBackOutlinedIcon />}
            title="Back to Rota"
            onClick={() => navigate.push('/rotas')}
          />
        </Box>
        <Box className="rota-order-info-section">
          <Typography variant="body1" className="rota-order-info-text">
            Drag employees up and down to change the order in which they display
            on your rotas
          </Typography>
        </Box>
      </Box>
      <Box className="rota-order-shifts-table">
        <Box className="table-container">
          {isLoader ? (
            <ContentLoader />
          ) : !isLoader && staffListData && staffListData?.length === 0 ? (
            <NoDataView
              title="No Employee Data Found"
              description="There is no Employee Data at the moment."
            />
          ) : (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="employees">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {/* Header Row */}
                    <div className="employee-header">
                      <Box className="header-cell">Name</Box>
                      <Box className="header-cell">Role</Box>
                      <Box className="header-cell">Location</Box>
                      <Box className="header-cell">Department</Box>
                    </div>
                    {staffListData.map((employee) => (
                      <Draggable
                        key={employee.id}
                        draggableId={employee.id.toString()}
                        index={employee.id}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="employee-row"
                          >
                            <Box className="d-flex align-center gap-sm">
                              <ReorderOutlinedIcon fontSize="small" />
                              {employee?.user_full_name}
                            </Box>
                            <Box>{employee?.user_roles?.role_name || '-'}</Box>
                            <Box>{employee?.branch?.branch_name || '-'}</Box>
                            <Box>
                              {employee?.department?.department_name || '-'}
                            </Box>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default RotasEmployeesOrder;
