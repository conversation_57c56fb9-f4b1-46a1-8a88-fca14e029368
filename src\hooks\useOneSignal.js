import { useEffect, useRef, useContext } from 'react';
import OneSignal from 'react-onesignal';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import AuthContext from '@/helper/authcontext';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';

const useOneSignalToken = () => {
  let isDeviceId = fetchFromStorage(identifiers?.DEVICEID);
  const intervalRef = useRef(null);
  const { authState } = useContext(AuthContext);
  const fetchAndUpdateToken = async () => {
    try {
      const playerId = JSON.parse(
        JSON.stringify(OneSignal?.User?.PushSubscription)
      )?.id;

      if (playerId) {
        const sendData = {
          webAppToken: playerId,
        };
        // console.error(' OneSignal Player ID:', playerId);
        // Update your backend
        await axiosInstance.post(URLS?.UPDATE_ONESIGNAL_TOKEN, sendData);

        // Stop polling
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      } else {
        console.error('Still waiting for OneSignal Player ID...');
      }
    } catch (error) {
      console.error('Error fetching OneSignal token:', error);
    }
  };

  useEffect(() => {
    // Only start polling if token is empty/null or doesn't match device ID
    if (
      authState?.webAppToken === '' ||
      authState?.webAppToken === null ||
      authState?.webAppToken !== isDeviceId
    ) {
      intervalRef.current = setInterval(fetchAndUpdateToken, 3000);
    } else {
      // Clear interval if conditions are not met
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    // Cleanup on unmount or when dependencies change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [authState?.webAppToken, isDeviceId]); // Add dependencies to re-run effect when these values change
};

export default useOneSignalToken;
