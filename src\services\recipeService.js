import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { RECIPE_URLS } from '@/helper/constants/urls';

// Utility function to determine if current route is public or private
// Public route: pathname contains '/recipe' but not '/recipes' (e.g., /recipe, /recipe/123)
// Private route: pathname contains '/recipes' (e.g., /recipes, /recipes/list)
export const isPublicRoute = (pathname) => {
  return pathname?.includes('/recipe') && !pathname?.includes('/recipes');
};

// Utility function to choose appropriate API call based on route type
// This function helps maintain consistency across components for public/private API logic
export const getRecipeListByRouteType = async (
  pathname,
  search,
  page,
  Rpp,
  filter
) => {
  const isPublic = isPublicRoute(pathname);

  if (isPublic) {
    // Call public API for public routes (e.g., /recipe)
    return await getPublicRecipeList(
      search || '',
      page || 1,
      Rpp || 10,
      filter
    );
  } else {
    // Call private API for private routes (e.g., /recipes) - default behavior
    return await getRecipeList(search || '', page || 1, Rpp || 10, filter, {
      key: 'updated_at',
      value: 'DESC',
    });
  }
};

// Utility function to conditionally fetch data based on route type
// For public routes, it returns empty arrays to avoid API calls that require authentication
// For private routes, it calls the provided API function
export const conditionalApiCall = async (
  pathname,
  apiFunction,
  fallbackValue = []
) => {
  const isPublic = isPublicRoute(pathname);

  if (isPublic) {
    // Return fallback value for public routes to avoid authentication issues
    return fallbackValue;
  } else {
    // Call the API function for private routes
    try {
      return await apiFunction();
    } catch (error) {
      console.error('API call failed:', error);
      return fallbackValue;
    }
  }
};

// Utility function to get CSS class based on route type
// Helps maintain consistent styling between public and private pages
export const getRouteBasedClassName = (
  pathname,
  baseClassName,
  publicSuffix = '--public'
) => {
  const isPublic = isPublicRoute(pathname);
  return isPublic ? `${baseClassName}${publicSuffix}` : baseClassName;
};

// Utility function to check if certain features should be available based on route type
// Returns an object with feature availability flags
export const getRouteFeatures = (pathname) => {
  const isPublic = isPublicRoute(pathname);

  return {
    isPublicRoute: isPublic,
    showRecipeMenu: !isPublic,
    showBookmarkFunctionality: !isPublic,
    showDraftPublishedStatus: !isPublic,
    showEditIcons: !isPublic,
    allowEdit: !isPublic,
    allowDelete: !isPublic,
    allowDuplicate: !isPublic,
    showPrivateFilters: !isPublic,
  };
};

export const getIngredientList = async (search, page, filter, Rpp, Sort) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter.status);
    params.append('type', 'ingredient');
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching ingredient list:', error);
    throw error;
  }
};

export const createIngredientCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient category');
  } catch (error) {
    throw error;
  }
};

export const updateIngredientCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient category');
  } catch (error) {
    throw error;
  }
};

export const deleteIngredientCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient category');
  } catch (error) {
    throw error;
  }
};

export const getRecipeCategoryList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  const categoryFilter =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    if (filter?.status) params.append('status', filter?.status);
    params.append('type', 'recipe');
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (categoryFilter !== '') {
      params.append('isSystemCategory', categoryFilter.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_CATEGORIES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        categories: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      categories: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching recipe categories:', error);
    throw error;
  }
};

export const createRecipeCategory = async (categoryData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_CATEGORIES,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create recipe category');
  } catch (error) {
    throw error;
  }
};

export const updateRecipeCategory = async (
  categoryId,
  categoryData,
  config
) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_CATEGORY}/${categoryId}`,
      categoryData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe category');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipeCategory = async (categoryId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_CATEGORY}/${categoryId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe category');
  } catch (error) {
    throw error;
  }
};

export const getAllergenList = async (search, page, filter, Rpp, Sort) => {
  const allergenType =
    filter?.type === 'default' ? true : filter?.type === 'custom' ? false : '';

  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'allergen');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }
    if (allergenType !== '') {
      params.append('isSystemAttribute', allergenType.toString());
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        allergens: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      allergens: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching allergen list:', error);
    throw error;
  }
};

export const createAllergen = async (allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_ALLERGEN,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create allergen');
  } catch (error) {
    throw error;
  }
};

export const updateAllergen = async (allergenId, allergenData, config) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_ALLERGEN}/${allergenId}`,
      allergenData,
      config
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update allergen');
  } catch (error) {
    throw error;
  }
};

export const deleteAllergen = async (allergenId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_ALLERGEN}/${allergenId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete allergen');
  } catch (error) {
    throw error;
  }
};

export const getIngredientItemsList = async (
  search,
  page,
  filter,
  Rpp,
  Sort
) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    // Basic parameters - always append page, handle empty values properly
    if (page) params.append('page', page.toString());
    if (Rpp) params.append('size', Rpp.toString());
    if (search) params.append('search', search);

    // Filter parameters - only append if they have values
    if (filter?.status) params.append('ingredient_status', filter?.status);
    if (filter?.category) params.append('category', filter?.category);
    if (filter?.allergen) params.append('allergy', filter?.allergen);
    if (filter?.dietary) params.append('dietary', filter?.dietary);
    if (filter?.organizationId)
      params.append('organization_id', filter?.organizationId);

    // Sort parameters - only append if both key and value exist
    if (Sort?.key && Sort?.value) {
      params.append('sort_by', Sort?.key);
      params.append('sort_order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_INGREDIENTS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        ingredients: data?.data || [],
        totalCount: data?.count || 0,
        import_sample_file: data?.import_sample_file || '',
      };
    }

    return {
      ingredients: [],
      totalCount: 0,
      import_sample_file: '',
    };
  } catch (error) {
    console.error('Error fetching ingredient items list:', error);
    throw error;
  }
};

// API function to get single ingredient by ID
export const getIngredientById = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_INGREDIENT_BY_ID}/${ingredientId}`
    );

    if (status === 200) {
      return {
        ingredient: data?.data || null,
      };
    }
    return {
      ingredient: null,
    };
  } catch (error) {
    throw error;
  }
};

export const createIngredient = async (ingredientData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_INGREDIENT,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to create ingredient');
  } catch (error) {
    throw error;
  }
};

export const updateIngredient = async (ingredientId, ingredientData) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_INGREDIENT}/${ingredientId}`,
      ingredientData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to delete ingredient by ID
export const deleteIngredient = async (ingredientId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_INGREDIENT}/${ingredientId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete ingredient');
  } catch (error) {
    throw error;
  }
};

// API function to export ingredient items
export const exportIngredient = async (format) => {
  try {
    // Build query string from filter

    const response = await axiosInstance.get(
      `${RECIPE_URLS.EXPORT_INGREDIENT}/${format}`,
      { responseType: 'blob' }
    );
    return response;
  } catch (error) {
    throw error;
  }
};

// API function to import ingredient categories
export const importIngredientCategory = async (file) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.IMPORT_INGREDIENT,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to import ingredient categories');
  } catch (error) {
    throw error;
  }
};

// API function to get nutrition data
export const getNutritionList = async () => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    params.append('page', '1');
    params.append('limit', '');
    params.append('search', '');
    params.append('type', 'nutrition');
    params.append('status', 'active');
    params.append('sort_by', 'attribute_title');
    params.append('sort_order', 'ASC');

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        nutrition: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      nutrition: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

// API function to get dietary data
export const getDietaryList = async (search, page, filter, Rpp, Sort) => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    if (page) params.append('page', page.toString());
    if (Rpp) params.append('limit', Rpp.toString());
    if (search) params.append('search', search);
    params.append('type', 'dietary');
    if (filter?.status) params.append('status', filter?.status);
    if (Sort?.key && Sort?.value) {
      params.append('sort', Sort?.key);
      params.append('order', Sort?.value);
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_ALLERGENS}?${params.toString()}`
    );

    if (status === 200) {
      return {
        dietary: data?.data || [],
        totalCount: data?.count || 0,
      };
    }

    return {
      dietary: [],
      totalCount: 0,
    };
  } catch (error) {
    console.error('Error fetching dietary list:', error);
    throw error;
  }
};

// API function to get recipe measures data
export const getRecipeMeasuresList = async () => {
  try {
    // Build query parameters using URLSearchParams
    const params = new URLSearchParams();

    params.append('page', '1');
    params.append('limit', '');
    params.append('search', '');
    params.append('status', 'active');
    params.append('sort_by', 'unit_title');
    params.append('sort_order', 'asc');

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.RECIPE_MEASURES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        measures: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      measures: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    // params.append('page', page || 1);
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    // params.append('limit', Rpp || 10);

    if (search) params.append('search', search);

    // Filter parameters
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Recipe status filter
      if (filter.status) {
        params.append('recipe_status', filter.status);
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }

      // Bookmark filter
      if (filter.bookmarked !== undefined) {
        params.append('bookmark', filter.bookmarked === 1 ? true : false);
      }

      // Ownership filter
      if (filter.ownership) {
        params.append('ownership', filter.ownership);
      }
    }

    // Sort parameters
    // if (Sort) {
    //   params.append('sort_by', Sort.key || 'updated_at');
    //   params.append('sort_order', Sort.value || 'DESC');
    // } else {
    //   params.append('sort_by', 'updated_at');
    //   params.append('sort_order', 'DESC');
    // }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_ALL_RECIPES}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getRecipeSettings = async () => {
  try {
    const { status, data } = await axiosInstance.get(
      RECIPE_URLS.RECIPES_SETTINGS
    );

    if (status === 200) {
      return data;
    }
    return {
      data: null,
      message: 'No settings found',
    };
  } catch (error) {
    console.error('Error fetching recipe settings:', error);
    throw error;
  }
};

export const updateRecipeSettings = async (settingsData) => {
  try {
    const { status, data } = await axiosInstance.put(
      RECIPE_URLS.UPDATE_RECIPES_SETTINGS,
      settingsData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe settings');
  } catch (error) {
    console.error('Error updating recipe settings:', error);
    throw error;
  }
};

export const updateBookmark = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.UPDATE_BOOKMARK}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update bookmark');
  } catch (error) {
    throw error;
  }
};

export const duplicateRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.post(
      `${RECIPE_URLS.DUPLICATE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to duplicate recipe');
  } catch (error) {
    throw error;
  }
};

export const deleteRecipe = async (recipeId) => {
  try {
    const { status, data } = await axiosInstance.delete(
      `${RECIPE_URLS.DELETE_RECIPE}/${recipeId}`
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to delete recipe');
  } catch (error) {
    throw error;
  }
};

export const createRecipe = async (recipeData) => {
  try {
    const { status, data } = await axiosInstance.post(
      RECIPE_URLS.CREATE_RECIPES,
      recipeData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to add recipe');
  } catch (error) {
    throw error;
  }
};

export const updateRecipe = async (recipeId, recipeData) => {
  try {
    const { status, data } = await axiosInstance.put(
      `${RECIPE_URLS.UPDATE_RECIPES}/${recipeId}`,
      recipeData
    );

    if (status === 200 || status === 201) {
      return data;
    }
    throw new Error('Failed to update recipe');
  } catch (error) {
    throw error;
  }
};

export const getAttributeList = async (
  search,
  page,
  filter,
  limit,
  sort,
  type
) => {
  try {
    const params = new URLSearchParams();

    if (search) params.append('search', search);
    if (page) params.append('page', page.toString());
    if (limit) params.append('limit', limit.toString());
    if (type) params.append('type', type);

    // Add filter parameters
    if (filter?.status) params.append('status', filter.status);

    // Add sort parameters
    if (sort?.key) params.append('sort_by', sort.key);
    if (sort?.value) params.append('sort_order', sort.value);

    const url = `${RECIPE_URLS.GET_ATTRIBUTES}?${params.toString()}`;

    const { status, data } = await axiosInstance.get(url);

    if (status === 200) {
      return {
        attributes: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      attributes: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};

export const getPublicRecipeList = async (search, page, Rpp, filter) => {
  try {
    // Build query parameters
    const params = new URLSearchParams();

    // Basic parameters
    if (page) params.append('page', page);
    if (Rpp) params.append('limit', Rpp);
    if (search) params.append('search', search);

    // Filter parameters for public recipes
    if (filter) {
      // Categories filter (array of category IDs)
      if (filter.categories && filter.categories.length > 0) {
        params.append('categories', filter.categories.join(','));
      }

      // Allergens filter (exclude recipes with these allergens)
      if (filter.allergens && filter.allergens.length > 0) {
        params.append('exclude_allergen', filter.allergens.join(','));
      }

      // Dietary filter (include recipes with these dietary attributes)
      if (filter.dietary && filter.dietary.length > 0) {
        params.append('dietary', filter.dietary.join(','));
      }

      // Difficulty level filter
      if (filter.difficulty) {
        params.append('recipe_complexity_level', filter.difficulty);
      }

      // Cost range filter
      if (filter.cost_min !== undefined) {
        params.append('portion_cost_min', filter.cost_min);
      }
      if (filter.cost_max !== undefined) {
        params.append('portion_cost_max', filter.cost_max);
      }

      // Cooking time filter
      if (filter.cooking_time_min !== undefined) {
        params.append('cooking_time_min', filter.cooking_time_min);
      }
      if (filter.cooking_time_max !== undefined) {
        params.append('cooking_time_max', filter.cooking_time_max);
      }
    }

    const { status, data } = await axiosInstance.get(
      `${RECIPE_URLS.GET_PUBLIC_RECIPE_LIST}?${params.toString()}`
    );

    if (status === 200) {
      return {
        recipesList: data?.data || [],
        totalCount: data?.count || 0,
      };
    }
    return {
      recipesList: [],
      totalCount: 0,
    };
  } catch (error) {
    throw error;
  }
};
