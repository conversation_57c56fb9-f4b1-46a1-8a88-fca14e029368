import React from 'react';
import { Box, Typography } from '@mui/material';
import CustomTextField from '@/components/UI/CustomTextField';
import { staticOptions } from '@/helper/common/staticOptions';
import Gender from '@/components/UI/FormGroupGender';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomSelect from '@/components/UI/CustomSelect';
import OtpInput from 'react-otp-input';
import dayjs from 'dayjs';

export default function Personalinfo({
  touched,
  errors,
  values,
  handleChange,
  setFieldValue,
  handleBlur,
  ViewAccessOnly,
  countryList,
}) {
  return (
    <Box>
      <Box className="display-grid pt16">
        <Box>
          <CustomTextField
            fullWidth
            id="pi_fname"
            name="pi_fname"
            value={values?.pi_fname}
            label="First Name"
            required
            placeholder="Enter first name"
            error={Boolean(touched.pi_fname && errors.pi_fname)}
            helperText={touched.pi_fname && errors.pi_fname}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="pi_mname"
            name="pi_mname"
            value={values?.pi_mname}
            label="Middle Name"
            placeholder="Enter middle name"
            error={Boolean(touched.pi_mname && errors.pi_mname)}
            helperText={touched.pi_mname && errors.pi_mname}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="pi_lname"
            name="pi_lname"
            value={values?.pi_lname}
            label="Last Name"
            required
            placeholder="Enter last name"
            error={Boolean(touched.pi_lname && errors.pi_lname)}
            helperText={touched.pi_lname && errors.pi_lname}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
      </Box>
      <Box className="pt8">
        <Gender
          keyValue={values?.pi_gender}
          keyName="pi_gender"
          setFieldValue={setFieldValue}
          isRequire={true}
          disable={ViewAccessOnly}
        />
        {touched.pi_gender && errors.pi_gender && (
          <Typography variant="body2" className="other-field-error-text">
            {errors.pi_gender}
          </Typography>
        )}
      </Box>
      <Box className="display-grid pt8">
        <Box className="phone-number">
          <CustomTextField
            fullWidth
            id="pi_phoneNo"
            name="pi_phoneNo"
            value={values?.pi_phoneNo}
            label="Phone number"
            required
            placeholder="Enter Phone number"
            error={Boolean(touched.pi_phoneNo && errors.pi_phoneNo)}
            helperText={touched.pi_phoneNo && errors.pi_phoneNo}
            onBlur={handleBlur}
            disabled={ViewAccessOnly}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 12) {
                handleChange(e);
              }
            }}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
          />
        </Box>
        <Box className="phone-number">
          <CustomTextField
            fullWidth
            id="pi_hphoneNo"
            name="pi_hphoneNo"
            value={values?.pi_hphoneNo}
            label="Emergency Contact"
            required
            placeholder="Enter Emergency Contact"
            error={Boolean(touched.pi_hphoneNo && errors.pi_hphoneNo)}
            helperText={touched.pi_hphoneNo && errors.pi_hphoneNo}
            onBlur={handleBlur}
            onChange={(e) => {
              if (e.target.value === '' || e.target.value?.length < 12) {
                handleChange(e);
              }
            }}
            disabled={ViewAccessOnly}
            onInput={(e) => {
              e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
            }}
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="pi_email"
            name="pi_email"
            value={values?.pi_email}
            label="Email address"
            required
            placeholder="Enter email address"
            error={Boolean(touched.pi_email && errors.pi_email)}
            helperText={touched.pi_email && errors.pi_email}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
          />
        </Box>
        <Box>
          <CustomSelect
            placeholder="Nationality"
            name="pi_country"
            options={countryList}
            value={
              countryList?.find((opt) => {
                return opt?.value === values?.pi_country;
              }) || ''
            }
            error={touched?.pi_country && errors?.pi_country}
            helperText={touched?.pi_country && errors?.pi_country}
            isDisabled={ViewAccessOnly}
            onChange={(e) => {
              setFieldValue('pi_country', e?.value);
            }}
            label={<span>Nationality</span>}
            required
          />
        </Box>
        <Box>
          <CustomTextField
            fullWidth
            id="pi_passport"
            name="pi_passport"
            value={values?.pi_passport}
            label="Passport No. / ID Doc No."
            placeholder="Enter Passport No. / ID Doc No."
            error={Boolean(touched.pi_passport && errors.pi_passport)}
            helperText={touched.pi_passport && errors.pi_passport}
            onBlur={handleBlur}
            onChange={handleChange}
            disabled={ViewAccessOnly}
            required
          />
        </Box>

        <Box>
          <CustomSelect
            placeholder="Work Permit Type"
            name="workpermit"
            options={staticOptions?.WORK_PERMIT_LIST}
            value={
              values?.workpermit &&
              staticOptions?.WORK_PERMIT_LIST?.find(
                (f) => f?.value === values?.workpermit
              )
            }
            onChange={(e) => {
              setFieldValue('workpermit', e?.value);
            }}
            error={touched?.workpermit && errors?.workpermit}
            helperText={touched?.workpermit && errors?.workpermit}
            isDisabled={ViewAccessOnly}
            label={<span>Work Permit Type</span>}
            required
          />
        </Box>
        {values?.workpermit === 'Other' && (
          <Box>
            <CustomTextField
              fullWidth
              id="pi_workpermit"
              name="pi_workpermit"
              value={values?.pi_workpermit}
              label="Work Permit"
              placeholder="Work Permit"
              required
              error={Boolean(touched.pi_workpermit && errors.pi_workpermit)}
              helperText={touched.pi_workpermit && errors.pi_workpermit}
              onBlur={handleBlur}
              onChange={handleChange}
              disabled={ViewAccessOnly}
            />
          </Box>
        )}
        <Box>
          <CustomDatePicker
            required
            label={<span>Validity</span>}
            error={Boolean(touched.pi_validity && errors.pi_validity)}
            helperText={touched.pi_validity && errors.pi_validity}
            name="pi_validity"
            value={dayjs(values?.pi_validity)}
            onBlur={handleBlur}
            onChange={(date) => {
              setFieldValue('pi_validity', date);
            }}
            inputVariant="outlined"
            // format={'M/YY'}
            // yearMonth
            disablePast
            disabled={ViewAccessOnly}
            format="DD/MM/YYYY"
            // disabled={isDisabled}
          />
        </Box>
        <Box>
          <CustomDatePicker
            label={<span>Issued Date</span>}
            required
            disabled={ViewAccessOnly}
            name="pi_issuingAuth"
            value={dayjs(values?.pi_issuingAuth)}
            error={Boolean(touched.pi_issuingAuth && errors.pi_issuingAuth)}
            helperText={touched.pi_issuingAuth && errors.pi_issuingAuth}
            onBlur={handleBlur}
            onChange={(date) => {
              setFieldValue('pi_issuingAuth', date);
            }}
            disableFuture={true}
            format="DD/MM/YYYY"
            inputVariant="outlined"
          />
        </Box>
      </Box>
      <Box className="pt16">
        <Typography className="field-label">
          {' '}
          National Insurance Number (if Known)
        </Typography>
        <Box
          className={
            ViewAccessOnly ? 'otp-section cursor-notallow' : 'otp-section'
          }
        >
          <OtpInput
            // value={OTP}
            className="otp"
            onChange={(otp) => {
              handleChange(otp);
              setFieldValue('insurance', otp);
            }}
            autoFocus
            OTPLength={9}
            numInputs={9}
            value={values?.insurance}
            renderInput={(props) => (
              <input
                {...props}
                // onKeyPress={(e) => {
                //   if (/[^0-9,.\s]/.test(e.key)) {
                //     e.preventDefault();
                //   }
                // }}
                className={ViewAccessOnly ? 'cursor-notallow' : ''}
                disabled={ViewAccessOnly}
                onPaste={(e) => {
                  // if (/^[0-9,.]+$/.test(e.clipboardData.getData('text'))) {
                  //   // setValue(pasteData);
                  // } else {
                  e.preventDefault(); // prevent invalid input from being pasted
                  // }
                }}
              />
            )}
          />
        </Box>
      </Box>
    </Box>
  );
}
