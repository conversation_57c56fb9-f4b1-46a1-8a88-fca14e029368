'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Box, Grid } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import CustomColorPicker from '@/components/UI/CustomColorPicker';
import { setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { identifiers } from '@/helper/constants/identifier';
import SignatureField from '@/components/UI/SignatureField';
import { findDifferences } from '@/helper/common/commonFunctions';
import SignatureHistory from '@/components/SignatureHistory';
import { useSearchParams } from 'next/navigation';

const validationSchema = Yup.object().shape({
  branchname: Yup.string().trim().required('This field is required'),
  company_name: Yup.string().trim().required('This field is required'),
  branch_work: Yup.string().trim().required('This field is required'),
  emp_name: Yup.string().trim().required('This field is required'),
  sign_name: Yup.string().trim().required('This field is required'),
  r_no: Yup.string().trim().required('This field is required'),
  status: Yup.string().trim().required('This field is required'),
  branch_remark: Yup.string().trim().required('This field is required'),
  emp_sign: Yup.string().trim().required('This field is required'),
  branch_color: Yup.string().trim().required('This field is required'),
  text_color: Yup.string().trim().required('This field is required'),
});

const initialValues = {
  branchname: '',
  company_name: '',
  branch_work: '',
  emp_name: '',
  sign_name: '',
  r_no: '',
  status: 'active',
  branch_remark: '',
  remark: '',
  emp_sign: '',
  branch_color: '#006bff',
  text_color: '#ffffff',
};

const logBookValues = {
  branch_employer_name: "Employer's Name",
  branch_heading_employer_name: 'Authorized Signatory',
  branch_heading_name: 'Company Name',
  branch_work_place: 'Main Place of Work',
  branch_heading_work_place: 'Location',
  branch_sign: "Employer's Signature",
  branch_name: 'Branch Name',
  branch_remark: 'Remark',
  branch_status: 'Branch Status',
  branch_color: 'Branch Color',
};

const logbookKey = [
  'branch_employer_name',
  'branch_heading_employer_name',
  'branch_heading_name',
  'branch_work_place',
  'branch_heading_work_place',
  'branch_sign',
  'branch_name',
  'branch_remark',
  'branch_status',
  'branch_color',
];

const LogBook = (key, value) => {
  return (
    <>
      {key === 'branch_sign' ? (
        <span className="fw600">Employer's Signature</span>
      ) : logBookValues[key] && value?.newValue && value?.newValue !== null ? (
        <>
          <span className="fw600 ">
            {key && logBookValues[key] ? logBookValues[key] : ''}
          </span>
          <span className="fw600 ">{' : '}</span>
          <span className=" fw400">{value?.newValue}</span>
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export default function AddEditBranch({ getBranchList, isUpdate, handleBack }) {
  const searchParams = useSearchParams();
  const [loader, setLoader] = useState(false);
  const [signValue, setSignValue] = useState(null);
  const [addSign, setAddSign] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);
  const [signatureHistory, setSignatureHistory] = useState([]);
  const [updateItem, setUpdateItem] = useState();
  const formikRef = useRef();

  const isBranchId = searchParams.get('branch_id');

  const getSettingsDetails = async () => {
    if (!isBranchId) return;

    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_BRANCH + `${isBranchId}`
      );
      const branchData = data?.data;
      setUpdateItem(branchData);
      if (status === 200 || status === 201) {
        if (branchData?.branch_sign) {
          setSignValue({
            url: branchData?.branch_sign,
            isLink: true,
          });
          formikRef?.current?.setFieldValue(
            'emp_sign',
            branchData?.branch_sign
          );
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (isUpdate && isBranchId) {
      getSettingsDetails();
      getSettingsHistory();
    }
  }, [isUpdate, isBranchId]);

  const getSettingsHistory = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_BRANCH_HISTORY + `${isBranchId}`
      );

      if (status === 200 || status === 201) {
        const signData =
          data?.data &&
          data?.data?.length > 0 &&
          data?.data?.map((a) => {
            let newdata = a?.new_data && JSON.parse(a?.new_data);
            let previousdata = a?.previous_data && JSON.parse(a?.previous_data);
            if (a?.activity_action !== 'created' && newdata && previousdata) {
              var diffs = findDifferences(previousdata, newdata);
            }
            return {
              ...a,
              differenceData:
                a?.activity_action !== 'created' ? diffs : newdata,
              new_data: newdata,
              previous_data: previousdata,
            };
          });
        setSignatureHistory(signData || []);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setSignatureHistory([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    setLoader(true);
    try {
      const sendData = {
        branch_name: values?.branchname,
        branch_heading_name: values?.company_name,
        branch_heading_work_place: values?.branch_work,
        branch_employer_name: values?.emp_name,
        branch_heading_employer_name: values?.sign_name,
        registration_number: values?.r_no,
        branchStatus: values?.status,
        branch_color: values?.branch_color,
        text_color: values?.text_color,
        branch_work_place: values?.branch_remark || null,
        branch_remark: values?.remark || null,
        ...(!signValue?.isLink && { branch_sign: values?.emp_sign || null }),
        // branch_sign: values?.emp_sign || null,
      };
      const ApiUrl = isUpdate
        ? URLS.UPDATE_BRANCH + isBranchId
        : URLS.UPDATE_BRANCH;

      const method = 'put';

      const { status, data } = await axiosInstance[method](ApiUrl, sendData);

      if (status === 200 || status === 201) {
        if (data?.status) {
          handleBack();
          setApiMessage('success', data?.message);
          getBranchList();
        } else {
          setApiMessage('error', data?.message);
        }
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'An error occurred'
      );
    } finally {
      setLoader(false);
      setSubmitting(false);
    }
  };

  const getInitialValues = () => {
    if (isUpdate && updateItem) {
      return {
        branchname: updateItem?.branch_name || '',
        company_name: updateItem?.branch_heading_name || '',
        branch_work: updateItem?.branch_work_place || '',
        emp_name: updateItem?.branch_employer_name || '',
        sign_name: updateItem?.branch_heading_employer_name || '',
        r_no: updateItem?.registration_number || '',
        status: updateItem?.branch_status || 'active',
        branch_remark: updateItem?.branch_heading_work_place || '',
        remark: updateItem?.branch_remark || '',
        emp_sign: updateItem?.branch_sign || '',
        branch_color: updateItem?.branch_color || '#006bff',
        text_color: updateItem?.text_color || '#ffffff',
      };
    }
    return initialValues;
  };

  return (
    <Box>
      <Formik
        initialValues={getInitialValues()}
        enableReinitialize={true}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        innerRef={formikRef}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
          dirty,
          isValid,
        }) => {
          return (
            <Form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    name="branchname"
                    label="Branch name"
                    placeholder="Enter branch name"
                    value={values.branchname}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={Boolean(touched.branchname && errors.branchname)}
                    helperText={touched.branchname && errors.branchname}
                    required
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="company_name"
                    value={values.company_name}
                    label="Company Name"
                    placeholder="Enter Company Name"
                    error={Boolean(touched.company_name && errors.company_name)}
                    helperText={touched.company_name && errors.company_name}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="branch_work"
                    value={values.branch_work}
                    label="Main Place of Work"
                    placeholder="Enter Main Place of Work"
                    error={Boolean(touched.branch_work && errors.branch_work)}
                    helperText={touched.branch_work && errors.branch_work}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="emp_name"
                    value={values.emp_name}
                    label="Employer's Name"
                    placeholder="Enter Employer's Name"
                    error={Boolean(touched.emp_name && errors.emp_name)}
                    helperText={touched.emp_name && errors.emp_name}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="sign_name"
                    value={values?.sign_name}
                    label="Authorized Signatory"
                    placeholder="Enter Authorized Signatory"
                    error={Boolean(touched.sign_name && errors.sign_name)}
                    helperText={touched.sign_name && errors.sign_name}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="r_no"
                    value={values.r_no}
                    label="Registration No"
                    placeholder="Enter Registration No"
                    error={Boolean(touched.r_no && errors.r_no)}
                    helperText={touched.r_no && errors.r_no}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomColorPicker
                    id="branch_color"
                    name="branch_color"
                    label="Branch Color"
                    required
                    value={values?.branch_color}
                    onChange={(val) => {
                      setFieldValue('branch_color', val);
                    }}
                    // error={false}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomColorPicker
                    id="text_color"
                    name="text_color"
                    label="Text Color"
                    required
                    value={values?.text_color}
                    onChange={(val) => {
                      setFieldValue('text_color', val);
                    }}
                    // error={false}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomSelect
                    required
                    name="status"
                    label="Status"
                    placeholder="Status"
                    options={identifiers?.STATUS}
                    value={
                      identifiers?.STATUS?.find(
                        (opt) => opt?.value === values?.status
                      ) || ''
                    }
                    className={
                      touched.status && errors.status ? 'textfeild-error' : ''
                    }
                    onChange={(selectedOption) =>
                      setFieldValue('status', selectedOption?.value || '')
                    }
                    error={Boolean(touched.status && errors.status)}
                    helperText={touched.status && errors.status}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    name="remark"
                    label="Remark"
                    multiline
                    rows={3}
                    onChange={handleChange}
                    error={Boolean(touched.remark && errors.remark)}
                    helperText={touched.remark && errors.remark}
                    placeholder="Remark"
                    value={values.remark}
                  />
                </Grid>

                <Grid item xs={12} md={4}>
                  <CustomTextField
                    fullWidth
                    required
                    name="branch_remark"
                    value={values?.branch_remark}
                    label="Location"
                    multiline
                    rows={3}
                    placeholder="Enter Location"
                    error={Boolean(
                      touched.branch_remark && errors.branch_remark
                    )}
                    helperText={touched.branch_remark && errors.branch_remark}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box>
                    <SignatureField
                      signValue={signValue}
                      setSignValue={setSignValue}
                      addSign={addSign}
                      setAddSign={setAddSign}
                      isSubmit={isSubmit}
                      setIsSubmit={setIsSubmit}
                      formikRef={formikRef}
                      title="Employer's Signature"
                      error={Boolean(touched.emp_sign && errors.emp_sign)}
                      helperText={touched.emp_sign && errors.emp_sign}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} className="d-flex justify-end">
                  <CustomButton
                    variant="contained"
                    type="submit"
                    disabled={!(dirty && isValid) || loader}
                    title={`${loader ? 'Saving...' : 'Save'}`}
                  />
                </Grid>
              </Grid>
            </Form>
          );
        }}
      </Formik>
      <SignatureHistory
        signatureHistory={signatureHistory}
        logbookKey={logbookKey}
        logBookValues={logBookValues}
        LogBook={LogBook}
        title="Log Book"
      />
    </Box>
  );
}
