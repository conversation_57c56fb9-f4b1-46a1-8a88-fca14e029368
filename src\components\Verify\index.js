'use client';
import React, { useState, useCallback, useEffect } from 'react';
import { Box, TextField, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';

import CustomButton from '../UI/button';
import { setApiMessage } from '@/helper/common/commonFunctions';
import {
  LeftVerifyVector,
  RightVerifyVector,
} from '../../helper/common/images';
// import AuthContext from '@/helper/authcontext';
import { ORG_URLS } from '@/helper/constants/urls';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { fetchFromStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import useTimer from '@/hooks/userTimer';
import './verify.scss';

// Validation Schema
const OtpSchema = Yup.object().shape({
  otp: Yup.array()
    .of(
      Yup.string()
        .matches(/^[0-9]{1}$/, 'Must be a single digit')
        .required('Required')
    )
    .min(4, 'OTP must be 4 digits'),
});

export default function VerifyEmail() {
  // const { authState } = useContext(AuthContext);
  const [loader, setLoader] = useState(false);
  const router = useRouter();
  const { timerCount, isTimerActive, startTimer } = useTimer(60);

  useEffect(() => {
    let hasCancelledOnce = false;

    const handleBackButton = (e) => {
      e.preventDefault();
      if (hasCancelledOnce) {
        router.push('/org/login');
        return;
      }

      if (
        window.confirm(
          'Please complete the verification process or contact support if you need assistance. Do you want to go back to login?'
        )
      ) {
        router.push('/org/login');
      } else {
        hasCancelledOnce = true;
        // Push the current state again to prevent back navigation
        window.history.pushState(null, '', window.location.href);
      }
    };

    // Push initial state
    window.history.pushState(null, '', window.location.href);
    window.addEventListener('popstate', handleBackButton);

    return () => {
      window.removeEventListener('popstate', handleBackButton);
    };
  }, [router]);

  const resendOTP = useCallback(async () => {
    if (isTimerActive || loader) return;

    const user = fetchFromStorage(identifiers.USER_ID);
    if (!user?.id) return;

    try {
      setLoader(true);
      const { status, data } = await axiosInstance.post(ORG_URLS.RESEND_OTP, {
        userId: user.id,
      });

      if (status === 200) {
        if (data?.status) {
          setApiMessage('success', data?.message);
          startTimer();
        } else {
          setApiMessage('error', data?.message || 'Failed to resend OTP.');
        }
      } else {
        setApiMessage('error', data?.message || 'Failed to resend OTP.');
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'An error occurred while resending OTP.'
      );
    } finally {
      setLoader(false);
    }
  }, [isTimerActive, loader, startTimer]);

  const verifyOTP = async (otp) => {
    const user = fetchFromStorage(identifiers.USER_ID);
    if (!user?.id) return;
    if (loader) return;

    try {
      setLoader(true);
      const { status, data } = await axiosInstance.post(ORG_URLS.VERIFY_OTP, {
        userId: user.id,
        otp: otp,
      });

      if (status === 200 && data?.status) {
        setApiMessage('success', data?.message);
        router.push('/org/reset-password');
      } else {
        setApiMessage(
          'error',
          data?.message || 'Invalid OTP or verification failed.'
        );
      }
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'An error occurred during verification.'
      );
    } finally {
      setLoader(false);
    }
  };

  const handleOtpChange = (index, value, values, setFieldValue) => {
    if (/^[0-9]?$/.test(value)) {
      setFieldValue(`otp.${index}`, value);
      const next =
        value && index < 3
          ? index + 1
          : value === '' && index > 0
            ? index - 1
            : index;
      const nextInput = document.getElementById(`otp-input-${next}`);
      if (nextInput) nextInput.focus();
    }
  };

  return (
    <Formik
      initialValues={{ otp: ['', '', '', ''] }}
      validationSchema={OtpSchema}
      onSubmit={(values) => {
        verifyOTP(values?.otp?.join(''));
      }}
    >
      {({ values, setFieldValue, errors, touched }) => (
        <Form className="verify-form-wrap">
          <Box className="verify-container">
            <Box className="verify-number-wrap">
              <Typography variant="h4" className="heading-wrap heading-text">
                Verify Your Email Address
              </Typography>
              <Typography component="p" className="text-wrap sub-heading-text">
                We have successfully sent a password reset activation code to
                your email address.
              </Typography>

              <Box className="d-flex justify-center input-wrap">
                {values?.otp?.map((digit, index) => (
                  <TextField
                    key={index}
                    id={`otp-input-${index}`}
                    value={digit}
                    variant="filled"
                    className="opt-input"
                    placeholder="-"
                    inputProps={{ maxLength: 1 }}
                    error={Boolean(
                      touched?.otp?.[index] && errors?.otp?.[index]
                    )}
                    helperText={touched?.otp?.[index] && errors?.otp?.[index]}
                    onChange={(e) =>
                      handleOtpChange(
                        index,
                        e.target.value,
                        values,
                        setFieldValue
                      )
                    }
                    disabled={loader}
                  />
                ))}
              </Box>

              <Box className="verify-btn-wrap" textAlign="center">
                <CustomButton
                  fullWidth
                  className="verify-btn"
                  type="submit"
                  variant="contained"
                  title="Verify"
                  isLoading={loader}
                  disabled={loader}
                />
              </Box>

              <Typography component="p" className="code-text-wrap">
                Didn't receive code?{' '}
                {isTimerActive ? (
                  <span>
                    Resend in <span className="timer-wrap"> {timerCount}s</span>
                  </span>
                ) : (
                  <span
                    className="try-again-wrap"
                    onClick={resendOTP}
                    style={{
                      cursor:
                        loader || isTimerActive ? 'not-allowed' : 'pointer',
                      opacity: loader || isTimerActive ? 0.6 : 1,
                    }}
                  >
                    Resend Code
                  </span>
                )}
              </Typography>
            </Box>
          </Box>

          <Box className="left-vector">
            <LeftVerifyVector />
          </Box>
          <Box className="right-vector">
            <RightVerifyVector />
          </Box>
        </Form>
      )}
    </Formik>
  );
}
