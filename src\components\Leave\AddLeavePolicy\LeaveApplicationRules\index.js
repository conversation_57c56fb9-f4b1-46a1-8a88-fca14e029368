import React, { useEffect, useContext } from 'react';
import * as Yup from 'yup';
import { Box, Tooltip, Typography } from '@mui/material';
import { Formik, Form } from 'formik';
import InfoIcon from '@mui/icons-material/Info';
import AuthContext from '@/helper/authcontext';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import './applicationrules.scss';
import CustomRadio from '@/components/UI/CustomRadio';

export default function LeaveApplicationRules({
  setLeavePolicyDetails,
  formikRefAppRule,
  leavePolicyDetails,
  setLeavePolicySubmit,
  leavePolicySubmit,
  LeaveStep2Payload,
  leavePolicyBack,
  setLeavePolicyBack,
}) {
  const { authState } = useContext(AuthContext);
  const isHour =
    authState?.generalSeetings?.leave_period_type &&
    authState?.generalSeetings?.leave_period_type === 'hour'
      ? true
      : false;

  const { leaveApplicationRules } = leavePolicyDetails || {};
  const {
    allow_leave_request_data,
    has_document_required,
    restrict_by_gender,
    restrict_by_gender_status,
    restrict_by_marital,
    restrict_by_marital_status,
    round_of_decimal_leave_balance,
  } = leaveApplicationRules || {};

  const validationSchema = Yup.object().shape({
    pastDays: Yup.lazy((value, context) => {
      const past = context.parent.past;
      return past === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Past days cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
    nextDays: Yup.lazy((value, context) => {
      const next = context.parent.next;
      return next === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Future days cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
    toBeAppliedDays: Yup.lazy((value, context) => {
      const toBeApplied = context.parent.toBeApplied;
      return toBeApplied === true
        ? Yup.string()
            .required('This field is required')
            .test(
              'not-zero',
              'Future days cannot be 0',
              (val) => !/^[0]+$/.test(val)
            )
        : Yup.string().nullable().notRequired();
    }),
  });

  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const maritalStatusOptions = [
    { label: 'Married', value: 'married' },
    { label: 'Unmarried', value: 'unmarried' },
  ];

  useEffect(() => {
    setLeavePolicySubmit({
      ...leavePolicySubmit,
      leaveApplicationRules: false,
    });
    setLeavePolicyBack({
      ...leavePolicyBack,
      leaveApplicationRules: false,
    });
  }, []);
  const Inputype = (e) => {
    let value = e.target.value;
    if (isHour) {
      {
        // Remove non-numeric characters
        if (value === '' || /^\d*\.?\d{0,2}$/.test(value)) {
          e.target.value = value;
        } else {
          e.target.value = value.slice(0, -1);
        }
      }
    } else {
      {
        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
      }
    }
  };
  return (
    <Formik
      innerRef={formikRefAppRule}
      enableReinitialize
      initialValues={{
        documentProofRequired: has_document_required ? 'yes' : 'no',
        documentProofDays:
          leaveApplicationRules?.limit_for_document_required || 0,
        restrictTo: restrict_by_gender || false,
        restrictedGender: restrict_by_gender_status || 'male',
        employeeRestriction: restrict_by_marital || false,
        employeeRestrictionStatus: restrict_by_marital_status || 'married',
        roundingOption: round_of_decimal_leave_balance || 'no',
        pastDates: allow_leave_request_data?.past_date ?? true,
        past: allow_leave_request_data?.past_date_status || false,
        pastDays: allow_leave_request_data?.past_date_days || 0,
        futureDates: allow_leave_request_data?.future_date ?? true,
        next: allow_leave_request_data?.future_date_status || false,
        nextDays: allow_leave_request_data?.future_date_days || 0,
        toBeApplied:
          allow_leave_request_data?.apply_in_advance_days_status || false,
        toBeAppliedDays: allow_leave_request_data?.apply_in_advance_days || 0,
      }}
      validationSchema={validationSchema}
      onSubmit={(values) => {
        setLeavePolicyDetails((prevState) => ({
          ...prevState,
          leaveApplicationRules: LeaveStep2Payload(values),
        }));
      }}
    >
      {({
        values,
        errors,
        touched,
        handleChange,
        setFieldValue,
        handleBlur,
      }) => (
        <Form>
          <Box className="leave-application-rules-wrap">
            <Box className="d-flex align-center gap-10 mt16 flex-wrap">
              <Box>
                <Box>
                  <CustomCheckbox
                    checked={values?.restrictTo}
                    onChange={(e) => {
                      setFieldValue('restrictTo', e?.target?.checked);
                      setFieldValue('restrictedGender', 'male');
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveApplicationRules: false,
                      });
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveApplicationRules: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-content-text">
                        Restrict to
                      </Typography>
                    }
                  />
                </Box>
              </Box>
              <Box>
                <CustomSelect
                  className="leave-select-wrap"
                  placeholder="Select"
                  name="restrictedGender"
                  options={genderOptions}
                  value={
                    genderOptions?.find((opt) => {
                      return opt?.value === values.restrictedGender;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('restrictedGender', e?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  error={Boolean(
                    touched?.restrictedGender && errors?.restrictedGender
                  )}
                  helperText={
                    touched?.restrictedGender && errors?.restrictedGender
                  }
                  isDisabled={!values?.restrictTo}
                  isClearable={false}
                />
              </Box>
            </Box>
            <Box className="d-flex align-center gap-10 mt16 flex-wrap">
              <Box>
                <CustomCheckbox
                  checked={values?.employeeRestriction}
                  onChange={(e) => {
                    setFieldValue('employeeRestriction', e?.target?.checked);
                    setFieldValue('employeeRestrictionStatus', 'married');
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Restrict to employee having
                    </Typography>
                  }
                />
              </Box>
              <Box>
                <CustomSelect
                  className="leave-select-wrap"
                  placeholder="Select"
                  name="employeeRestrictionStatus"
                  options={maritalStatusOptions}
                  value={
                    maritalStatusOptions?.find((opt) => {
                      return opt?.value === values?.employeeRestrictionStatus;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('employeeRestrictionStatus', e?.value);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  error={Boolean(
                    touched?.employeeRestrictionStatus &&
                      errors?.employeeRestrictionStatus
                  )}
                  helperText={
                    touched?.employeeRestrictionStatus &&
                    errors?.employeeRestrictionStatus
                  }
                  isDisabled={!values?.employeeRestriction}
                  isClearable={false}
                />
              </Box>
            </Box>

            <Box className="mt16 d-flex align-center gap-5">
              <Typography className="sub-content-text">
                When applying for a leave, if leave balance is in decimals, do
                you want to round-off the balance for the purpose of leave
                application?
              </Typography>
              <Tooltip
                arrow
                title={
                  <Typography>
                    Rounding-off the leave balance will not have an effect on
                    the annual quota. If a leave balance of 0.8 is rounded to
                    1.0 ,the employee is granted a negative balance of -0.2
                    which is adjusted in his immediate accrual period.
                  </Typography>
                }
                classes={{ tooltip: 'info-tooltip-container' }}
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            </Box>

            <Box className="mt4">
              <Box>
                <CustomRadio
                  name="roundingOption"
                  checked={values?.roundingOption === 'no'}
                  onChange={() => {
                    setFieldValue('roundingOption', 'no');
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      {isHour
                        ? 'Do not round-off fractional hours. An employee will not be able to consume leave if he or she does not have a sufficient leave.'
                        : 'Do not round-off decimals. Employee will not be able to consume leave if does not have sufficient leave balance.'}
                    </Typography>
                  }
                />
              </Box>
              <Box className="d-flex align-center gap-sm">
                <CustomRadio
                  name="roundingOption"
                  checked={values?.roundingOption === 'full_day'}
                  onChange={() => {
                    setFieldValue('roundingOption', 'full_day');
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  label={
                    <Typography className="sub-content-text">
                      {isHour
                        ? ' Round-off to full hour'
                        : ' Round-off to full day'}
                    </Typography>
                  }
                />
                <Tooltip
                  arrow
                  title={
                    <>
                      <Typography>{'Balance <= 0.5 - Rounded to 0'}</Typography>
                      <Typography>{'Balance > 0.5 - Rounded to 1'}</Typography>
                    </>
                  }
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon className="info-icon cursor-pointer" />
                </Tooltip>
              </Box>
            </Box>

            <Box className="mt16">
              <Typography className="sub-content-text">
                Allow leave requests for,
              </Typography>

              <Box className="d-flex align-center gap-sm">
                <CustomCheckbox
                  checked={values?.pastDates}
                  onChange={(e) => {
                    setFieldValue('pastDates', e?.target?.checked);
                    setFieldValue('past', false);
                    setFieldValue('pastDays', 0);
                    setLeavePolicySubmit({
                      ...leavePolicySubmit,
                      leaveApplicationRules: false,
                    });
                    setLeavePolicyBack({
                      ...leavePolicyBack,
                      leaveApplicationRules: false,
                    });
                  }}
                  disableRipple
                  label={
                    <Typography className="sub-content-text">
                      Past Dates
                    </Typography>
                  }
                />
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      When the Past Dates rule is selected and days are not
                      specified, the employee can apply for the leave either up
                      to his "Joining Date" or up to 365 calendar days back from
                      the current date, excluding the current date, whichever
                      comes earlier.
                    </Typography>
                  }
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <InfoIcon className="info-icon cursor-pointer" />
                </Tooltip>
              </Box>

              {values?.pastDates ? (
                <Box className="ml16 d-flex align-center gap-sm">
                  <Box>
                    <Box>
                      <CustomCheckbox
                        checked={values?.past}
                        onChange={(e) => {
                          setFieldValue('past', e?.target?.checked);
                          setFieldValue('pastDays', 0);
                          setLeavePolicySubmit({
                            ...leavePolicySubmit,
                            leaveApplicationRules: false,
                          });
                          setLeavePolicyBack({
                            ...leavePolicyBack,
                            leaveApplicationRules: false,
                          });
                        }}
                        disableRipple
                        label={
                          <Typography className="sub-content-text">
                            Past
                          </Typography>
                        }
                      />
                    </Box>
                  </Box>
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="pastDays"
                      name="pastDays"
                      placeholder="Days"
                      value={values?.pastDays}
                      error={Boolean(touched?.pastDays && errors?.pastDays)}
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                        setLeavePolicySubmit({
                          ...leavePolicySubmit,
                          leaveApplicationRules: false,
                        });
                        setLeavePolicyBack({
                          ...leavePolicyBack,
                          leaveApplicationRules: false,
                        });
                      }}
                      onInput={Inputype}
                      disabled={!values?.past}
                    />
                  </Box>
                  <Box className="d-flex align-center gap-5">
                    <Typography className="title-text">Days</Typography>
                    <Tooltip
                      arrow
                      title={
                        <Typography>
                          When the Past Dates rule is selected and days are
                          specified, the employee can apply for the leave either
                          up to the mentioned day or up to his "Joining Date,"
                          excluding the current date, whichever comes earlier.
                        </Typography>
                      }
                      classes={{ tooltip: 'info-tooltip-container' }}
                    >
                      <InfoIcon className="info-icon cursor-pointer" />
                    </Tooltip>
                  </Box>
                  <Box>
                    {touched.pastDays && errors.pastDays && (
                      <Typography className="other-field-error-text">
                        {errors.pastDays}
                      </Typography>
                    )}
                  </Box>
                </Box>
              ) : (
                <></>
              )}

              <Box className="mt8">
                <Box className="d-flex align-center gap-5">
                  <CustomCheckbox
                    checked={values?.futureDates}
                    onChange={(e) => {
                      setFieldValue('futureDates', e?.target?.checked);
                      setFieldValue('next', false);
                      setFieldValue('nextDays', 0);
                      setFieldValue('toBeApplied', false);
                      setFieldValue('toBeAppliedDays', 0);
                      setLeavePolicySubmit({
                        ...leavePolicySubmit,
                        leaveApplicationRules: false,
                      });
                      setLeavePolicyBack({
                        ...leavePolicyBack,
                        leaveApplicationRules: false,
                      });
                    }}
                    disableRipple
                    label={
                      <Typography className="sub-content-text">
                        Future Dates
                      </Typography>
                    }
                  />
                  <Tooltip
                    arrow
                    title={
                      <Typography>
                        When the Future Dates rule is selected and days are not
                        specified, the employee can apply for the leave either
                        up to his "Compensation/Agreement End Date" (if the "Do
                        you want to consider leave accrual based on
                        Agreement/Compensation End Date?" rule is enabled) or
                        his "Employee Exit Date" or up to 365 calendar future
                        dates excluding the current date, whichever comes
                        earlier.
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
              </Box>
              {values?.futureDates ? (
                <>
                  <Box className="ml16 d-flex align-center gap-sm">
                    <Box>
                      <CustomCheckbox
                        checked={values?.next}
                        onChange={(e) => {
                          setFieldValue('next', e?.target?.checked);
                          setFieldValue('nextDays', 0);
                          setLeavePolicySubmit({
                            ...leavePolicySubmit,
                            leaveApplicationRules: false,
                          });
                          setLeavePolicyBack({
                            ...leavePolicyBack,
                            leaveApplicationRules: false,
                          });
                        }}
                        disableRipple
                        label={
                          <Typography className="sub-content-text">
                            Next
                          </Typography>
                        }
                      />
                    </Box>
                    <Box>
                      <CustomTextField
                        fullWidth
                        id="nextDays"
                        name="nextDays"
                        placeholder="Days"
                        value={values?.nextDays}
                        error={Boolean(touched?.nextDays && errors?.nextDays)}
                        onBlur={handleBlur}
                        onChange={(e) => {
                          handleChange(e);
                          setLeavePolicySubmit({
                            ...leavePolicySubmit,
                            leaveApplicationRules: false,
                          });
                          setLeavePolicyBack({
                            ...leavePolicyBack,
                            leaveApplicationRules: false,
                          });
                        }}
                        onInput={Inputype}
                        disabled={!values?.next}
                      />
                    </Box>
                    <Box className="d-flex align-center gap-5">
                      <Typography className="title-text">Days</Typography>
                      <Tooltip
                        arrow
                        title={
                          <Typography>
                            When the Future Dates rule is selected and days are
                            specified, the employee can apply for the leave
                            either up to the mentioned day or
                            "Compensation/Agreement End Date" (if the "Do you
                            want to consider leave accrual based on
                            Agreement/Compensation End Date?" rule is enabled)
                            or up to his "Employee Exit Date," excluding the
                            current date, whichever comes earlier.
                          </Typography>
                        }
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <InfoIcon className="info-icon cursor-pointer" />
                      </Tooltip>
                    </Box>
                    <Box>
                      {touched.nextDays && errors.nextDays && (
                        <Typography className="other-field-error-text">
                          {errors.nextDays}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  <Box className="ml16 d-flex align-center gap-sm mt8 flex-wrap">
                    <Box>
                      <Box>
                        <CustomCheckbox
                          checked={values?.toBeApplied}
                          onChange={(e) => {
                            setFieldValue('toBeApplied', e?.target?.checked);
                            setFieldValue('toBeAppliedDays', 0);
                            setLeavePolicySubmit({
                              ...leavePolicySubmit,
                              leaveApplicationRules: false,
                            });
                            setLeavePolicyBack({
                              ...leavePolicyBack,
                              leaveApplicationRules: false,
                            });
                          }}
                          disableRipple
                          label={
                            <Typography className="sub-content-text">
                              To be applied
                            </Typography>
                          }
                        />
                      </Box>
                    </Box>
                    <Box>
                      <CustomTextField
                        fullWidth
                        id="toBeAppliedDays"
                        name="toBeAppliedDays"
                        placeholder="Days"
                        value={values?.toBeAppliedDays}
                        error={Boolean(
                          touched?.toBeAppliedDays && errors?.toBeAppliedDays
                        )}
                        onBlur={handleBlur}
                        onChange={(e) => {
                          handleChange(e);
                          setLeavePolicySubmit({
                            ...leavePolicySubmit,
                            leaveApplicationRules: false,
                          });
                          setLeavePolicyBack({
                            ...leavePolicyBack,
                            leaveApplicationRules: false,
                          });
                        }}
                        onInput={Inputype}
                        disabled={!values?.toBeApplied}
                      />
                    </Box>
                    <Box className="d-flex align-center gap-5">
                      <Typography className="title-text">
                        days in advance
                      </Typography>
                      <Tooltip
                        arrow
                        title={
                          <Typography>
                            When the rule for future dates is checked, future
                            days are also specified. Now, if the "To Be Applied
                            to #N Days in Advance" rule is to be given an input
                            of #N days, then the user has to apply the leave in
                            advance to that given #N number of calendar days.
                          </Typography>
                        }
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <InfoIcon className="info-icon cursor-pointer" />
                      </Tooltip>
                    </Box>
                    <Box>
                      {touched.toBeAppliedDays && errors.toBeAppliedDays && (
                        <Typography className="other-field-error-text">
                          {errors.toBeAppliedDays}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </>
              ) : (
                <></>
              )}
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
