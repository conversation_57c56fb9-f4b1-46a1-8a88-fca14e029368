.Signature-history {
  margin-top: 24px;

  .folder-desc-divider {
    border-bottom: 1px solid var(--color-black);
    margin: 16px 0;
  }
  .tree {
    padding: 0;
    padding-left: 9px;
    list-style: none;
    color: #369;
    position: relative;

    li {
      margin: 0;
      line-height: 2em;
      font-weight: bold;
      position: relative;
      list-style: none;

      &:last-child {
        &:before {
          background: var(--color-white);
          height: auto;
          top: 1em;
          bottom: 0;
        }
      }
    }

    ul {
      margin: 0 0 0 1em;
      padding: 0;
      list-style: none;
      color: var(--color-black);
      position: relative;
      margin-left: 0.5em;

      li {
        margin: 0;
        padding: 0px 5px 0px 30px;
        position: relative;
        list-style: none;

        &:before {
          content: '';
          display: block;
          width: 40px;
          border-top: 1px solid var(--color-dark-20);
          margin-top: -5px;
          position: absolute;
          left: -3px;
          color: var(--color-dark-60);
          top: 10px;
          width: 7px;
          height: 7px;
          border-radius: 50%;
          background-color: var(--color-dark-60);
        }

        &:last-child {
          &:before {
            background: var(--color-white);
            bottom: 0;
            left: -3px;
            top: 10px;
            width: 7px;
            height: 7px;
            border-radius: 50%;
            background-color: var(--color-dark-60);
          }
        }
      }

      &:before {
        content: '';
        display: block;
        width: 0;
        position: absolute;
        top: 6px;
        bottom: 0;
        left: 0;
        border-left: 2px solid var(--color-dark-20);
        color: var(--color-dark-60);
      }
      .event-type-tree {
        padding-top: 14px;
        &:before {
          color: var(--color-dark-40);
        }
        li {
          &:before {
            color: var(--color-dark-40);
          }
          span {
            color: var(--color-dark-40);
            span {
              color: var(--color-black);
            }
          }
          .list {
            margin-bottom: 8px !important;
            display: flex;
            align-items: baseline;
          }
        }
      }
    }
  }
  .signature-list {
    margin-top: 22px;
    max-width: max-content;
  }

  .list-view {
    position: relative;
  }
  .Image-profile {
    max-width: max-content;
    position: absolute;
    left: -16px;
    top: 0;
    .profile-icon {
      width: 36px;
      height: 36px;
    }
    .profile-image {
      .profile {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit: cover;
      }
    }
  }
  .sign-activity {
    span {
      font-family: var(--font-family-primary) !important;
      font-size: 12px !important;
      line-height: 18px !important;
      letter-spacing: -0.5px !important;
    }
    margin-top: -2px;
  }
}