'use client';

import React, { useEffect, useState, useContext, useRef } from 'react';
import { Box, Divider, Typography, Tooltip } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import MultiSelect from '@/components/UI/CustomMultiSelect';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import Gender from '@/components/UI/FormGroupGender';
import MaritalStatus from '@/components/UI/FormGroupMarital';
import {
  setApiMessage,
  TextFieldMaxLength,
  HIGH_LEVEL_MANAGER_ROLE_IDS,
} from '@/helper/common/commonFunctions';
import { useRouter } from 'next/navigation';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';
import CustomEditor from '@/components/UI/CustomEditor';
import { identifiers } from '@/helper/constants/identifier';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import AuthContext from '@/helper/authcontext';
import moment from 'moment';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { ListManager } from 'react-beautiful-dnd-grid';
import CloseIcon from '@mui/icons-material/Close';
import MultiselectTitles from '@/components/UI/MultiselectTitles';
import CustomCreateSelect from '@/components/UI/CustomCreateSelect';
import CustomRadio from '@/components/UI/CustomRadio';
import DriveFileRenameOutlineIcon from '@mui/icons-material/DriveFileRenameOutline';
import useLocationData from '@/hooks/useLocationData';
import InfoIcon from '@mui/icons-material/Info';
import { LocationOn } from '@mui/icons-material';
import useRoleList from '@/hooks/useRoleList';
import _ from 'lodash';
import '../StaffList/staff.scss';

export default function CreateStaff() {
  const router = useRouter();
  const formikRef = useRef(null);
  const {
    authState,
    userdata,
    setUserdata,
    AllListsData,
    setRestrictedLimitModal,
  } = useContext(AuthContext);
  const {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  } = useLocationData();
  const { roleList, fetchRoleList } = useRoleList();

  const [loader, setLoader] = useState(false);
  const [generalTemplateData, setGeneralTemplateData] = useState([]);
  const [departmentTemplateData, setDepartmentTemplateData] = useState([]);
  const [departmentTemplateDataByID, setDepartmentTemplateDataByID] = useState(
    []
  );
  const [departmentTemByID, setDepartmentTempByID] = useState([]);
  const [editioContent, setEditorContent] = useState('');
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [ContractList, setContractList] = useState([]);
  const [AddContract, setAddContract] = useState();
  const [random, setRandom] = useState('');
  const [wageType, setWageType] = useState('fixed');
  const [contractTypeData, setContractTypeData] = useState([]);
  const [leaveTypeList, setLeaveTypeList] = useState([]);
  const [leaveTypeListall, setLeaveTypeListAll] = useState([]);
  const [holidayChanged, setHolidayChanged] = useState(false);
  const handleDatePickerOpen = () => {
    setDatePickerOpen(!datePickerOpen);
  };

  const handleDatePickerClose = () => {
    setDatePickerOpen(false);
  };
  // Reorder Duties in employee contract
  const onDragEnd = (result) => {
    const { source, destination } = result;
    const newData = ContractList;
    const [movedRow] = newData.splice(source?.index, 1);
    newData.splice(destination?.index, 0, movedRow);
    setContractList(newData);
  };
  const handleCreateOptionContractPolicy = async (inputValue) => {
    // setContractName(inputValue);
    let sendData = { contract_name: inputValue };
    try {
      const { status } = await axiosInstance.post(
        URLS?.ADD_USER_CONTRACT_TYPE,
        sendData
      );

      if (status === 200) {
        setLoader(false);
        getContractTypeDetails(inputValue);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);

      setLoader(false);
    }
  };
  // GET GENERAL TEMPLATE DETAILS
  const getGeneralTemplateDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FILE_LIST + `?type=general&status=active`
      );

      if (status === 200) {
        setLoader(false);

        const tempData = data?.data?.map((item) => {
          return {
            ...item,
            cat_id: item?.emp_contract_category?.id,
            cat_name: item?.emp_contract_category?.name,
            cat_type: item?.emp_contract_category?.type,
            dept_id: item?.emp_contract_category?.department_id,
            label: item?.name,
            value: item?.id,
          };
        });
        setGeneralTemplateData(tempData);
        tempData &&
          tempData?.[0] &&
          tempData?.[0]?.value &&
          formikRef.current.setFieldValue(
            'general_template',
            tempData?.[0]?.value
          );
      }
    } catch (error) {
      setLoader(false);
      setGeneralTemplateData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET DEPARTMENT TEMPLATE DETAILS
  const getDepartmentTemplateDetails = async (id) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FILE_LIST +
          `?type=department&status=active&department_id=${id ? id : ''}`
      );

      if (status === 200) {
        setLoader(false);

        const tempData = data?.data?.map((item) => {
          return {
            ...item,
            cat_id: item?.emp_contract_category?.id,
            cat_name: item?.emp_contract_category?.name,
            cat_type: item?.emp_contract_category?.type,
            dept_id: item?.emp_contract_category?.department_id,
            label: item?.name,
            value: item?.id,
          };
        });

        setDepartmentTemplateData(tempData);
        setDepartmentTempByID(tempData);
      }
    } catch (error) {
      setLoader(false);
      setDepartmentTemplateData([]);
      setDepartmentTempByID([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // GET DEPARTMENT TEMPLATE DETAILS By ID
  const getDepartmentTemplateDetailsByID = async (id) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_FILE_LIST +
          `?type=department&status=active&department_id=${id ? id : ''}`
      );

      if (status === 200) {
        setLoader(false);

        const tempData = data?.data?.map((item) => {
          return {
            ...item,
            cat_id: item?.emp_contract_category?.id,
            cat_name: item?.emp_contract_category?.name,
            cat_type: item?.emp_contract_category?.type,
            dept_id: item?.emp_contract_category?.department_id,
            label: item?.name,
            value: item?.id,
          };
        });

        setDepartmentTemplateDataByID(tempData);
      }
    } catch (error) {
      setLoader(false);
      setDepartmentTemplateDataByID([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Calculate Anual leave based on duration working hours
  const calculateLeave = (value, type) => {
    if (value) {
      const weeklyHours = type === 'month' ? value / 4.33 : value;
      const applicableWeeklyHours = Math.min(
        weeklyHours,
        parseFloat(authState?.max_limit_per_week)
      );
      const totalLeaveHours =
        applicableWeeklyHours * parseFloat(authState?.base_leave);

      // Calculate leave in days, using 7 as divisor or returning total leave hours if 7 is zero
      const leaveInDays = Math.ceil(totalLeaveHours / 7) || totalLeaveHours;

      formikRef.current.setFieldValue(
        'days',
        !Number.isNaN(leaveInDays) && !isNaN(parseFloat(leaveInDays))
          ? leaveInDays
          : ''
      );
    } else {
      formikRef.current.setFieldValue('days', '');
    }
  };
  const submitEmpContract = async (requestData, userId) => {
    const calculateExpiryDate = (startDate, duration) => {
      const date = moment(startDate ? startDate : new Date());

      switch (duration) {
        case '2_month':
          return date.add(2, 'months').format('YYYY-MM-DD');
        case '4_month':
          return date.add(4, 'months').format('YYYY-MM-DD');
        case '6_month':
          return date.add(6, 'months').format('YYYY-MM-DD');
        case '1_year':
          return date.add(1, 'year').format('YYYY-MM-DD');
        case 'custom':
          return null;
        default:
          return date.format('YYYY-MM-DD');
      }
    };
    const getExpDate =
      requestData?.expire_duration === 'custom'
        ? requestData?.expire_date
        : requestData?.expire_duration
          ? calculateExpiryDate(
              dayjs(requestData?.joiningdate).format('YYYY-MM-DD'),
              requestData?.expire_duration
            )
          : null;
    const selctedcontract = ContractList?.map((d) => d?.id);

    // const result = [requestData?.department_template, ...selctedcontract].join(
    //   ','
    // );
    const selctedpolicyIds =
      requestData?.policyholiday &&
      requestData?.policyholiday?.length > 0 &&
      requestData?.policyholiday?.map((d) => d?.id);
    let sendData = {
      general_template: requestData?.general_template,
      department_template: requestData?.department_template,
      additional_template: selctedcontract.join(','),
      // leave_policy_id: requestData?.leave_policy_id,
      contract_name_id: requestData?.contractName
        ? requestData?.contractName
        : null,
      duration_type: requestData?.duration_type
        ? requestData?.duration_type
        : null,
      working_hours: requestData?.working_hours,
      wage_type: requestData?.wage_type,
      ...(requestData?.wage_type === 'fixed' && {
        fixed_types: requestData?.wage_amount_type,
      }),
      wages_hours: requestData?.wage_per_hour,
      contract_remark: requestData?.contractRemark
        ? requestData?.contractRemark
        : null,
      leave_remark: requestData?.leaveRemark ? requestData?.leaveRemark : null,
      // leave_type_id: requestData?.holidayEntitlement
      //   ? requestData?.holidayEntitlement
      //   : null,
      place_of_work: requestData?.place_of_work
        ? requestData?.place_of_work
        : null,
      has_holiday_entitlement: requestData?.has_holiday_entitlement,
      leave_days:
        requestData?.has_holiday_entitlement && requestData?.days
          ? requestData?.days
          : null,
      tips_grade: requestData?.tipsgrad ? requestData?.tipsgrad : null,
      // start_date: dayjs(requestData?.start_date).format('YYYY-MM-DD'),
      leave_policy_ids:
        selctedpolicyIds && selctedpolicyIds?.length > 0
          ? selctedpolicyIds.toString()
          : null,
      // working_hour_per_day: requestData?.working_hours_per_day,
      // max_limit_per_week: requestData?.max_limit_per_week,
      expire_duration: requestData?.expire_duration,
      ...(getExpDate && {
        expire_date: dayjs(getExpDate).format('YYYY-MM-DD'),
      }),
      other: editioContent,
      user_id: userId,
      probation_length: requestData?.probationperiod
        ? requestData?.probationperiod
        : null,
    };
    try {
      setLoader(true);
      const { status } = await axiosInstance.put(
        URLS?.UPDATE_USER_CONTRACT,
        sendData
      );

      if (status === 200 || status === 201) {
        setLoader(false);
        setTimeout(() => {
          router.push('/staff');
        }, 700);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // GET CONTRACT TYPE DETAILS
  const getContractTypeDetails = async (name) => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_CONTRACT_TYPE //+ `?status=active`
      );

      if (status === 200) {
        setLoader(false);

        const tempData = data?.data?.map((item) => {
          return {
            ...item,
            label: item?.contract_name,
            value: item?.id,
          };
        });
        setContractTypeData(tempData);

        if (name) {
          const findname = tempData?.find((f) => f?.label === name);
          // setSelectedCon(findname?.value);
          formikRef.current.setFieldValue('contractName', findname?.value);
          // setSelectedContract(findname);
        }
      }
    } catch (error) {
      setLoader(false);
      setContractTypeData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getLeaveTypeList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_LEAVE_TYPE + `?search=`
      );
      if (status === 200 || status === 201) {
        const tempData = data?.data?.map((item) => {
          const options = item?.leave_accural_policy?.map((sub) => {
            return {
              ...sub,
              label: sub?.leave_policy_name,
              value: sub?.id,
              parent_id: item?.id,
            };
          });
          return {
            ...item,
            label: item?.name,
            options: options,
          };
        });

        if (tempData && tempData?.length > 0) {
          const filteredOptions = tempData?.flatMap((leave) =>
            leave.options.filter((option) => option.has_leave_policy_default)
          );
          formikRef.current.setFieldValue('policyholiday', filteredOptions);
          const parentIds = filteredOptions?.map((policy) => policy?.parent_id);
          const filteredLeaveTypes = tempData?.filter(
            (type) => !parentIds.includes(type?.id)
          );
          setLeaveTypeList(filteredLeaveTypes);
          setLeaveTypeListAll(tempData);
        }
      }
    } catch (error) {
      // setLoader(false);
      setLeaveTypeList([]);
      setLeaveTypeListAll([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (authState?.remaining_emp === 0) {
      setRestrictedLimitModal('staff');
    }
  }, [authState?.remaining_emp]);

  useEffect(() => {
    if ([1, 2].includes(authState?.UserPermission?.staff)) {
      fetchRoleList(true);
      getGeneralTemplateDetails();
      getDepartmentTemplateDetails();
      getDepartmentTemplateDetailsByID();
      getLeaveTypeList();
      getContractTypeDetails();
    }
  }, [authState?.UserPermission?.staff]);
  const oldStaff = fetchFromStorage(identifiers?.RedirectData)
    ? fetchFromStorage(identifiers?.RedirectData)
    : userdata;

  useEffect(() => {
    if (oldStaff?.IsFromUser === undefined && oldStaff?.page !== undefined) {
      setUserdata({ ...userdata, IsFromUser: true });
      saveToStorage(identifiers?.RedirectData, {
        ...oldStaff,
        IsFromUser: true,
      });
    }
  }, [oldStaff]);

  useEffect(() => {
    ContractList && setContractList(ContractList);
  }, [random]);

  const scrollToError = (errors) => {
    const firstErrorField = Object.keys(errors)[0];

    // Try to find the element by ID first
    let element = document.getElementById(firstErrorField);

    // If not found by ID, try other selectors
    if (!element) {
      element = document.querySelector(`[name="${firstErrorField}"]`);
    }

    // If still not found or it's a hidden input, try to find the parent container
    if (!element || element.type === 'hidden') {
      const selectBox = document.querySelector(
        `.select-box[data-field="${firstErrorField}"]`
      );
      const errorField = document.querySelector(
        `.textfeild-error[data-field="${firstErrorField}"]`
      );
      const customField = document.querySelector(
        `[data-field="${firstErrorField}"]`
      );

      element = selectBox || errorField || customField;
    }
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      // For MultiSelect, focus the select input if it exists
      const selectInput = element.querySelector('input') || element;
      selectInput.focus();
    }
  };

  return (
    <Box className="create-staff-section">
      <Box className="d-flex align-center">
        <ArrowBackIosIcon
          className="cursor-pointer mt4"
          onClick={() => {
            setTimeout(() => {
              router.push('/staff');
            }, 1000);
          }}
        />
        <Typography className="title-sm pr8">Create staff</Typography>
      </Box>
      <Divider className="mb16 mt16" />
      <Box className="d-flex pt8">
        <Typography className="title-sm pr8">User Profile</Typography>
      </Box>
      <Formik
        innerRef={formikRef}
        initialValues={{
          firstname: '',
          lastname: '',
          mname: '',
          email: '',
          nationality: '',
          country: '',
          city: '',
          county: '',
          pincode: '',
          phoneNo: '',
          birthdate: null,
          branchname: '',
          departmentname: '',
          branches: [],
          // gdesignation:
          //   isUpdate && updateItem?.user_designation
          //     ? updateItem?.user_designation
          //     : '',
          designation: [],
          joiningdate: null,
          homeaddress1: '',
          homeaddress2: '',
          gender: '',
          marital: '',
          general_template: '',
          department_template: '',
          expire_duration: '',
          expire_date: null,
          // start_date:
          //   isUpdate && updateItem?.start_date
          //     ? dayjs(updateItem?.start_date).format('YYYY-MM-DD')
          //     : dayjs(new Date()).format('YYYY-MM-DD'),

          tipsgrad: '',
          contractName: '',
          duration_type: 'week',
          working_hours: '',
          wage_type: 'fixed',
          wage_amount_type: '',
          wage_per_hour: '',
          contractRemark: '',
          days: '',
          // holidayEntitlement: '',
          place_of_work: '',
          leaveRemark: '',
          probationperiod: '',
          has_holiday_entitlement: true,
          // working_hours_per_day: '',
          // max_limit_per_week: '',
          policyholiday: [],
        }}
        enableReinitialize={true}
        validationSchema={Yup.object().shape({
          firstname: Yup.string().trim().required('This field is required'),
          lastname: Yup.string().trim().required('This field is required'),
          email: Yup.string()
            .required('This field is required')
            .matches(
              /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
              'Please enter valid email'
            ),
          phoneNo: Yup.string()
            .trim()
            .matches(/^[0-9]{10,11}$/, 'Please enter valid phone number'),
          joiningdate: Yup.string()
            .nullable()
            .required('This field is required'),
          branchname: Yup.string()
            .trim()
            .test(
              'required-when-not-admin',
              'This field is required',
              function (value) {
                const { designation } = this.parent;
                if (!designation) return true;

                const designationValues = designation.map((d) => d.value);
                const hasAdminRole = designationValues.every((val) =>
                  HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                );

                // If has admin role (1,2,3), not required
                if (hasAdminRole) return true;

                // Otherwise required
                return !!value;
              }
            ),
          departmentname: Yup.string()
            .trim()
            .test(
              'required-when-not-admin',
              'This field is required',
              function (value) {
                const { designation } = this.parent;
                if (!designation) return true;

                const designationValues = designation.map((d) => d.value);
                const hasAdminRole = designationValues.every((val) =>
                  HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                );

                // If has admin role (1,2,3), not required
                if (hasAdminRole) return true;

                // Otherwise required
                return !!value;
              }
            ),
          designation: Yup.array().min(1, 'At least one role must be selected'),
          branches: Yup.array()
            .test(
              'required-when-designation-5',
              'At least one branch must be selected',
              function (value) {
                const { designation, branchname } = this.parent; // Access other field values
                if (
                  designation &&
                  designation?.map((r) => r?.value).includes(5) &&
                  !branchname
                ) {
                  return Array.isArray(value) && value.length > 0;
                }
                return true; // Pass validation if the condition is not met
              }
            )
            .required('Branches is required'),
          contractName: Yup.string().trim().required('This field is required'),
          duration_type: Yup.string().trim().required('This field is required'),
          working_hours: Yup.number()
            .required('This field is required')
            .typeError('Must be a number'),
          wage_type: Yup.string().trim().required('This field is required'),
          wage_per_hour: Yup.number()
            .required('This field is required')
            .typeError('Must be a number'),
          wage_amount_type:
            wageType === 'fixed' &&
            Yup.string().trim().required('This field is required'),
          days: Yup.lazy((value, context) => {
            const has_holiday_entitlement =
              context.parent.has_holiday_entitlement;
            return has_holiday_entitlement
              ? Yup.number().nullable().required('This field is required')
              : Yup.number().nullable().notRequired();
          }),
          leaveRemark:
            holidayChanged &&
            Yup.string().trim().required('This field is required'),
          probationperiod: Yup.number()
            .nullable()
            .test(
              'probationperiod-less-than-expiry-diff',
              'Probation period must be less than the difference between expiry date and today.',
              function (value) {
                const { expire_duration, expire_date } = this.parent;
                let expiry;

                if (expire_duration === 'custom' && expire_date) {
                  expiry = dayjs(expire_date);
                } else if (expire_duration) {
                  switch (expire_duration) {
                    case '2_month':
                      expiry = dayjs().add(2, 'month');
                      break;
                    case '4_month':
                      expiry = dayjs().add(4, 'month');
                      break;
                    case '6_month':
                      expiry = dayjs().add(6, 'month');
                      break;
                    case '1_year':
                      expiry = dayjs().add(1, 'year');
                      break;
                    default:
                      expiry = null;
                  }
                }

                if (!expiry || !value) return true; // skip if not enough info
                const today = dayjs().startOf('day');
                const diff = expiry.diff(today, 'day'); // works now
                return value <= diff;
              }
            ),
          expire_date: Yup.lazy((value, context) => {
            const expire_duration = context.parent.expire_duration;
            return expire_duration === 'custom'
              ? Yup.string().nullable().required('This field is required')
              : Yup.string().nullable().notRequired();
          }),
          general_template: Yup.string()
            .trim()
            .required('This field is required'),
          // start_date: Yup.string().trim().required('This field is required'),
          // expire_duration: Yup.string()
          //   .trim()
          //   .required('This field is required'),
          department_template: Yup.string()
            .trim()
            .required('This field is required'),

          // working_hours_per_day: Yup.number()
          //   .typeError('Working hours per Day must be a number')
          //   .required('This field is required'),
          // max_limit_per_week: Yup.number()
          //   .typeError('Max Limit per Week must be a number')
          //   .required('This field is required'),

          // holidayEntitlement: Yup.string()
          //   .trim()
          //   .required('This field is required'),
        })}
        onSubmit={async (requestData) => {
          setLoader(true);
          const rid =
            requestData &&
            requestData?.designation.length > 0 &&
            requestData?.designation?.map((item) => item?.value);
          const branchesList = _.concat(
            AllListsData?.ActiveBranchList?.filter(
              (b) => b?.value === requestData?.branchname
            ),
            requestData?.branches?.filter(
              (f) => f?.value !== requestData?.branchname
            )
          );
          const bid =
            branchesList &&
            branchesList?.length > 0 &&
            branchesList?.map((item) => item?.value);
          let sendData = {
            user_first_name: requestData?.firstname,
            user_last_name: requestData?.lastname,
            ...(requestData?.mname && {
              user_middle_name: requestData?.mname,
            }),
            user_email: requestData?.email,
            ...(requestData?.homeaddress1 && {
              address_line1: requestData?.homeaddress1,
            }),
            ...(requestData?.homeaddress2 && {
              address_line2: requestData?.homeaddress2,
            }),
            ...(requestData?.nationality && {
              country: requestData?.nationality,
            }),
            ...(requestData?.country && {
              geo_country: requestData?.country,
            }),
            ...(requestData?.city && {
              geo_city: requestData?.city,
            }),
            ...(requestData?.county && {
              geo_state: requestData?.county,
            }),
            ...(requestData?.pincode && {
              pin_code: requestData?.pincode,
            }),
            ...(requestData?.phoneNo && {
              user_phone_number: requestData?.phoneNo.toString(),
            }),
            ...(requestData?.birthdate && {
              date_of_birth: dayjs(requestData?.birthdate).format('YYYY-MM-DD'),
            }),
            // ...(requestData?.gdesignation && {
            //   user_designation: requestData?.gdesignation
            // }),
            ...(requestData?.branchname && {
              branch_id: requestData?.branchname,
            }),
            ...(requestData?.branchname && {
              branch_id: requestData?.branchname,
            }),
            ...(requestData?.designation?.length > 0 &&
              rid?.length > 0 &&
              rid?.includes(5) &&
              requestData?.branches &&
              bid &&
              bid?.length > 0 && {
                assign_branch_ids: bid,
              }),
            ...(requestData?.departmentname && {
              department_id: requestData?.departmentname,
            }),
            ...(requestData?.gender &&
              (requestData?.gender === 'male' ||
                requestData?.gender == 'female') && {
                user_gender: requestData?.gender,
              }),
            ...(requestData?.gender &&
              requestData?.gender !== 'male' &&
              requestData?.gender !== 'female' && {
                user_gender_other: requestData?.gender,
              }),
            ...(requestData?.marital &&
              (requestData?.marital === 'single' ||
                requestData?.marital === 'married') && {
                marital_status: requestData?.marital,
              }),
            ...(requestData?.marital &&
              requestData?.marital !== 'single' &&
              requestData?.marital !== 'married' && {
                marital_status_other: requestData?.marital,
              }),
            ...(requestData?.joiningdate && {
              joining_date: dayjs(requestData?.joiningdate).format(
                'YYYY-MM-DD'
              ),
            }),
            ...(requestData?.designation &&
              rid &&
              rid?.length > 0 && {
                role_ids: rid,
              }),
          };
          const ApiUrl = URLS.CREATE_USER;
          const method = 'post';
          try {
            const { status, data } = await axiosInstance[method](
              ApiUrl,
              sendData
            );

            if (status === 200) {
              if (data?.status) {
                const userId = data?.data?.id;
                submitEmpContract(requestData, userId);
                setApiMessage('success', data?.message);
              } else {
                setApiMessage('error', data?.message);
              }
              setLoader(false);
            }
          } catch (error) {
            setLoader(false);
            setApiMessage('error', error?.response?.data?.message);
          }
        }}
      >
        {({
          errors,
          touched,
          handleBlur,
          values,
          handleSubmit,
          handleChange,
          setFieldValue,
        }) => (
          <Form onSubmit={handleSubmit}>
            <Box className="display-grid pt16">
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="firstname"
                  name="firstname"
                  value={values?.firstname}
                  label="First name"
                  required
                  placeholder="Enter firstname"
                  error={Boolean(touched.firstname && errors.firstname)}
                  helperText={touched.firstname && errors.firstname}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="mname"
                  name="mname"
                  value={values?.mname}
                  label="Middle name"
                  placeholder="Enter middle name"
                  error={Boolean(touched.mname && errors.mname)}
                  helperText={touched.mname && errors.mname}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="lastname"
                  name="lastname"
                  value={values?.lastname}
                  label="Last name"
                  required
                  placeholder="Enter lastname"
                  error={Boolean(touched.lastname && errors.lastname)}
                  helperText={touched.lastname && errors.lastname}
                  onBlur={handleBlur}
                  onChange={handleChange}
                  inputProps={{ maxLength: TextFieldMaxLength }}
                />
              </Box>
              <Box className="">
                <CustomTextField
                  fullWidth
                  id="email"
                  name="email"
                  value={values?.email}
                  label="Email address"
                  required
                  placeholder="Enter Email address"
                  error={Boolean(touched.email && errors.email)}
                  helperText={touched.email && errors.email}
                  onBlur={handleBlur}
                  onChange={handleChange}
                />
              </Box>

              <Box className="phone-number ">
                <CustomTextField
                  fullWidth
                  id="phoneNo"
                  name="phoneNo"
                  value={values?.phoneNo}
                  label="Phone number"
                  placeholder="Enter Phone number"
                  error={Boolean(touched.phoneNo && errors.phoneNo)}
                  helperText={touched.phoneNo && errors.phoneNo}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    if (e.target.value === '' || e.target.value?.length < 12) {
                      handleChange(e);
                    }
                  }}
                  onInput={(e) => {
                    e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                  }}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label={<span>Date of birth</span>}
                  error={Boolean(
                    !values?.birthdate && touched.birthdate && errors.birthdate
                  )}
                  helperText={
                    !values?.birthdate && touched.birthdate && errors.birthdate
                  }
                  name="birthdate"
                  value={dayjs(values?.birthdate)}
                  onBlur={handleBlur}
                  OpenDialog={handleDatePickerOpen}
                  CloseDialog={handleDatePickerClose}
                  onChange={(date) => {
                    setFieldValue('birthdate', date);
                  }}
                  inputVariant="outlined"
                  disableFuture={true}
                  format="DD/MM/YYYY"
                  maxDate={dayjs().subtract(18, 'year')}
                />
              </Box>
              <Box>
                <CustomSelect
                  placeholder="Nationality"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.nationality;
                    }) || ''
                  }
                  name="nationality"
                  error={touched.nationality && errors.nationality}
                  onChange={(e) => {
                    setFieldValue('nationality', e?.value);
                  }}
                  label={<span>Nationality</span>}
                />
              </Box>
              <Box>
                <CustomSelect
                  label={
                    <span className="field-info">
                      Country of Residence
                      <Tooltip
                        title="Current Address"
                        placement="right"
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                        arrow
                      >
                        <InfoIcon
                          sx={{ marginLeft: '8px' }}
                          className="info-icon cursor-poniter"
                        />
                      </Tooltip>
                    </span>
                  }
                  name="country"
                  placeholder="Country of Residence"
                  options={countries}
                  value={
                    countries?.find((opt) => {
                      return opt?.value === values?.country;
                    }) || ''
                  }
                  onChange={(e) => {
                    setFieldValue('country', e?.value);
                    setFieldValue('county', '');
                    setFieldValue('city', '');
                    setCities([]);
                    setCounties([]);
                    setSelectedCountry(e?.value);
                  }}
                  error={touched?.country && errors?.country}
                  helperText={touched?.country && errors?.country}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomSelect
                  label={<span>County</span>}
                  name="county"
                  placeholder="County"
                  options={counties}
                  value={
                    counties?.find((opt) => {
                      return opt?.value === values?.county;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('county', selectedOption?.value);
                    setFieldValue('city', '');
                    setSelectedCounty(selectedOption?.value);
                  }}
                  error={touched?.county && errors?.county}
                  helperText={touched?.county && errors?.county}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomSelect
                  label={<span>City</span>}
                  name="city"
                  placeholder="City"
                  options={cities}
                  value={
                    cities?.find((opt) => {
                      return opt?.value === values?.city;
                    }) || ''
                  }
                  onChange={(selectedOption) => {
                    setFieldValue('city', selectedOption?.value || '');
                  }}
                  error={touched?.city && errors?.city}
                  helperText={touched?.city && errors?.city}
                  startAdornment={<LocationOn />}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="pincode"
                  name="pincode"
                  value={values?.pincode}
                  label="Post Code"
                  placeholder="Enter Post code"
                  error={Boolean(touched.pincode && errors.pincode)}
                  helperText={touched.pincode && errors.pincode}
                  onBlur={handleBlur}
                  onChange={(e) => {
                    if (
                      e?.target?.value === '' ||
                      e?.target?.value?.length < 9
                    ) {
                      handleChange(e);
                    }
                  }}
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label={<span>Joining date</span>}
                  name="joiningdate"
                  value={dayjs(values?.joiningdate)}
                  error={Boolean(
                    !values?.joiningdate &&
                      touched.joiningdate &&
                      errors.joiningdate
                  )}
                  helperText={
                    !values?.joiningdate &&
                    touched.joiningdate &&
                    errors.joiningdate
                  }
                  onBlur={handleBlur}
                  onChange={(date) => {
                    setFieldValue('joiningdate', date);
                    setFieldValue('expire_date', '');
                  }}
                  OpenDialog={handleDatePickerOpen}
                  CloseDialog={handleDatePickerClose}
                  inputVariant="outlined"
                  required
                  format="DD/MM/YYYY"
                />
              </Box>
            </Box>
            <Box className="display-grid-50 space-grid">
              <Box>
                <CustomTextField
                  fullWidth
                  id="homeaddress1"
                  name="homeaddress1"
                  value={values?.homeaddress1}
                  label="Address line 1"
                  placeholder="Enter Address"
                  error={Boolean(touched.homeaddress1 && errors.homeaddress1)}
                  helperText={touched.homeaddress1 && errors.homeaddress1}
                  onBlur={handleBlur}
                  onChange={handleChange}
                />
              </Box>
              <Box>
                <CustomTextField
                  fullWidth
                  id="homeaddress2"
                  name="homeaddress2"
                  value={values?.homeaddress2}
                  label="Address line 2"
                  placeholder="Enter Address"
                  error={Boolean(touched.homeaddress2 && errors.homeaddress2)}
                  helperText={touched.homeaddress2 && errors.homeaddress2}
                  onBlur={handleBlur}
                  onChange={handleChange}
                />
              </Box>
            </Box>
            <Box className="space-grid">
              <Gender
                keyName="gender"
                setFieldValue={setFieldValue}
                isRequire={false}
                keyValue={values?.gender}
              />
            </Box>
            <Box className="space-grid">
              <MaritalStatus
                keyName="marital"
                setFieldValue={setFieldValue}
                isRequire={false}
                keyValue={values?.marital}
              />
            </Box>
            <Box className="display-grid space-grid">
              <Box className="">
                <CustomSelect
                  placeholder="Branch name"
                  options={AllListsData?.ActiveBranchList}
                  value={
                    AllListsData?.ActiveBranchList?.find((opt) => {
                      return opt?.value === values?.branchname;
                    }) || ''
                  }
                  name="branchname"
                  error={
                    !values?.branchname &&
                    touched.branchname &&
                    errors.branchname
                  }
                  helperText={
                    !values?.branchname &&
                    touched.branchname &&
                    errors.branchname
                  }
                  onChange={(e) => {
                    setFieldValue('branchname', e?.value || '');
                  }}
                  label={<span>Branch name</span>}
                  showDot={true}
                  required={
                    values?.designation?.length > 0 &&
                    !values?.designation
                      ?.map((d) => d?.value)
                      ?.every((val) =>
                        HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                      )
                  }
                />
              </Box>
              <Box>
                <CustomSelect
                  placeholder="Department name"
                  options={AllListsData?.ActiveDepartmentList}
                  value={
                    AllListsData?.ActiveDepartmentList?.find((opt) => {
                      return opt?.value === values?.departmentname;
                    }) || ''
                  }
                  name="departmentname"
                  error={
                    !values?.departmentname &&
                    touched.departmentname &&
                    errors.departmentname
                  }
                  helperText={
                    !values?.departmentname &&
                    touched.departmentname &&
                    errors.departmentname
                  }
                  onChange={(e) => {
                    getDepartmentTemplateDetailsByID(e?.value);
                    setFieldValue('departmentname', e?.value || '');
                    setFieldValue('department_template', '');
                    setContractList([]);
                  }}
                  required={
                    values?.designation?.length > 0 &&
                    !values?.designation
                      ?.map((d) => d?.value)
                      ?.every((val) =>
                        HIGH_LEVEL_MANAGER_ROLE_IDS?.includes(val)
                      )
                  }
                  label={<span>Department name</span>}
                />
              </Box>
            </Box>
            <Box className="display-grid-50 space-grid">
              <Box>
                <MultiSelect
                  placeholder="System Access"
                  options={roleList}
                  value={values?.designation}
                  error={touched.designation && errors.designation}
                  helperText={touched.designation && errors.designation}
                  onChange={(e) => setFieldValue('designation', e)}
                  name="designation"
                  required
                  label={<span>System Access</span>}
                />
              </Box>
              {values?.designation &&
                values?.designation?.length > 0 &&
                values?.designation?.map((m) => m?.value)?.includes(5) && (
                  <Box className="select-box ">
                    <MultiSelect
                      placeholder="Assign branches"
                      options={AllListsData?.ActiveBranchList}
                      value={_.concat(
                        AllListsData?.ActiveBranchList?.filter(
                          (b) => b?.value === values?.branchname
                        ),
                        values?.branches?.filter(
                          (f) => f?.value !== values?.branchname
                        )
                      )}
                      error={touched.branches && errors.branches}
                      helperText={touched.branches && errors.branches}
                      className={
                        touched.branches && errors.branches
                          ? 'textfeild-error'
                          : ''
                      }
                      isAreaManager={true}
                      isOptionWithColor={true}
                      onChange={(e) => {
                        setFieldValue(
                          'branches',
                          e?.filter((f) => f?.value !== values?.branchname)
                        );
                      }}
                      name="branches"
                      label={<span>Assign branches</span>}
                      required
                    />
                  </Box>
                )}
            </Box>

            <Box className="pt24">
              <Divider className="mb8" />
              <Box className="d-flex pt8">
                <Typography className="title-sm pr8">
                  Employee Contract
                </Typography>
              </Box>
              <Box className="display-grid pt16">
                <Box>
                  <CustomCreateSelect
                    id="contractName"
                    name="contractName"
                    value={
                      contractTypeData?.find(
                        (option) => option.value === values?.contractName
                      ) || null
                    }
                    options={contractTypeData}
                    onChange={(inputValue) => {
                      setFieldValue('contractName', inputValue?.value);
                      // const sel = contractTypeData?.find(
                      //   (f) => f?.id === inputValue?.value
                      // );
                      // setSelectedContract(sel);
                      // setSelectedCon();
                    }}
                    onCreateOption={(inputValue) => {
                      handleCreateOptionContractPolicy(inputValue);
                      // setFieldValue('contractName', inputValue);
                    }}
                    placeholder="Contract Type"
                    onBlur={handleBlur}
                    label="Contract Type"
                    error={Boolean(touched.contractName && errors.contractName)}
                    helperText={touched.contractName && errors.contractName}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="Duration Type"
                    options={identifiers?.DURATION_TYPE}
                    value={
                      identifiers?.DURATION_TYPE?.find((opt) => {
                        return opt?.value === values?.duration_type;
                      }) || ''
                    }
                    name="duration_type"
                    error={touched.duration_type && errors.duration_type}
                    helperText={touched.duration_type && errors.duration_type}
                    onChange={(selectedOption) => {
                      setFieldValue(
                        'duration_type',
                        selectedOption?.value || ''
                      );
                      calculateLeave(
                        values?.working_hours,
                        selectedOption?.value
                        // values?.max_limit_per_week
                      );
                    }}
                    label={<span>Duration Type</span>}
                    required
                  />
                </Box>
                <Box>
                  <CustomTextField
                    fullWidth
                    id="working_hours"
                    name="working_hours"
                    value={values?.working_hours}
                    label="Duration Working Hours"
                    required
                    placeholder="Duration Working Hours"
                    error={Boolean(
                      touched.working_hours && errors.working_hours
                    )}
                    helperText={touched.working_hours && errors.working_hours}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                      calculateLeave(
                        e?.target?.value,
                        values?.duration_type
                        // values?.max_limit_per_week
                      );
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>{' '}
                {/* <Box className="">
                      <CustomTextField
                        InputLabelProps={{
                          shrink: true,
                        }}
                        fullWidth
                        id="working_hours_per_day"
                        name="working_hours_per_day"
                        value={values?.working_hours_per_day}
                        label="Working hours per Day *"
                        variant="filled"
                        placeholder="Enter Working hours per Day"
                        error={Boolean(
                          touched.working_hours_per_day &&
                            errors.working_hours_per_day
                        )}
                        helperText={
                          touched.working_hours_per_day &&
                          errors.working_hours_per_day
                        }
                        onBlur={handleBlur}
                        onChange={(e) => {
                          handleChange(e);
                        }}
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9.]/g,
                            ''
                          );
                        }}
                      />
                    </Box>
                    <Box className="">
                      <CustomTextField
                        InputLabelProps={{
                          shrink: true,
                        }}
                        fullWidth
                        id="max_limit_per_week"
                        name="max_limit_per_week"
                        value={values?.max_limit_per_week}
                        label=" Max Limit per Week *"
                        variant="filled"
                        placeholder="Enter Max Limit per Week"
                        error={Boolean(
                          touched.max_limit_per_week &&
                            errors.max_limit_per_week
                        )}
                        helperText={
                          touched.max_limit_per_week &&
                          errors.max_limit_per_week
                        }
                        onBlur={handleBlur}
                        onChange={(e) => {
                          handleChange(e);
                          calculateLeave(
                            values?.working_hours,
                            values?.duration_type,
                            e?.target?.value
                          );
                        }}
                        onInput={(e) => {
                          e.target.value = e.target.value.replace(
                            /[^0-9.]/g,
                            ''
                          );
                        }}
                      />
                    </Box> */}
                <Box>
                  <CustomSelect
                    placeholder="Wage Type"
                    options={identifiers?.WAGE_TYPE}
                    value={
                      identifiers?.WAGE_TYPE?.find((opt) => {
                        return opt?.value === values.wage_type;
                      }) || ''
                    }
                    name="wage_type"
                    onChange={(selectedOption) => {
                      const selectedValue = selectedOption?.value;
                      setFieldValue('wage_type', selectedValue);
                      setWageType(selectedValue);
                    }}
                    label="Wage Type"
                    error={touched?.wage_type && errors?.wage_type}
                    helperText={touched?.wage_type && errors?.wage_type}
                    required
                  />
                </Box>
                <Box>
                  <CustomTextField
                    fullWidth
                    id="wage_per_hour"
                    name="wage_per_hour"
                    value={values?.wage_per_hour}
                    label={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount'
                        : 'Wage Per Hours'
                    }
                    required
                    placeholder={
                      values?.wage_type === 'fixed'
                        ? 'Fixed amount'
                        : 'Wage Per Hours'
                    }
                    error={Boolean(
                      touched.wage_per_hour && errors.wage_per_hour
                    )}
                    helperText={touched.wage_per_hour && errors.wage_per_hour}
                    onBlur={handleBlur}
                    onChange={(e) => {
                      handleChange(e);
                    }}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>
                {values?.wage_type === 'fixed' && (
                  <Box>
                    <CustomSelect
                      placeholder="Amount per"
                      options={identifiers?.WAGE_AMOUNT_TYPE}
                      value={
                        identifiers?.WAGE_AMOUNT_TYPE?.find((opt) => {
                          return opt?.value === values?.wage_amount_type;
                        }) || ''
                      }
                      name="wage_amount_type"
                      error={
                        touched.wage_amount_type && errors.wage_amount_type
                      }
                      helperText={
                        touched?.wage_amount_type && errors?.wage_amount_type
                      }
                      onChange={(selectedOption) => {
                        setFieldValue(
                          'wage_amount_type',
                          selectedOption?.value || ''
                        );
                      }}
                      label={<span>Amount per</span>}
                      required
                    />
                  </Box>
                )}
              </Box>

              <Box className="display-grid-50 space-grid">
                <CustomTextField
                  fullWidth
                  id="contractRemark"
                  name="contractRemark"
                  value={values?.contractRemark}
                  label="Contract Remark"
                  placeholder="Contract Remark"
                  onBlur={handleBlur}
                  onChange={handleChange}
                />
              </Box>
              <Box className="mt16">
                <Typography className="sub-content-text">
                  Do you want assign holiday entitlement?
                </Typography>
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value={true}
                    checked={values?.has_holiday_entitlement}
                    onChange={() =>
                      setFieldValue('has_holiday_entitlement', true)
                    }
                    disableRipple
                    label={
                      <Typography className="sub-title-text">Yes</Typography>
                    }
                  />
                </Box>
                <Box>
                  <CustomRadio
                    name="considerLeaveAccrual"
                    value={false}
                    checked={!values?.has_holiday_entitlement}
                    onChange={() =>
                      setFieldValue('has_holiday_entitlement', false)
                    }
                    disableRipple
                    label={
                      <Typography className="sub-title-text">No</Typography>
                    }
                  />
                </Box>
              </Box>
              <Box className="display-grid space-grid">
                {values?.has_holiday_entitlement ? (
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="days"
                      name="days"
                      required
                      value={values?.days}
                      label={<span>Holiday Entitlement Days </span>}
                      labelIcon={
                        <Tooltip
                          title="Change"
                          placement="right"
                          classes={{
                            tooltip: 'info-tooltip-container ',
                          }}
                          arrow
                        >
                          <DriveFileRenameOutlineIcon
                            sx={{ marginLeft: '8px' }}
                            className="label-icon"
                            onClick={() => {
                              setHolidayChanged(true);
                            }}
                          />
                        </Tooltip>
                      }
                      placeholder="Holiday Entitlement Days"
                      error={Boolean(touched.days && errors.days)}
                      helperText={touched.days && errors.days}
                      onBlur={handleBlur}
                      onChange={(e) => {
                        handleChange(e);
                      }}
                      disabled={!holidayChanged}
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9.]/g, ''); // Restrict input to numbers only
                      }}
                    />
                  </Box>
                ) : (
                  <></>
                )}

                <Box>
                  <CustomTextField
                    fullWidth
                    id="place_of_work"
                    name="place_of_work"
                    value={values.place_of_work}
                    label="Place of work" // Ensure correct type usage
                    placeholder="Place of work"
                    error={Boolean(
                      touched.place_of_work && errors.place_of_work
                    )}
                    helperText={touched.place_of_work && errors.place_of_work}
                    onBlur={handleBlur}
                    onChange={handleChange}
                  />
                </Box>
              </Box>
              <Box className="display-grid-50 space-grid">
                <Box>
                  {' '}
                  <CustomTextField
                    fullWidth
                    id="leaveRemark"
                    name="leaveRemark"
                    value={values?.leaveRemark}
                    label={'Leave Policy Remark'}
                    required={holidayChanged}
                    placeholder="Leave Policy Remark"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    error={Boolean(touched.leaveRemark && errors.leaveRemark)}
                    helperText={touched.leaveRemark && errors.leaveRemark}
                  />
                </Box>
                <Box>
                  <MultiselectTitles
                    placeholder="Assign policy"
                    options={leaveTypeList}
                    value={values?.policyholiday}
                    error={touched.policyholiday && errors.policyholiday}
                    helperText={touched.policyholiday && errors.policyholiday}
                    onChange={(e) => {
                      setFieldValue('policyholiday', e);
                      if (e && e?.length > 0) {
                        const parentIds = e?.map((policy) => policy?.parent_id);
                        const filteredLeaveTypes = leaveTypeListall?.filter(
                          (type) => !parentIds.includes(type?.id)
                        );
                        setLeaveTypeList(filteredLeaveTypes);
                      } else {
                        setLeaveTypeList(leaveTypeListall);
                      }
                    }}
                    name="policyholiday"
                    label={<span>Assign policy</span>}
                  />
                </Box>
              </Box>

              <Box className="display-grid space-grid">
                <Box>
                  <CustomTextField
                    fullWidth
                    id="tipsgrad"
                    name="tipsgrad"
                    value={values?.tipsgrad}
                    label={'TipsGrade'}
                    placeholder={'TipsGrade'}
                    // error={Boolean(touched.tipsgrad && errors.tipsgrad)}
                    // helperText={touched.tipsgrad && errors.tipsgrad}
                    onBlur={handleBlur}
                    // onChange={handleChange}
                    onChange={(e) => {
                      if (
                        e?.target.value === '' ||
                        e?.target.value?.length < 3
                      ) {
                        handleChange(e);
                      }
                    }}
                  />
                </Box>
                {/* <Box className="textfeild date-picker-textfield">
                      <CustomDatePicker
                        label={<span>Start Date</span>}
                        name="start_date"
                        value={dayjs(values?.start_date)}
                        className={
                          touched.start_date && errors.start_date
                            ? 'textfeild-error'
                            : ''
                        }
                        onBlur={handleBlur}
                        onChange={(date) => {
                          setFieldValue('start_date', date);
                        }}
                        inputVariant="outlined"
                      />
                      {touched.start_date && errors.start_date && (
                        <Typography
                          variant="body2"
                          color="error"
                          className="field-error"
                        >
                          {errors.start_date}
                        </Typography>
                      )}
                    </Box> */}
                <Box>
                  <CustomSelect
                    placeholder="Expiry Date"
                    options={identifiers?.EXPIRY_DATE_DURATION}
                    value={
                      identifiers?.EXPIRY_DATE_DURATION?.find((opt) => {
                        return opt?.value === values?.expire_duration;
                      }) || ''
                    }
                    name="expire_duration"
                    error={touched?.expire_duration && errors?.expire_duration}
                    helperText={
                      touched?.expire_duration && errors?.expire_duration
                    }
                    onChange={(e) => {
                      setFieldValue('expire_duration', e?.value);
                      setFieldValue('expire_date', null);
                    }}
                    label={<span>Expiry Date </span>}
                  />
                </Box>
                {values?.expire_duration === 'custom' && (
                  <Box>
                    <CustomDatePicker
                      label={<span>Select Expiry Date</span>}
                      error={Boolean(touched.expire_date && errors.expire_date)}
                      helperText={touched.expire_date && errors.expire_date}
                      name="expire_date"
                      value={dayjs(values?.expire_date)}
                      onBlur={handleBlur}
                      onChange={(date) => {
                        setFieldValue('expire_date', date);
                      }}
                      inputVariant="outlined"
                      minDate={dayjs(values?.joiningdate)}
                      required
                      format="DD/MM/YYYY"
                      disablePast
                    />
                  </Box>
                )}
                <Box>
                  <CustomTextField
                    fullWidth
                    id="probationperiod"
                    name="probationperiod"
                    value={values?.probationperiod}
                    label="Probation Period ( In Days )"
                    placeholder="90 Days"
                    error={Boolean(
                      touched.probationperiod && errors.probationperiod
                    )}
                    helperText={
                      touched.probationperiod && errors.probationperiod
                    }
                    onBlur={handleBlur}
                    onChange={handleChange}
                    onInput={(e) => {
                      e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                    }}
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="General"
                    options={generalTemplateData}
                    value={
                      generalTemplateData?.find((opt) => {
                        return opt?.value === values?.general_template;
                      }) || ''
                    }
                    name="general_template"
                    error={
                      touched?.general_template && errors?.general_template
                    }
                    helperText={
                      touched?.general_template && errors?.general_template
                    }
                    onChange={(e) => {
                      setFieldValue('general_template', e?.value);
                    }}
                    label={<span>General</span>}
                    required
                  />
                </Box>
                <Box>
                  <CustomSelect
                    placeholder="Job Role"
                    options={departmentTemplateDataByID}
                    value={
                      departmentTemplateDataByID?.find((opt) => {
                        return opt?.value === values?.department_template;
                      }) || ''
                    }
                    className={
                      touched.department_template && errors.department_template
                        ? 'textfeild-error'
                        : ''
                    }
                    name="department_template"
                    error={
                      touched?.department_template &&
                      errors?.department_template
                    }
                    helperText={
                      touched?.department_template &&
                      errors?.department_template
                    }
                    onChange={(e) => {
                      setFieldValue('department_template', e?.value);
                      const filterCat = departmentTemByID?.filter(
                        (f) => f?.id !== e?.value
                      );
                      setDepartmentTemplateData(filterCat);
                      const selectedCat = ContractList?.filter(
                        (fi) => fi?.id !== e?.value
                      );
                      setContractList(selectedCat);
                    }}
                    label={<span>Job Role</span>}
                    required
                  />
                </Box>
              </Box>
              <Box className="display-grid space-grid align-end">
                <Box>
                  <CustomSelect
                    placeholder="Additional Duties"
                    options={departmentTemplateData}
                    value={
                      departmentTemplateData?.find((opt) => {
                        return opt?.value === AddContract;
                      }) || ''
                    }
                    name="additional_duties"
                    onChange={(e) => {
                      setAddContract(e?.value);
                    }}
                    label={<span>Additional Duties</span>}
                  />
                </Box>
                <Box>
                  <CustomButton
                    variant="contained"
                    disabled={loader || !AddContract}
                    title="Add"
                    onClick={() => {
                      const clist = ContractList;
                      const selectedCat = departmentTemplateData?.find(
                        (f) => f?.id === AddContract
                      );
                      clist.push(selectedCat);
                      setContractList(clist);
                      const filterCat = departmentTemplateData?.filter(
                        (f) => f?.id !== AddContract
                      );
                      setDepartmentTemplateData(filterCat);
                      setRandom(Math.random());
                      setAddContract();
                    }}
                  />
                </Box>
              </Box>

              <Box className="employee-contract-section">
                <ListManager
                  items={ContractList || []}
                  direction="horizontal"
                  maxItems={1}
                  render={(item) => (
                    <Box key={item?.id} className="selected-files">
                      <Box className="file-name">
                        <Typography className="title-text text-ellipsis-line">
                          {item?.name}
                        </Typography>
                      </Box>
                      <CloseIcon
                        onClick={() => {
                          const files = departmentTemplateData;
                          files?.push(item);

                          setDepartmentTemplateData(files);
                          const selectedCat = ContractList?.filter(
                            (fi) => fi?.id !== item?.id
                          );
                          setContractList(selectedCat);
                        }}
                      />
                    </Box>
                  )}
                  onDragEnd={onDragEnd}
                />
              </Box>
              <Box className="pt16">
                <Typography className="field-label">Content</Typography>
                <CustomEditor
                  content={editioContent}
                  setContent={setEditorContent}
                  // height={300}
                />
              </Box>
            </Box>
            <Box className="pt24">
              <CustomButton
                type="submit"
                variant="contained"
                disabled={loader}
                onClick={async () => {
                  if (formikRef?.current) {
                    const errors = await formikRef.current.validateForm();
                    if (Object.keys(errors).length > 0) {
                      scrollToError(errors);
                      formikRef.current.setSubmitting(false);
                    }
                  }
                }}
                title={`${loader ? 'Creating...' : 'Create'}`}
              />
            </Box>
          </Form>
        )}
      </Formik>
    </Box>
  );
}
