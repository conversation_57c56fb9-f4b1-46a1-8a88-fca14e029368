import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import Image from 'next/image';
import './InstructionsCard.scss';

const InstructionsCard = ({ instructions }) => {
  return (
    <div className="instructions-card">
      <div className="instructions-card__header">
        <p className="instructions-card__title">
          <Icon name="List" size={20} color="currentColor" />
          <span>Cooking Instructions</span>
        </p>
      </div>

      <div className="instructions-card__content">
        <div className="instructions-card__list">
          {instructions?.map((instruction) => (
            <div key={instruction?.id} className="instructions-card__item">
              <div className="instructions-card__step-header">
                <div className="instructions-card__step-number">
                  <span>{instruction?.recipe_step_order}</span>
                </div>
                <div className="instructions-card__step-content">
                  <div className="instructions-card__image-wrap">
                    <Image
                      src={instruction?.item_detail?.item_link}
                      height={80}
                      width={80}
                      className="instructions-card__image"
                      alt="img"
                    />
                  </div>
                  <p className="instructions-card__step-text">
                    {instruction.recipe_step_description}
                  </p>

                  {/* <div className="instructions-card__details">
                    <div className="instructions-card__detail instructions-card__detail--timing">
                      <div className="instructions-card__detail-header">
                        <Icon
                          name="Clock"
                          size={14}
                          color="var(--color-warning)"
                        />
                        <span className="instructions-card__detail-label instructions-card__detail-label--timing">
                          Timing
                        </span>
                      </div>
                      <p className="instructions-card__detail-text">
                        {instruction.timing}
                      </p>
                    </div>

                    <div className="instructions-card__detail instructions-card__detail--equipment">
                      <div className="instructions-card__detail-header">
                        <Icon
                          name="Wrench"
                          size={14}
                          color="var(--color-success)"
                        />
                        <span className="instructions-card__detail-label instructions-card__detail-label--equipment">
                          Equipment
                        </span>
                      </div>
                      <p className="instructions-card__detail-text">
                        {instruction.equipment}
                      </p>
                    </div>
                  </div>

                  {instruction.chefNotes && (
                    <div className="instructions-card__note instructions-card__note--chef">
                      <div className="instructions-card__note-header">
                        <Icon
                          name="ChefHat"
                          size={14}
                          color="var(--color-primary)"
                          className="instructions-card__note-icon"
                        />
                        <span className="instructions-card__note-label instructions-card__note-label--chef">
                          Chef Notes:
                        </span>
                      </div>
                      <p className="instructions-card__note-text">
                        {instruction.chefNotes}
                      </p>
                    </div>
                  )}

                  {instruction.qualityCheck && (
                    <div className="instructions-card__note instructions-card__note--quality">
                      <div className="instructions-card__note-header">
                        <Icon
                          name="CheckCircle"
                          size={14}
                          color="var(--color-success)"
                          className="instructions-card__note-icon"
                        />
                        <span className="instructions-card__note-label instructions-card__note-label--quality">
                          Quality Check:
                        </span>
                      </div>
                      <p className="instructions-card__note-text">
                        {instruction.qualityCheck}
                      </p>
                    </div>
                  )} */}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default InstructionsCard;
