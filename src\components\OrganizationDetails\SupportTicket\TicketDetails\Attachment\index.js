'use client';
import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, Tooltip, Typography } from '@mui/material';
import CustomButton from '@/components/UI/button';
import { EmptAttachmentIcon } from '@/helper/common/images';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import PermMediaOutlinedIcon from '@mui/icons-material/PermMediaOutlined';
import PreviewModal from '../../CreateTicket/PreviewModal';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import './attachment.scss';
import Pohover from '../../Popover';

export default function Attachment() {
  const [mediaFiles, setMediaFiles] = useState([]);
  const [previewMedia, setPreviewMedia] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);

  const handlePopoverOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const handlePreviewClick = (preview) => {
    if (preview?.type === 'image') {
      setPreviewMedia(preview?.preview);
    } else {
      setPreviewMedia(preview);
    }
    setOpenModal(true);
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles.length) {
        const newMedia = acceptedFiles?.map((file) => ({
          file,
          name: file?.name,
          preview: URL.createObjectURL(file),
          type: file?.type.split('/')[0],
        }));
        setMediaFiles((prevFiles) => [...prevFiles, ...newMedia]);
      }
    },
    accept: 'image/*,video/*',
    multiple: true,
    noClick: true,
  });

  const removeMedia = (index) => {
    setMediaFiles((prevFiles) => prevFiles?.filter((_, i) => i !== index));
  };

  const handleDownload = (fileUrl, fileName) => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.click();
  };

  return (
    <Box className="attachment-wrap d-flex flex-col align-center justify-center text-align">
      {mediaFiles.length === 0 ? (
        <Box className="empty-attachment">
          <EmptAttachmentIcon />
          <Box className="attachment-text-wrap">
            <Typography component="p" className="no-attachment">
              No Attachments available
            </Typography>
            <Typography component="p" className="upload-attachment">
              Upload attachments to add more context to this Ticket.
            </Typography>
          </Box>

          <Box className="browse-files-wrap">
            <CustomButton
              className="p16 browse-files"
              onClick={open}
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Browse files"
            />
          </Box>
        </Box>
      ) : (
        <Box className="media-previews w100">
          <Box className="add-file-wrap d-flex justify-end">
            <CustomButton
              className="p16 add-file"
              onClick={open}
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Browse files"
            />
          </Box>
          {mediaFiles?.map((media, index) => (
            <Box
              key={index}
              className="preview-container d-flex align-center justify-space-between pl32 pr32 "
            >
              <Box className="file-name-wrap d-flex  align-center gap-30">
                <Box className="media-icon-wrap">
                  <PermMediaOutlinedIcon
                    sx={{ cursor: 'pointer' }}
                    className="icon-wrap"
                  />
                </Box>
                <Tooltip title={media?.name} arrow>
                  <Typography component="p" className="file-name">
                    {media?.name}
                  </Typography>
                </Tooltip>
              </Box>
              <Box className="more-item-icon">
                <MoreHorizIcon
                  onClick={handlePopoverOpen}
                  sx={{ cursor: 'pointer' }}
                  className="more-item"
                />
                <Pohover
                  anchorEl={anchorEl}
                  setAnchorEl={setAnchorEl}
                  handlePopoverClose={handlePopoverClose}
                  handlePreviewClick={handlePreviewClick}
                  media={media}
                  handleDownload={handleDownload}
                  removeMedia={removeMedia}
                  index={index}
                />
              </Box>
              <Box className="icons-wrap d-flex gap-sm">
                <Tooltip title="Preview" arrow>
                  <RemoveRedEyeOutlinedIcon
                    onClick={() => handlePreviewClick(media)}
                    className="eye-icon"
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>
                <Tooltip title="Download" arrow>
                  <FileDownloadOutlinedIcon
                    onClick={() => handleDownload(media?.preview, media?.name)}
                    className="download-icon"
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>

                <Tooltip title="Delete" arrow>
                  <DeleteOutlineIcon
                    className="delete-icon"
                    onClick={() => removeMedia(index)}
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>
              </Box>
            </Box>
          ))}
        </Box>
      )}
      <Box {...getRootProps()} style={{ display: 'none' }}>
        <input {...getInputProps()} />
      </Box>
      <PreviewModal
        open={openModal}
        setOpen={setOpenModal}
        previewMedia={previewMedia}
      />
    </Box>
  );
}
