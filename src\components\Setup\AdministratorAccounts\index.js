'use client';

import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  Divider,
  Popover,
  Checkbox,
} from '@mui/material';
import AuthContext from '@/helper/authcontext';
import CustomButton from '@/components/UI/CustomButton';
import AddIcon from '@mui/icons-material/Add';
import DialogBox from '@/components/UI/Modalbox';
import { DataGrid } from '@mui/x-data-grid';
import { DateFormat, setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  fetchFromStorage,
  removeFromStorage,
  saveToStorage,
} from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
// import moment from 'moment';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomOrgPagination from '@/components/UI/customPagination';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import useLocationData from '@/hooks/useLocationData';
import DeleteModal from '@/components/UI/DeleteModal';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CreateAdmin from './CreateAdmin';
import AdminProfile from '@/components/Users/<USER>';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import DownloadIcon from '@mui/icons-material/Download';
import SettingsIcon from '@mui/icons-material/Settings';
import './adminstaroraccounts.scss';

export default function AdministratorAccount() {
  const { authState } = useContext(AuthContext);
  const { userdata, setUserdata } = useContext(AuthContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const isAdmin = searchParams.get('is_admin');
  const isEdit = searchParams.get('is_edit');
  const isAdminId = searchParams.get('admin_id');
  const [adminUserList, setAdminUserList] = useState([{ id: '' }]);
  const [updateItem, setUpdateItem] = useState('');
  const [loader, setLoader] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [roleList, setRoleList] = useState([]);
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);
  const id = open ? 'export-popover' : undefined;
  const [selectedCat, setSelectedCat] = useState([]);
  const [selectedFields, setSelectedFields] = useState([]);
  const queryParams = new URLSearchParams(searchParams);

  const {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  } = useLocationData();

  // get single user details
  const fetchSingleUser = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONE_USER + `${isAdminId}`
      );
      if (status === 200) {
        // setUpdateItem(data?.data);
        let filterUserList = data?.data?.user_roles?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        setUpdateItem({ ...data?.data, user_roles: filterUserList });
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (isAdminId) {
      fetchSingleUser();
    } else {
      setUpdateItem('');
    }
  }, [isAdminId]);
  useEffect(() => {
    setSelectedCountry(updateItem?.geo_country?.place_code || '');
    setSelectedCounty(updateItem?.geo_state?.place_code || '');
  }, [updateItem]);
  const handleOpenDeleteDialog = (id) => {
    setDeleteId(id);
    setDeleteDialogOpen(true);
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };
  // Delete admin user
  const handleDeleteUser = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_USER + id
      );
      if (status === 200 || status === 201) {
        handleCloseDeleteDialog();
        if (adminUserList?.length === 1 && page !== 1) {
          getAdminUserList(searchValue, Number(page) - 1);
          setPage(Number(page) - 1);
        } else {
          getAdminUserList(searchValue, Number(page));
        }
        if (data?.status) {
          setApiMessage('success', data?.message);
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // list of admin users
  const getAdminUserList = async (search, pageNo, Rpp, isExport) => {
    !isExport && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_USER_LIST +
          `?isAdmin=true&role_id=2&search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }`
      );

      if (status === 200 || status === 201) {
        setAdminUserList(data?.userList);
        setTotalCount(data?.count);
        removeFromStorage(identifiers?.RedirectData);
        setUserdata();
        setTimeout(() => {
          setLoader(false);
        }, 500);
      }
    } catch (error) {
      setLoader(false);
      setAdminUserList([]);
      setTotalCount(0);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // Save field sequence
  const handleSaveConfiguration = async (type) => {
    const requiredFields = [
      'employment_number',
      'username',
      'user_first_name',
      'user_last_name',
      'user_email',
    ];
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.STORE_USER_FIELDS_SEQUENCE,
        {
          user_field_order: requiredFields,
        }
      );
      if (status === 200) {
        if (data?.status) {
          setTimeout(() => {
            getDownloadUserList(searchValue, 1, undefined, type, true);
          }, 500);
        } else {
          // setApiMessage('error', data?.message);
        }
      }
    } catch {
      // setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getStoredField = async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STORED_USER_FIELDS
      );

      if (status === 200) {
        setSelectedFields(data?.data?.user_field_order);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Download staff list
  const getDownloadUserList = async (
    search,
    pageNo,
    Rpp,
    fileType,
    isExport
  ) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.EXPORT_USER_LIST +
          `?isAdmin=true&role_id=2&search=${search}&page=${pageNo}&size=${
            Rpp ? Rpp : rowsPerPage
          }&file_type=${fileType}`,
        {
          responseType: 'blob',
        }
      );

      if (status === 200) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const link = document.createElement('a');
        link.href = url;
        var filename;
        filename = `${identifiers?.APP_NAME}_Admin_List.${fileType === 'excel' ? 'xlsx' : 'csv'}`;

        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        isExport && getStoredField();
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onPageChange = (newPage) => {
    setPage(newPage);
    getAdminUserList(searchValue, newPage);
  };
  const onRowPerPage = (newPage) => {
    setRowsPerPage(newPage);
    setPage(1);
    getAdminUserList(searchValue, 1, newPage);
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setPage(1);
      getAdminUserList(searchValue, 1);
    }
  };
  // list of roles or designations
  const getRoleList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_ROLE_LIST);

      if (status === 200) {
        setLoader(false);
        let filterUserList = data?.data?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        setRoleList(filterUserList);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  useEffect(() => {
    if (
      fetchFromStorage(identifiers?.RedirectData) &&
      fetchFromStorage(identifiers?.RedirectData)?.IsFromUser &&
      fetchFromStorage(identifiers?.RedirectData)?.IsAdmin
    ) {
      const fdata = fetchFromStorage(identifiers?.RedirectData);
      setPage(fdata?.page);
      setRowsPerPage(fdata?.rowsPerPage);
      setSearchValue(fdata?.searchValue);
      getAdminUserList(fdata?.searchValue, fdata?.page, fdata?.rowsPerPage);
    } else if (userdata && userdata?.IsFromUser && userdata?.IsAdmin) {
      const fdata = userdata;
      setPage(fdata?.page);
      setRowsPerPage(fdata?.rowsPerPage);
      setSearchValue(fdata?.searchValue);
      getAdminUserList(fdata?.searchValue, fdata?.page, fdata?.rowsPerPage);
    } else {
      removeFromStorage(identifiers?.RedirectData);
      setUserdata();
    }
  }, [
    fetchFromStorage(identifiers?.RedirectData)?.IsFromUser,
    userdata?.IsFromUser,
  ]);
  useEffect(() => {
    if (
      authState?.UserPermission?.user === 1 ||
      authState?.UserPermission?.user === 2
    ) {
      getRoleList();
      getStoredField();
      if (
        (!fetchFromStorage(identifiers?.RedirectData) &&
          userdata?.page === undefined &&
          fetchFromStorage(identifiers?.RedirectData)?.IsFromUser ===
            undefined &&
          userdata?.IsFromUser === undefined) ||
        !fetchFromStorage(identifiers?.RedirectData)?.IsAdmin
      ) {
        getAdminUserList(searchValue, page);
      }
    }
  }, [authState?.UserPermission?.user]);

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      const newSelecteds = adminUserList?.map((n) => n?.id);
      setSelectedCat(newSelecteds);
      return;
    }
    setSelectedCat([]);
  };

  const columns = [
    {
      field: 'id',
      width: 100,
      minWidth: 100,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      headerName: (
        <Box className="d-flex align-center justify-start">
          <Box className="d-flex justify-start">
            <Checkbox
              className="table-checkbox pl0"
              checked={
                selectedCat === 'all' ||
                (Array.isArray(selectedCat) &&
                  adminUserList?.every((user) =>
                    selectedCat?.includes(user?.id)
                  ))
              }
              onClick={(event) => handleSelectAllClick(event)}
            />
          </Box>
          <Typography className="title-text cursor-pointer fw600">
            {'ID'}
          </Typography>
        </Box>
      ),
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100">
            <Box className="d-flex align-center">
              <Checkbox
                className="table-checkbox checkbox pl0 pr4"
                disabled={true}
                checked={
                  selectedCat === 'all'
                    ? true
                    : selectedCat?.indexOf(params?.value) !== -1
                }
              />
            </Box>
            <Tooltip
              arrow
              title={<Typography>{params?.row?.employment_number}</Typography>}
              classes={{
                tooltip: 'info-tooltip-container ',
              }}
            >
              <Typography className="title-text cursor-pointer">
                {params?.row?.employment_number}
              </Typography>
            </Tooltip>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'Name',
      width: 200,
      minWidth: 200,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <CommonUserDetails
            userData={params?.row}
            filterDataApplied={{}}
            searchValue={searchValue}
            page={page}
            rowsPerPage={rowsPerPage}
            setUserdata={setUserdata}
            authState={authState}
            navigationProps={{ IsAdmin: true }}
            isRedirect
            onClick={() => {
              queryParams.set('is_admin', true);
              queryParams.set('is_edit', true);
              queryParams.set('admin_id', params?.row?.id);
              router.push(`?${queryParams.toString()}`);
            }}
          />
        );
      },
    },
    {
      field: 'user_joining_date',
      headerName: 'Joining date',
      width: 200,
      minWidth: 200,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      sortable: false,
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center">
            {DateFormat(params?.value, 'dates')}
          </Box>
        );
      },
    },
    {
      field: 'user_status',
      headerName: 'Profile status',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            {params?.value === 'rejected' ||
            params?.value === 'deleted' ||
            params?.value === 'cancelled' ? (
              <Typography className="p12 failed fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : params?.value === 'ongoing' ? (
              <Typography className="p12  ongoing fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : params?.value === 'draft' || params?.value === 'pending' ? (
              <Typography className="p12 draft fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : params?.value === 'completed' ||
              params?.value === 'verified' ? (
              <Typography className="p12 active-onboarding fw600">
                {' '}
                {params?.value}{' '}
              </Typography>
            ) : (
              <Typography className="p12 success fw600">
                {/* active  */} {params?.value}{' '}
              </Typography>
            )}
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        // Custom actions icons for each row
        return (
          <Box className="d-flex align-center justify-center actions">
            {authState?.UserPermission?.user === 1 ? (
              <>
                <Tooltip
                  title={<Typography>View</Typography>}
                  arrow
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <Box>
                    <ViewIcon
                      onClick={() => {
                        setUserdata({
                          id: params?.row?.id,
                          searchValue: searchValue,
                          page: page,
                          IsAdmin: true,
                          rowsPerPage: rowsPerPage,
                        });
                        saveToStorage(identifiers?.RedirectData, {
                          id: params?.row?.id,
                          searchValue: searchValue,
                          page: page,
                          IsAdmin: true,
                          rowsPerPage: rowsPerPage,
                        });

                        queryParams.set('is_admin', true);
                        queryParams.set('is_edit', true);
                        queryParams.set('admin_id', params?.row?.id);
                        router.push(`?${queryParams.toString()}`);

                        // router.push(`/user/${params?.row?.id}?IsAdmin=true`);
                      }}
                    />
                  </Box>
                </Tooltip>
              </>
            ) : authState?.UserPermission?.user === 2 ? (
              <>
                <Tooltip
                  title={<Typography>View</Typography>}
                  arrow
                  classes={{ tooltip: 'info-tooltip-container' }}
                >
                  <Box>
                    <ViewIcon
                      onClick={() => {
                        setUserdata({
                          id: params?.row?.id,
                          searchValue: searchValue,
                          page: page,
                          IsAdmin: true,
                          rowsPerPage: rowsPerPage,
                        });
                        saveToStorage(identifiers?.RedirectData, {
                          id: params?.row?.id,
                          searchValue: searchValue,
                          page: page,
                          IsAdmin: true,
                          rowsPerPage: rowsPerPage,
                        });

                        queryParams.set('is_admin', true);
                        queryParams.set('is_edit', true);
                        queryParams.set('admin_id', params?.row?.id);
                        router.push(`?${queryParams.toString()}`);

                        // router.push(`/user/${params?.row?.id}?IsAdmin=true`);
                      }}
                    />
                  </Box>
                </Tooltip>
                {params?.row?.user_status !== 'deleted' && (
                  <Tooltip
                    title={<Typography>Delete</Typography>}
                    arrow
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <Box>
                      <DeleteIcon
                        onClick={() => handleOpenDeleteDialog(params?.row?.id)}
                      />
                    </Box>
                  </Tooltip>
                )}
              </>
            ) : (
              <></>
            )}
          </Box>
        );
      },
    },
  ];

  const handleClearSearch = () => {
    setPage(1);
    getAdminUserList('', 1);
  };

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <>
      {isAdmin === 'true' && (isEdit === 'true' || isEdit === 'false') ? (
        <>
          {isEdit === 'false' && (
            <Box>
              <Box className="d-flex align-center">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={() => {
                    router.back();
                  }}
                />
                <Typography className="body-text fw600 pr8">
                  Create Admin
                </Typography>
              </Box>
              <Divider className="mb16 mt16" />
              <CreateAdmin
                isUpdate={isEdit === 'true'}
                updateItem={updateItem}
                getAdminUserList={getAdminUserList}
                searchValue={searchValue}
                page={page}
                countries={countries}
                counties={counties}
                cities={cities}
                setCounties={setCounties}
                setCities={setCities}
                setSelectedCountry={setSelectedCountry}
                setSelectedCounty={setSelectedCounty}
                roleList={roleList}
              />
            </Box>
          )}
          {isEdit === 'true' && (
            <Box>
              <AdminProfile />
            </Box>
          )}
        </>
      ) : (
        <Box className="admin-accounts-page">
          <Box className="administrator-section">
            <CustomSearch
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              onKeyPress={handleKeyPress}
              isClearSearch
              handleClearSearch={() => handleClearSearch()}
            />
            <CustomButton
              variant="outlined"
              title="Search"
              onClick={() => {
                setPage(1);
                getAdminUserList(searchValue, 1);
              }}
              fullWidth={false}
            />
            {authState?.UserPermission?.user === 2 && (
              <CustomButton
                variant="contained"
                title="Create admin"
                startIcon={<AddIcon />}
                fullWidth={false}
                onClick={() => {
                  queryParams.set('is_admin', true);
                  queryParams.set('is_edit', false);
                  router.push(`?${queryParams.toString()}`);
                  // saveToStorage(identifiers?.RedirectData, {
                  //   fromAdminAccount: true,
                  // });
                  // router.push('/staff/create-staff');
                }}
              />
            )}
            <Box className="export-section">
              {authState?.UserPermission?.user === 2 && (
                <CustomButton
                  isIconOnly
                  disabled={selectedCat !== 'all' && selectedCat?.length === 0}
                  startIcon={
                    <Tooltip
                      title={<Typography>Download </Typography>}
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                      arrow
                    >
                      <DownloadIcon />
                    </Tooltip>
                  }
                  onClick={handleClick}
                />
              )}
              {authState?.UserPermission?.staff === 2 && (
                <>
                  <CustomButton
                    isIconOnly
                    startIcon={
                      <Tooltip
                        title={<Typography>Export Settings</Typography>}
                        classes={{
                          tooltip: 'info-tooltip-container',
                        }}
                        arrow
                      >
                        <SettingsIcon />
                      </Tooltip>
                    }
                    onClick={() => {
                      router.push('/org/setup?is_setup=8&is_tab=1');
                    }}
                  />
                </>
              )}
            </Box>
          </Box>
          <Box className="selection-staff-count">
            {selectedCat === 'all' ? (
              <Typography className="title-text text-align">
                All {totalCount} admins are selected.{' '}
                <span
                  className="select-all-text"
                  onClick={() => {
                    setSelectedCat([]);
                  }}
                >
                  Clear selection
                </span>
              </Typography>
            ) : selectedCat?.length > 0 ? (
              <Typography className="title-text text-align">
                All {selectedCat?.length} admins on this page are selected.{' '}
                <span
                  className="select-all-text"
                  onClick={() => {
                    setSelectedCat('all');
                  }}
                >
                  Select all {totalCount} admins
                </span>
              </Typography>
            ) : (
              <></>
            )}
          </Box>
          <Box className="table-container table-layout">
            {loader ? (
              <ContentLoader />
            ) : (
              <>
                {adminUserList && adminUserList?.length === 0 ? (
                  <NoDataView
                    title="No Admin Data Found"
                    description="There is no Admin Data at the moment."
                  />
                ) : (
                  <>
                    <DataGrid
                      rows={adminUserList}
                      columns={columns}
                      pageSize={rowsPerPage}
                      checkboxSelection={false} // Disable default checkbox column
                      disableSelectionOnClick // Disable row selection on click
                      hideMenuIcon
                    />
                    <CustomOrgPagination
                      currentPage={page}
                      totalCount={totalCount}
                      rowsPerPage={rowsPerPage}
                      onPageChange={onPageChange}
                      OnRowPerPage={onRowPerPage}
                    />
                  </>
                )}
              </>
            )}
          </Box>

          <Popover
            className="export-popover"
            id={id}
            open={open}
            anchorEl={anchorEl}
            onClose={handleClose}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Box className="export-option">
              <Typography
                className="title-text fw600 pb8 cursor-pointer"
                onClick={() => {
                  setPage(1);
                  getAdminUserList(searchValue, 1, undefined, true);

                  if (selectedFields?.length > 0) {
                    getDownloadUserList(searchValue, 1, undefined, 'excel');
                  } else {
                    handleSaveConfiguration('excel');
                  }
                  handleClose();
                }}
              >
                Excel
              </Typography>
              <Typography
                className="title-text fw600 cursor-pointer"
                onClick={() => {
                  setPage(1);
                  getAdminUserList(searchValue, 1, undefined, true);
                  if (selectedFields?.length > 0) {
                    getDownloadUserList(searchValue, 1, undefined, 'csv');
                  } else {
                    handleSaveConfiguration('csv');
                  }
                  handleClose();
                }}
              >
                CSV
              </Typography>
            </Box>
          </Popover>
        </Box>
      )}

      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={() => handleDeleteUser(deleteId)}
            text="Are you sure you want to delete this Admin User?"
          />
        }
      />
    </>
  );
}
