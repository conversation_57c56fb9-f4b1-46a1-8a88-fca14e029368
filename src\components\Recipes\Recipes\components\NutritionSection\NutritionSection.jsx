import React, { useState, useEffect } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import { InputAdornment } from '@mui/material';
import './NutritionSection.scss';

// Validation Schema
const nutritionValidationSchema = Yup.object().shape({
  calories: Yup.number()
    .min(0, 'Calories cannot be negative')
    .max(9999, 'Calories too high'),
  protein: Yup.number()
    .min(0, 'Protein cannot be negative')
    .max(999, 'Protein too high'),
  carbs: Yup.number()
    .min(0, 'Carbs cannot be negative')
    .max(999, 'Carbs too high'),
  fat: Yup.number().min(0, 'Fat cannot be negative').max(999, 'Fat too high'),
  fiber: Yup.number()
    .min(0, 'Fiber cannot be negative')
    .max(999, 'Fiber too high'),
  sugar: Yup.number()
    .min(0, 'Sugar cannot be negative')
    .max(999, 'Sugar too high'),
  sodium: Yup.number()
    .min(0, 'Sodium cannot be negative')
    .max(9999, 'Sodium too high'),
  cholesterol: Yup.number()
    .min(0, 'Cholesterol cannot be negative')
    .max(999, 'Cholesterol too high'),
});

const NutritionSection = ({ formData, dispatch }) => {
  const [activeTab, setActiveTab] = useState('nutrition');
  const [allergenSearch, setAllergenSearch] = useState('');

  // Local state for nutrition to ensure immediate UI updates
  const [localNutrition, setLocalNutrition] = useState(() => {
    return (
      formData?.nutrition || {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        vitaminA: 0,
        vitaminC: 0,
        calcium: 0,
        iron: 0,
      }
    );
  });

  // Local state for allergens - separate states for common and may contain
  const [localCommonAllergens, setLocalCommonAllergens] = useState(() => {
    return formData?.nutrition?.commonAllergens || [];
  });

  const [localMayContainAllergens, setLocalMayContainAllergens] = useState(
    () => {
      return formData?.nutrition?.mayContainAllergens || [];
    }
  );

  // Sync local state with prop changes
  useEffect(() => {
    setLocalNutrition(
      formData?.nutrition || {
        calories: 0,
        protein: 0,
        carbs: 0,
        fat: 0,
        fiber: 0,
        sugar: 0,
        sodium: 0,
        cholesterol: 0,
        vitaminA: 0,
        vitaminC: 0,
        calcium: 0,
        iron: 0,
      }
    );
    setLocalCommonAllergens(formData?.nutrition?.commonAllergens || []);
    setLocalMayContainAllergens(formData?.nutrition?.mayContainAllergens || []);
  }, [
    formData?.nutrition,
    formData?.nutrition?.commonAllergens,
    formData?.nutrition?.mayContainAllergens,
  ]);

  // Use local state for immediate UI updates
  const nutrition = localNutrition;
  const selectedCommonAllergens = localCommonAllergens;
  const selectedMayContainAllergens = localMayContainAllergens;

  const allergenOptions = [
    { id: 'milk', name: 'Milk/Dairy', icon: 'Milk', severity: 'high' },
    { id: 'eggs', name: 'Eggs', icon: 'Egg', severity: 'high' },
    { id: 'fish', name: 'Fish', icon: 'Fish', severity: 'high' },
    { id: 'shellfish', name: 'Shellfish', icon: 'Shell', severity: 'high' },
    { id: 'tree-nuts', name: 'Tree Nuts', icon: 'Nut', severity: 'high' },
    { id: 'peanuts', name: 'Peanuts', icon: 'Peanut', severity: 'high' },
    { id: 'wheat', name: 'Wheat/Gluten', icon: 'Wheat', severity: 'medium' },
    { id: 'soy', name: 'Soy', icon: 'Bean', severity: 'medium' },
    { id: 'sesame', name: 'Sesame', icon: 'Seed', severity: 'medium' },
    { id: 'sulfites', name: 'Sulfites', icon: 'Droplets', severity: 'low' },
    { id: 'mustard', name: 'Mustard', icon: 'Leaf', severity: 'low' },
    { id: 'celery', name: 'Celery', icon: 'Carrot', severity: 'low' },
  ];

  // Validate nutrition field
  const validateNutritionField = async (field, value) => {
    try {
      await nutritionValidationSchema.validateAt(field, { [field]: value });
      return null;
    } catch (error) {
      return error?.message || 'Invalid value';
    }
  };

  const nutritionFields = [
    {
      key: 'calories',
      label: 'Calories',
      unit: 'kcal',
      icon: 'Zap',
      color: 'nutrition-section__icon--orange',
    },
    {
      key: 'protein',
      label: 'Protein',
      unit: 'g',
      icon: 'Dumbbell',
      color: 'nutrition-section__icon--red',
    },
    {
      key: 'carbs',
      label: 'Carbohydrates',
      unit: 'g',
      icon: 'Wheat',
      color: 'nutrition-section__icon--yellow',
    },
    {
      key: 'fat',
      label: 'Total Fat',
      unit: 'g',
      icon: 'Droplets',
      color: 'nutrition-section__icon--blue',
    },
    {
      key: 'fiber',
      label: 'Dietary Fiber',
      unit: 'g',
      icon: 'Leaf',
      color: 'nutrition-section__icon--green',
    },
    {
      key: 'sugar',
      label: 'Sugar',
      unit: 'g',
      icon: 'Candy',
      color: 'nutrition-section__icon--pink',
    },
    {
      key: 'sodium',
      label: 'Sodium',
      unit: 'mg',
      icon: 'Salt',
      color: 'nutrition-section__icon--gray',
    },
    {
      key: 'cholesterol',
      label: 'Cholesterol',
      unit: 'mg',
      icon: 'Heart',
      color: 'nutrition-section__icon--purple',
    },
  ];

  const vitaminFields = [
    {
      key: 'vitaminA',
      label: 'Vitamin A',
      unit: 'IU',
      icon: 'Eye',
      color: 'nutrition-section__icon--orange',
    },
    {
      key: 'vitaminC',
      label: 'Vitamin C',
      unit: 'mg',
      icon: 'Citrus',
      color: 'nutrition-section__icon--yellow',
    },
    {
      key: 'calcium',
      label: 'Calcium',
      unit: 'mg',
      icon: 'Bone',
      color: 'nutrition-section__icon--gray',
    },
    {
      key: 'iron',
      label: 'Iron',
      unit: 'mg',
      icon: 'Shield',
      color: 'nutrition-section__icon--red',
    },
  ];

  const updateNutrition = async (field, value) => {
    const numericValue = parseFloat(value) || 0;

    // Update local state immediately for UI responsiveness
    const updatedNutrition = { ...nutrition, [field]: numericValue };
    setLocalNutrition(updatedNutrition);

    // Validate field
    await validateNutritionField(field, numericValue);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: { [field]: numericValue },
      });
    }
  };

  const toggleCommonAllergen = (allergenId) => {
    const currentCommonAllergens = selectedCommonAllergens || [];
    const updatedCommonAllergens = currentCommonAllergens?.includes?.(
      allergenId
    )
      ? currentCommonAllergens?.filter?.((id) => id !== allergenId) || []
      : [...(currentCommonAllergens || []), allergenId];

    // Update local state immediately for UI responsiveness
    setLocalCommonAllergens(updatedCommonAllergens);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: { commonAllergens: updatedCommonAllergens },
      });
    }
  };

  const toggleMayContainAllergen = (allergenId) => {
    const currentMayContainAllergens = selectedMayContainAllergens || [];
    const updatedMayContainAllergens = currentMayContainAllergens?.includes?.(
      allergenId
    )
      ? currentMayContainAllergens?.filter?.((id) => id !== allergenId) || []
      : [...(currentMayContainAllergens || []), allergenId];

    // Update local state immediately for UI responsiveness
    setLocalMayContainAllergens(updatedMayContainAllergens);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_NUTRITION',
        payload: { mayContainAllergens: updatedMayContainAllergens },
      });
    }
  };

  const filteredAllergens =
    allergenOptions?.filter?.((allergen) =>
      allergen?.name
        ?.toLowerCase?.()
        ?.includes?.(allergenSearch?.toLowerCase?.() || '')
    ) || [];

  const getSeverityClass = (severity) => {
    switch (severity) {
      case 'high':
        return 'nutrition-section__allergen-item--high';
      case 'medium':
        return 'nutrition-section__allergen-item--medium';
      case 'low':
        return 'nutrition-section__allergen-item--low';
      default:
        return 'nutrition-section__allergen-item--default';
    }
  };

  return (
    <div className="nutrition-section">
      {/* Tab Navigation */}
      <div className="nutrition-section__tabs">
        <button
          onClick={() => setActiveTab('nutrition')}
          className={`nutrition-section__tab ${
            activeTab === 'nutrition'
              ? 'nutrition-section__tab--active'
              : 'nutrition-section__tab--inactive'
          }`}
        >
          <Icon
            name="BarChart3"
            size={16}
            color={
              activeTab === 'nutrition'
                ? 'var(--color-primary)'
                : 'var(--text-color-slate-gray)'
            }
          />
          <span>Nutrition Facts</span>
        </button>

        <button
          onClick={() => setActiveTab('allergens')}
          className={`nutrition-section__tab ${
            activeTab === 'allergens'
              ? 'nutrition-section__tab--active'
              : 'nutrition-section__tab--inactive'
          }`}
        >
          <Icon
            name="AlertTriangle"
            size={16}
            color={
              activeTab === 'allergens'
                ? 'var(--color-primary)'
                : 'var(--text-color-slate-gray)'
            }
          />
          <span>Allergens</span>
        </button>
      </div>

      {/* Nutrition Facts Tab */}
      {activeTab === 'nutrition' && (
        <div className="nutrition-section__content">
          <div className="nutrition-section__header">
            <h3 className="nutrition-section__title">Nutrition Information</h3>
            <p className="nutrition-section__description">
              Enter nutritional values per serving
            </p>
          </div>

          {/* Main Nutrition Fields */}
          <div className="nutrition-section__grid">
            {nutritionFields?.map?.((field) => (
              <div key={field?.key} className="nutrition-section__field-group">
                <CustomTextField
                  label={
                    <div className="nutrition-section__field-label">
                      <Icon
                        name={field?.icon}
                        size={16}
                        color="currentColor"
                        className={field?.color}
                      />
                      <span>{field?.label}</span>
                    </div>
                  }
                  type="number"
                  value={nutrition?.[field?.key] || ''}
                  onChange={(e) =>
                    updateNutrition(field?.key, e?.target?.value || '')
                  }
                  inputProps={{ step: 0.1, min: 0 }}
                  placeholder="0"
                  helperText={field?.unit}
                  fullWidth
                />
              </div>
            )) || []}
          </div>

          {/* Vitamins & Minerals */}
          <div className="nutrition-section__vitamins">
            <h4 className="nutrition-section__vitamins-title">
              Vitamins & Minerals (Optional)
            </h4>
            <div className="nutrition-section__grid-4">
              {vitaminFields?.map?.((field) => (
                <div
                  key={field?.key}
                  className="nutrition-section__field-group"
                >
                  <CustomTextField
                    label={
                      <div className="nutrition-section__field-label">
                        <Icon
                          name={field?.icon}
                          size={14}
                          color="currentColor"
                          className={field?.color}
                        />
                        <span>{field?.label}</span>
                      </div>
                    }
                    type="number"
                    value={nutrition?.[field?.key] || ''}
                    onChange={(e) =>
                      updateNutrition(field?.key, e?.target?.value || '')
                    }
                    inputProps={{ step: 0.1, min: 0 }}
                    placeholder="0"
                    helperText={field?.unit}
                    size="small"
                    fullWidth
                  />
                </div>
              )) || []}
            </div>
          </div>

          {/* Nutrition Summary Card */}
          <div className="nutrition-section__summary">
            <h4 className="nutrition-section__summary-title">
              Nutrition Summary
            </h4>
            <div className="nutrition-section__summary-grid">
              <div className="nutrition-section__summary-item">
                <div className="nutrition-section__summary-value">
                  {nutrition?.calories || 0}
                </div>
                <div className="nutrition-section__summary-label">Calories</div>
              </div>
              <div className="nutrition-section__summary-item">
                <div className="nutrition-section__summary-value">
                  {nutrition?.protein || 0}g
                </div>
                <div className="nutrition-section__summary-label">Protein</div>
              </div>
              <div className="nutrition-section__summary-item">
                <div className="nutrition-section__summary-value">
                  {nutrition?.carbs || 0}g
                </div>
                <div className="nutrition-section__summary-label">Carbs</div>
              </div>
              <div className="nutrition-section__summary-item">
                <div className="nutrition-section__summary-value">
                  {nutrition?.fat || 0}g
                </div>
                <div className="nutrition-section__summary-label">Fat</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Allergens Tab */}
      {activeTab === 'allergens' && (
        <div className="nutrition-section__content">
          <div className="nutrition-section__header">
            <h3 className="nutrition-section__title">Allergen Information</h3>
            <p className="nutrition-section__description">
              Select all allergens present in this recipe
            </p>
          </div>

          {/* Allergen Search */}
          <div className="nutrition-section__search">
            <CustomTextField
              value={allergenSearch}
              onChange={(e) => setAllergenSearch(e?.target?.value || '')}
              placeholder="Search allergens..."
              fullWidth
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Icon
                      name="Search"
                      size={20}
                      className="nutrition-section__search-icon"
                    />
                  </InputAdornment>
                ),
              }}
            />
          </div>

          {/* Common Allergens Grid */}
          <div className="nutrition-section__allergens">
            <h3 className="nutrition-section__allergens-title">
              Common Allergens
            </h3>
            <div className="nutrition-section__allergens-grid">
              {filteredAllergens?.map?.((allergen) => {
                return (
                  <button
                    key={`common-${allergen?.id}`}
                    onClick={() => toggleCommonAllergen(allergen?.id)}
                    className={`nutrition-section__allergen-item ${
                      selectedCommonAllergens?.includes?.(allergen?.id)
                        ? 'nutrition-section__allergen-item--selected'
                        : getSeverityClass(allergen?.severity)
                    }`}
                  >
                    <div
                      className={`nutrition-section__allergen-icon ${
                        selectedCommonAllergens?.includes?.(allergen?.id)
                          ? 'nutrition-section__allergen-icon--selected'
                          : 'nutrition-section__allergen-icon--default'
                      }`}
                    >
                      <Icon
                        name={allergen?.icon}
                        size={20}
                        color="currentColor"
                      />
                    </div>

                    <div className="nutrition-section__allergen-content">
                      <div className="nutrition-section__allergen-label">
                        {allergen?.name}
                      </div>
                      <div className="nutrition-section__allergen-severity">
                        {allergen?.severity} risk
                      </div>
                    </div>

                    {selectedCommonAllergens?.includes?.(allergen?.id) && (
                      <Icon name="Check" size={16} color="currentColor" />
                    )}
                  </button>
                );
              }) || []}
            </div>
          </div>

          {/* May Contain Allergens Grid */}
          <div className="nutrition-section__allergens">
            <h3 className="nutrition-section__allergens-title">
              May Contain Allergens
            </h3>
            <div className="nutrition-section__allergens-grid">
              {filteredAllergens?.map?.((allergen) => {
                return (
                  <button
                    key={`may-contain-${allergen?.id}`}
                    onClick={() => toggleMayContainAllergen(allergen?.id)}
                    className={`nutrition-section__allergen-item ${
                      selectedMayContainAllergens?.includes?.(allergen?.id)
                        ? 'nutrition-section__allergen-item--selected'
                        : getSeverityClass(allergen?.severity)
                    }`}
                  >
                    <div
                      className={`nutrition-section__allergen-icon nutrition-section__allergen-icon--may-contain ${
                        selectedMayContainAllergens?.includes?.(allergen?.id)
                          ? 'nutrition-section__allergen-icon--selected'
                          : 'nutrition-section__allergen-icon--default'
                      }`}
                    >
                      <Icon
                        name={allergen?.icon}
                        size={20}
                        color="currentColor"
                      />
                      <span className="nutrition-section__allergen-indicator">
                        M
                      </span>
                    </div>

                    <div className="nutrition-section__allergen-content">
                      <div className="nutrition-section__allergen-label">
                        {allergen?.name}
                      </div>
                      <div className="nutrition-section__allergen-severity">
                        {allergen?.severity} risk
                      </div>
                    </div>

                    {selectedMayContainAllergens?.includes?.(allergen?.id) && (
                      <Icon name="Check" size={16} color="currentColor" />
                    )}
                  </button>
                );
              }) || []}
            </div>
          </div>

          {/* Selected Common Allergens Summary */}
          {(selectedCommonAllergens?.length || 0) > 0 && (
            <div className="nutrition-section__dietary">
              <div className="nutrition-section__dietary-header">
                <Icon name="AlertTriangle" size={16} color="#EF4444" />
                <h4 className="nutrition-section__dietary-title">
                  Contains Allergens ({selectedCommonAllergens?.length || 0})
                </h4>
              </div>
              <div className="nutrition-section__dietary-grid">
                {selectedCommonAllergens?.map?.((allergenId) => {
                  const allergenData = allergenOptions?.find?.(
                    (a) => a?.id === allergenId
                  );
                  return (
                    <div
                      key={`common-${allergenId}`}
                      className="nutrition-section__dietary-item nutrition-section__dietary-item--selected"
                    >
                      <div className="nutrition-section__dietary-icon">
                        <Icon
                          name={allergenData?.icon || 'AlertTriangle'}
                          size={16}
                          color="currentColor"
                        />
                      </div>

                      <div className="nutrition-section__dietary-content">
                        <span className="nutrition-section__dietary-label">
                          {allergenData?.name || allergenId}
                        </span>
                      </div>

                      <button
                        onClick={() => toggleCommonAllergen(allergenId)}
                        className="nutrition-section__dietary-remove"
                        aria-label={`Remove ${allergenData?.name || allergenId}`}
                      >
                        <Icon name="X" size={14} color="currentColor" />
                      </button>
                    </div>
                  );
                }) || []}
              </div>
            </div>
          )}

          {/* Selected May Contain Allergens Summary */}
          {(selectedMayContainAllergens?.length || 0) > 0 && (
            <div className="nutrition-section__dietary">
              <div className="nutrition-section__dietary-header">
                <Icon name="AlertCircle" size={16} color="#F59E0B" />
                <h4 className="nutrition-section__dietary-title">
                  May Contain Allergens (
                  {selectedMayContainAllergens?.length || 0})
                </h4>
              </div>
              <div className="nutrition-section__dietary-grid">
                {selectedMayContainAllergens?.map?.((allergenId) => {
                  const allergenData = allergenOptions?.find?.(
                    (a) => a?.id === allergenId
                  );
                  return (
                    <div
                      key={`may-contain-${allergenId}`}
                      className="nutrition-section__dietary-item nutrition-section__dietary-item--may-contain"
                    >
                      <div className="nutrition-section__dietary-icon nutrition-section__dietary-icon--may-contain">
                        <Icon
                          name={allergenData?.icon || 'AlertCircle'}
                          size={16}
                          color="currentColor"
                        />
                        <span className="nutrition-section__dietary-indicator">
                          M
                        </span>
                      </div>

                      <div className="nutrition-section__dietary-content">
                        <span className="nutrition-section__dietary-label">
                          {allergenData?.name || allergenId}
                        </span>
                      </div>

                      <button
                        onClick={() => toggleMayContainAllergen(allergenId)}
                        className="nutrition-section__dietary-remove"
                        aria-label={`Remove ${allergenData?.name || allergenId}`}
                      >
                        <Icon name="X" size={14} color="currentColor" />
                      </button>
                    </div>
                  );
                }) || []}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NutritionSection;
