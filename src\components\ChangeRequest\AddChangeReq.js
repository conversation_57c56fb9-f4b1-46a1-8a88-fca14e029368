'use client';

import React, { useState, useRef } from 'react';
import { Box, Typography, Grid } from '@mui/material';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { useDropzone } from 'react-dropzone';
import CloseIcon from '@mui/icons-material/Close';
import CollectionsIcon from '@mui/icons-material/Collections';
import { addChangeRequest } from '@/services/changeRequestService';

const AddChangeRequest = () => {
  const router = useRouter();
  const formikRef = useRef(null);

  const [UploadFile, setUploadFile] = useState([]);
  const [loader, setLoader] = useState(false);

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'application/pdf': [],
      'image/*': [],
    },
    onDrop: (acceptedFile, rejectedFiles) => {
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes

      const largeFiles = acceptedFile.filter((file) => file.size > maxSize);
      const allFiles = [...UploadFile, ...acceptedFile];
      if (rejectedFiles.length > 0) {
        setApiMessage('error', 'Please upload PDF/Image files only.');
      } else if (allFiles.length > 5) {
        setApiMessage('error', 'You can upload up to 5 files only.');
      } else if (largeFiles.length > 0) {
        setApiMessage('error', 'File size should be less than 5MB.');
      } else {
        if (allFiles && allFiles?.length > 0) {
          setUploadFile(allFiles);
        }
      }
    },
  });

  return (
    <>
      <Box>
        {
          <Box className="">
            {' '}
            <Formik
              innerRef={formikRef}
              initialValues={{
                subject: '',
                old_value: '',
                new_value: '',
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                subject: Yup.string().trim().required('This field is required'),
                old_value: Yup.string()
                  .trim()
                  .required('This field is required'),
                new_value: Yup.string()
                  .trim()
                  .required('This field is required'),
              })}
              onSubmit={async (requestData) => {
                setLoader(true);
                try {
                  const { status, data } = await addChangeRequest(
                    requestData,
                    UploadFile
                  );
                  if (status === 200) {
                    if (data?.status) {
                      setApiMessage('success', data?.message);
                      router.push('/change-request');
                    } else {
                      setApiMessage('error', data?.message);
                    }
                  }
                } catch (error) {
                  setApiMessage('error', error?.response?.data?.message);
                } finally {
                  setLoader(false);
                }
              }}
            >
              {({
                errors,
                touched,
                handleBlur,
                values,
                handleSubmit,
                handleChange,
                dirty,
                isValid,
              }) => (
                <Form onSubmit={handleSubmit}>
                  <Box className="">
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Box className="">
                          <CustomTextField
                            fullWidth
                            required
                            name="subject"
                            value={values?.subject}
                            label="Subject"
                            placeholder="Enter Subject"
                            error={Boolean(touched.subject && errors.subject)}
                            helperText={touched.subject && errors.subject}
                            onBlur={handleBlur}
                            onChange={handleChange}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Box className="pt8 ">
                          <CustomTextField
                            fullWidth
                            required
                            name="old_value"
                            value={values?.old_value}
                            label="Old information"
                            placeholder="Enter Old information"
                            error={Boolean(
                              touched.old_value && errors.old_value
                            )}
                            helperText={touched.old_value && errors.old_value}
                            multiline
                            rows={3}
                            onBlur={handleBlur}
                            onChange={handleChange}
                          />
                        </Box>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Box className="pt8 ">
                          <CustomTextField
                            fullWidth
                            required
                            name="new_value"
                            value={values?.new_value}
                            label="New information"
                            multiline
                            rows={3}
                            placeholder="Enter New information"
                            error={Boolean(
                              touched.new_value && errors.new_value
                            )}
                            helperText={touched.new_value && errors.new_value}
                            onBlur={handleBlur}
                            onChange={handleChange}
                          />
                        </Box>
                      </Grid>

                      <Grid item xs={12}>
                        <Box className="Upload-files ">
                          <Typography className="field-label pt8 pb4">
                            Attached file (max 5)
                          </Typography>
                          <Box className="upload-sec cursor-pointer w100">
                            <Box
                              {...getRootProps({ className: 'dropzone' })}
                              className="upload-area text-align"
                            >
                              <CollectionsIcon className="upload-icon-wrap" />
                              <input {...getInputProps()} />
                              <Typography className="title-text upload-text">
                                <span className="blue-text">Browse</span> or
                                Drop your image here
                              </Typography>
                            </Box>
                          </Box>
                          {UploadFile?.length > 0 &&
                            UploadFile?.map((f, index) => {
                              return (
                                <Box className="selected-files" key={index}>
                                  <Box className="file-name">
                                    <Typography className="title-text">
                                      {f?.name}
                                    </Typography>
                                  </Box>
                                  <CloseIcon
                                    onClick={() => {
                                      const files = UploadFile;
                                      const filter = files?.filter(
                                        (s, i) => i !== index
                                      );
                                      setUploadFile(filter);
                                    }}
                                  />
                                </Box>
                              );
                            })}
                        </Box>
                      </Grid>
                      {/* <Grid item xs={12} md={6}></Grid> */}
                    </Grid>
                  </Box>

                  <Box className="d-flex justify-end">
                    <CustomButton
                      variant="contained"
                      type="submit"
                      title={`${
                        loader ? 'Change request...' : 'Add Change request'
                      }`}
                      disabled={loader || !isValid || !dirty}
                    />
                  </Box>
                </Form>
              )}
            </Formik>
          </Box>
        }
      </Box>
    </>
  );
};

export default AddChangeRequest;
