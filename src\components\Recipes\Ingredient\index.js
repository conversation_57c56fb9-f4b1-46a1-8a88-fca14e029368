'use client';
import { useSearchParams } from 'next/navigation';
import IngredientItems from './IngredientItems';
import AddEditIngredient from './AddEditIngredient';

export default function Ingredient() {
  const searchParams = useSearchParams();
  const ingredientId = searchParams.get('id');

  // If there's an ID in the search params, show the edit form
  if (ingredientId) {
    return <AddEditIngredient isUpdate={true} />;
  }

  // Otherwise, show the list view
  return <IngredientItems />;
}
