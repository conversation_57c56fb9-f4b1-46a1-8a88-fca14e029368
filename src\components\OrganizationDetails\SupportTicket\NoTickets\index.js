import { EmptyTicketIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import React from 'react';
import './notickets.scss';
import CustomButton from '@/components/UI/button';

export default function NoTickets({ onAddTicketClick }) {
  return (
    <Box className="no-ticket-wrap d-flex flex-col align-center">
      <Box className="ticket-icon-wrap">
        <EmptyTicketIcon />
      </Box>
      <Typography className="no-ticket-text-wrap" variant="h5">
        You don't have any Tickets
      </Typography>
      <Typography className="text-wrap" component="p">
        Get started by adding new Tickets to your help desk
      </Typography>
      <Box className="add-button-wrap">
        <CustomButton
          className="p16 add-ticket-btn"
          type="button" // Change to 'button' instead of 'submit'
          fontWeight="600"
          variant="contained"
          background="#39596e"
          backgroundhover="#39596e"
          colorhover="#FFFFFF"
          title="Add Ticket"
          onClick={onAddTicketClick} // Trigger the function passed as prop
        />
      </Box>
    </Box>
  );
}
