'use client';
import React, { useRef, useEffect, useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomButton from '@/components/UI/CustomButton';
import PreviewModeSelector from './components/PreviewModeSelector';
import RecipeHeader from './components/RecipeHeader';
import IngredientsCard from './components/IngredientsCard';
import CostAnalysisCard from './components/CostAnalysisCard';
import InstructionsCard from './components/InstructionsCard';
import ChefTipsGrid from './components/ChefTipsGrid';
import ServingInfoCard from './components/ServingInfoCard';
import { useReactToPrint } from 'react-to-print';
import axiosInstance from '@/helper/axios/axiosInstanceOrg';
import { RECIPE_URLS } from '@/helper/constants/urls';
import { setApiMessage } from '@/helper/common/commonFunctions';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import './preview.scss';

const RecipePreviewView = ({ slug }) => {
  const printRef = useRef();
  const [recipeData, setRecipeData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRecipeData = async () => {
      if (!slug) return;

      try {
        setIsLoading(true);
        const { status, data } = await axiosInstance.get(
          `${RECIPE_URLS.RECIPE_PREVIEW}/${slug}`
        );

        if (status === 200 && data?.data) {
          setRecipeData(data.data);
        } else {
          setApiMessage('error', 'Recipe not found');
        }
      } catch (error) {
        setApiMessage(
          'error',
          error?.response?.data?.message || 'Failed to fetch recipe data'
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecipeData();
  }, [slug]);

  const handlePrint = useReactToPrint({
    content: () => printRef.current,
    documentTitle: 'Recipe Preview',
    removeAfterPrint: true,
  });

  const handleExport = () => {
    // Mock export functionality
    // Export logic would go here
  };

  if (isLoading) {
    return <ContentLoader />;
  }

  if (!recipeData) {
    return (
      <NoDataView
        title="Recipe Not Found"
        description="The requested recipe could not be found. Please check the URL and try again."
      />
    );
  }

  return (
    <div className="recipe-preview">
      {/* Header Controls */}
      <div className="recipe-preview__header">
        <div className="recipe-preview__header-content">
          <div className="recipe-preview__header-left">
            <PreviewModeSelector />
          </div>

          <div className="recipe-preview__header-actions">
            <CustomButton
              variant="outlined"
              title="Print"
              leftIcon={<Icon name="Printer" size={16} />}
              onClick={handlePrint}
              className="recipe-preview__action-btn"
            />

            <CustomButton
              variant="contained"
              title="Export"
              leftIcon={<Icon name="Download" size={16} />}
              onClick={handleExport}
              className="recipe-preview__action-btn"
            />
          </div>
        </div>
      </div>
      <div ref={printRef}>
        <div className="recipe-preview__container">
          <RecipeHeader recipeData={recipeData} />

          <div className="recipe-preview__content-grid">
            {/* Left Column - Ingredients & Cost */}
            <div className="recipe-preview__left-column">
              <IngredientsCard ingredients={recipeData.ingredients} />
              <CostAnalysisCard recipeData={recipeData} />
            </div>

            {/* Right Column - Instructions & Chef Notes */}
            <div className="recipe-preview__right-column">
              <InstructionsCard instructions={recipeData.steps} />
              <ChefTipsGrid recipeData={recipeData} />
              <ServingInfoCard recipeData={recipeData} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecipePreviewView;
