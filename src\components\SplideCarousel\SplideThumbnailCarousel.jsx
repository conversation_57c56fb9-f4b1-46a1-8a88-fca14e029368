'use client';
import React, { useRef, useEffect, useState } from 'react';
import { Splide, SplideSlide } from '@splidejs/react-splide';
import DialogBox from '../UI/Modalbox';
import '@splidejs/splide/css';
import './thumbnailCarousel.scss';

const SplideThumbnailCarousel = ({
  media = [],
  thumbPosition = 'bottom', // 'top' | 'bottom' | 'left' | 'right'
}) => {
  const mainRef = useRef(null);
  const thumbRef = useRef(null);
  const [addEditModal, setAddEditModal] = useState(false);
  const [carouselHeight, setCarouselHeight] = useState(0);
  const [activeSlide, setActiveSlide] = useState(0);

  const handleOpenAddEditModal = () => {
    setAddEditModal(true);
  };

  const handleCloseAddEditModal = () => {
    setAddEditModal(false);
  };

  useEffect(() => {
    if (mainRef.current && thumbRef.current) {
      const main = mainRef.current.splide;
      const thumbs = thumbRef.current.splide;

      if (main && thumbs) {
        main.sync(thumbs);

        main.on('move', () => {
          const videos = document.querySelectorAll('.main-carousel video');
          videos.forEach((video) => video.pause());
        });
      }
    }
  }, []);

  const isImage = (url) => /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
  const isVideo = (url) => /\.(mp4|webm|ogg)$/i.test(url);
  const isYouTube = (url) =>
    /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/.test(url);

  const getYouTubeEmbedUrl = (url) => {
    const videoIdMatch = url.match(
      /(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([\w-]+)/
    );
    return videoIdMatch
      ? `https://www.youtube.com/embed/${videoIdMatch[1]}?rel=0&autoplay=1`
      : null;
  };
  const getYouTubeThumbnail = (url) => {
    const videoIdMatch = url.match(
      /(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([\w-]+)/
    );
    return videoIdMatch
      ? `https://img.youtube.com/vi/${videoIdMatch[1]}/hqdefault.jpg`
      : null;
  };
  const isHorizontal = thumbPosition === 'top' || thumbPosition === 'bottom';
  const isVertical = thumbPosition === 'left' || thumbPosition === 'right';

  useEffect(() => {
    const carousel = document.getElementById('main-carousel');
    if (carousel) {
      setCarouselHeight(carousel.offsetHeight);
    }
  }, []);

  const renderMain = (startAt = 0) => (
    <Splide
      ref={mainRef}
      options={{
        type: 'fade',
        rewind: true,
        pagination: false,
        arrows: true,
        cover: true,
        heightRatio: 0.5625, // 16:9
        start: startAt,
        breakpoints: {
          768: { heightRatio: 0.75 }, // 4:3 on smaller screens
        },
      }}
      className="main-carousel"
      id="main-carousel"
    >
      {media.map((item, index) => (
        <SplideSlide
          key={index}
          onClick={() => {
            setActiveSlide(index);
            handleOpenAddEditModal();
          }}
          style={{
            cursor: 'pointer',
            position: 'relative',
          }}
        >
          {isImage(item) ? (
            <img loading="lazy" src={item} alt={`media-${index}`} />
          ) : isVideo(item) ? (
            <video autoPlay muted playsInline controls preload="metadata">
              <source src={item} type="video/mp4" />
              Your browser does not support the video tag.
            </video>
          ) : isYouTube(item) ? (
            <iframe
              src={getYouTubeEmbedUrl(item)}
              title={`youtube-video-${index}`}
              allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              loading="lazy"
            />
          ) : null}
        </SplideSlide>
      ))}
    </Splide>
  );

  const renderThumbnails = () => (
    <Splide
      ref={thumbRef}
      options={{
        fixedWidth: isHorizontal ? 100 : undefined,
        fixedHeight: isVertical ? 64 : undefined,
        gap: '0.5rem',
        rewind: true,
        pagination: false,
        isNavigation: true,
        direction: isVertical ? 'ttb' : 'ltr',
        // wheel: true,
        // releaseWheel: true,
        height: isHorizontal ? undefined : carouselHeight + 'px',
        breakpoints: {
          768: {
            fixedWidth: isHorizontal ? 70 : undefined,
            fixedHeight: isVertical ? 48 : undefined,
          },
        },
      }}
      className={`thumbnail-carousel thumbnail-${thumbPosition}`}
    >
      {media.map((item, index) => (
        <SplideSlide key={index}>
          {isImage(item) ? (
            <img loading="lazy" src={item} alt={`thumb-${index}`} />
          ) : isVideo(item) ? (
            <video muted playsInline preload="metadata">
              <source src={item} type="video/mp4" />
            </video>
          ) : isYouTube(item) ? (
            <img
              loading="lazy"
              src={getYouTubeThumbnail(item)}
              alt={`thumb-${index}`}
            />
          ) : null}
        </SplideSlide>
      ))}
    </Splide>
  );

  return (
    <div className={`sync-carousel ${thumbPosition}`}>
      {(thumbPosition === 'top' || thumbPosition === 'left') &&
        renderThumbnails()}
      {renderMain()}
      {(thumbPosition === 'bottom' || thumbPosition === 'right') &&
        renderThumbnails()}

      <DialogBox
        open={addEditModal}
        handleClose={handleCloseAddEditModal}
        className="fullscreen-dialog-box-container"
        content={
          <div className={`sync-carousel`}>{renderMain(activeSlide)}</div>
        }
      />
    </div>
  );
};

export default SplideThumbnailCarousel;
