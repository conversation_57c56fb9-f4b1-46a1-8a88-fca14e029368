import { Box, Typography } from '@mui/material';
import Image from 'next/image';

import './recipecard.scss';

const recipeData = [
  {
    id: 1,
    image:
      'https://images.giftpro.co.uk/product-image/500/0a41ac62-60de-4faa-b216-29b9b6ca6768.jpg',
    name: 'Classic Margherita Pizza',
    price: '£15.00',
    from: false,
  },
  {
    id: 2,
    image:
      'https://images.giftpro.co.uk/product-image/500/a85a1f34-3ac2-4550-87fc-f58d2e19d87a.png',
    name: 'Grilled Salmon',
    price: '£18.00',
    from: true,
  },
  {
    id: 3,
    image:
      'https://images.giftpro.co.uk/product-image/500/57f3bc90-8c28-42a8-bee5-2682a772dae7.jpg',
    name: 'Vegetable Pasta',
    price: '£14.00',
    from: true,
  },
];

export default function RecipeCard() {
  return (
    <Box className="recipe-card-section">
      <Box className="recipe-card-wrap">
        {recipeData.map((recipe) => (
          <Box key={recipe?.id} className="recipe-card">
            <Box className="recipe-card-image-wrap">
              <Image
                src={recipe?.image}
                alt={recipe?.name}
                width={397}
                height={221}
              />
            </Box>
            <Box className="recipe-card-content">
              <Typography className="brown-title-text recipe-card-title">
                {recipe?.name}
              </Typography>
              <Box
                className={`${recipe?.from ? 'recipe-card-price-wrap' : 'recipe-card-no-from'}`}
              >
                <Typography className="brown-content-small-text recipe-card-from ">
                  {recipe?.from ? 'From' : ''}
                </Typography>
                <Typography className="body-text brown-title-text recipe-card-price">
                  {recipe?.price}
                </Typography>
              </Box>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
}
