import React, { useState, useEffect, useRef } from 'react';
import * as Yup from 'yup';
import Icon from '@/components/UI/AppIcon/AppIcon';
import Image from '@/components/UI/AppImage/AppImage';
import CustomButton from '@/components/UI/CustomButton';
import CustomTextField from '@/components/UI/CustomTextField';
import './MediaUploadSection.scss';

// File validation constraints
const FILE_CONSTRAINTS = {
  mainImage: {
    maxSize: 10 * 1024 * 1024,
    types: ['image/jpeg', 'image/png', 'image/webp'],
    required: true,
  },
  additionalImages: {
    maxSize: 50 * 1024 * 1024, // Max size for videos, images will be smaller
    types: [
      'image/jpeg',
      'image/png',
      'image/webp', // Images
      'video/mp4',
      'video/mov',
      'video/avi', // Videos
    ],
    required: false,
  },
  documents: {
    maxSize: 25 * 1024 * 1024,
    types: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    required: false,
  },
  audio: {
    maxSize: 25 * 1024 * 1024,
    types: ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/m4a'],
    required: false,
  },
  links: {
    maxSize: 0, // No file size for links
    types: [], // No file types for links
    required: false,
  },
};

// Validation Schema
const mediaValidationSchema = Yup.object().shape({
  mainImage: Yup.mixed().required('Main image is required'),
  additionalImages: Yup.array().max(7, 'Maximum 7 images and videos allowed'),
  documents: Yup.array().max(5, 'Maximum 5 documents allowed'),
  audio: Yup.array().max(3, 'Maximum 3 audio files allowed'),
  links: Yup.array().max(7, 'Maximum 7 links allowed'),
});

const MediaUploadSection = ({ formData, dispatch, validationErrors = {} }) => {
  const inputRef = useRef(null);
  const [dragOver, setDragOver] = useState(null);
  const [uploadErrors, setUploadErrors] = useState({});

  const mediaDetails = formData?.media;

  // Local state for media to ensure immediate UI updates
  const [localMediaData, setLocalMediaData] = useState(() => {
    return (
      mediaDetails || {
        mainImage: null,
        additionalImages: [], // Combined images and videos
        documents: [],
        audio: [],
        links: [],
      }
    );
  });

  // Sync local state with prop changes
  useEffect(() => {
    setLocalMediaData(
      mediaDetails || {
        mainImage: null,
        additionalImages: [], // Combined images and videos
        documents: [],
        audio: [],
        links: [],
      }
    );
  }, [mediaDetails]);

  // Use local state for immediate UI updates
  const mediaData = localMediaData;

  // Get appropriate icon for file type
  const getFileTypeIcon = (file) => {
    const fileType = file?.type?.toLowerCase() || '';
    const fileName = file?.name?.toLowerCase() || '';

    // Images
    if (fileType.startsWith('image/')) {
      return { name: 'Image', color: '#10B981' };
    }

    // Videos
    if (fileType.startsWith('video/')) {
      return { name: 'Video', color: '#8B5CF6' };
    }

    // Audio files
    if (
      fileType.startsWith('audio/') ||
      fileName.endsWith('.mp3') ||
      fileName.endsWith('.wav') ||
      fileName.endsWith('.m4a')
    ) {
      return { name: 'Music', color: '#F59E0B' };
    }

    // PDF files
    if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      return { name: 'FileText', color: '#EF4444' };
    }

    // Word documents
    if (
      fileType.includes('word') ||
      fileName.endsWith('.doc') ||
      fileName.endsWith('.docx')
    ) {
      return { name: 'FileText', color: '#2563EB' };
    }

    // Default file icon
    return { name: 'File', color: '#6B7280' };
  };

  // Validate file constraints
  const validateFile = (file, type) => {
    const constraints = FILE_CONSTRAINTS[type];
    const errors = [];

    if (!constraints) {
      errors.push('Invalid file type');
      return errors;
    }

    // Check file type
    if (
      constraints?.types?.length > 0 &&
      !constraints.types.includes(file?.type)
    ) {
      errors.push(`File type ${file?.type} not supported`);
    }

    // Check file size - special handling for additionalImages (images vs videos)
    if (type === 'additionalImages') {
      const isImage = file?.type?.startsWith('image/');
      const isVideo = file?.type?.startsWith('video/');

      if (isImage && file?.size > 5 * 1024 * 1024) {
        // 5MB for images
        errors.push('Image size exceeds 5MB limit');
      } else if (isVideo && file?.size > 50 * 1024 * 1024) {
        // 50MB for videos
        errors.push('Video size exceeds 50MB limit');
      }
    } else {
      // Standard size check for other file types
      if (file?.size > constraints?.maxSize) {
        const maxSizeMB = (constraints?.maxSize / 1024 / 1024)?.toFixed?.(0);
        errors.push(`File size exceeds ${maxSizeMB}MB limit`);
      }
    }

    return errors;
  };

  // Validate media data
  const validateMediaData = async (updatedMedia) => {
    try {
      await mediaValidationSchema.validate(updatedMedia);
      return null;
    } catch (error) {
      return error?.message || 'Invalid media data';
    }
  };

  const handleFileUpload = async (type, files) => {
    if (!files?.length) return;

    // Clear previous errors for this type
    setUploadErrors((prev) => ({ ...prev, [type]: null }));

    // Simulate file upload - in real app, this would upload to server
    const fileArray = Array.from(files);
    const validFiles = [];
    const typeErrors = [];

    // Get current files and limits
    const currentFiles = mediaData?.[type] || [];
    const maxFiles =
      type === 'mainImage'
        ? 1
        : type === 'additionalImages'
          ? 7
          : type === 'documents'
            ? 5
            : type === 'audio'
              ? 3
              : 7;

    // For main image, only allow 1 file
    if (type === 'mainImage' && fileArray.length > 1) {
      typeErrors.push(
        'Only one main image is allowed. Please select a single file.'
      );
    }

    // Check if adding these files would exceed the limit
    const availableSlots = maxFiles - currentFiles.length;
    if (fileArray.length > availableSlots && availableSlots > 0) {
      typeErrors.push(
        `Cannot add ${fileArray.length} files. Only ${availableSlots} slots available (${currentFiles.length}/${maxFiles} used).`
      );
    } else if (availableSlots <= 0) {
      typeErrors.push(
        `Cannot add more files. Maximum limit reached (${currentFiles.length}/${maxFiles}).`
      );
    }

    // Validate each file
    fileArray?.forEach?.((file, index) => {
      const fileErrors = validateFile(file, type);
      if (fileErrors?.length > 0) {
        typeErrors.push(
          `File ${index + 1} (${file?.name || 'unknown'}): ${fileErrors.join(', ')}`
        );
      } else {
        validFiles.push(file);
      }
    });

    // Set upload errors if any
    if (typeErrors?.length > 0) {
      setUploadErrors((prev) => ({ ...prev, [type]: typeErrors }));
      return;
    }

    // Process all valid files at once
    if (validFiles.length > 0) {
      // Get current files and available space
      const currentFiles = mediaData?.[type] || [];
      const availableSpace = maxFiles - currentFiles.length;
      const filesToProcess = validFiles.slice(0, availableSpace);

      // Create file data for all files at once
      const newFileDataArray = filesToProcess.map((file, index) => ({
        id: Date.now() + Math.random() + index, // Ensure unique IDs
        name: file?.name || 'unknown',
        size: file?.size || 0,
        type: file?.type || 'unknown',
        url: URL.createObjectURL(file),
        uploadedAt: new Date(),
        file: file, // Store actual file for potential upload
      }));

      let updatedMediaData = { ...mediaData };

      if (type === 'mainImage') {
        // For main image, only take the first file
        updatedMediaData.mainImage = newFileDataArray[0];
      } else {
        // For other types, add all valid files to existing array
        updatedMediaData[type] = [...currentFiles, ...newFileDataArray];
      }

      // Update local state immediately for UI responsiveness (single update)
      setLocalMediaData(updatedMediaData);

      // Validate updated media data
      await validateMediaData(updatedMediaData);

      // Dispatch to parent (single dispatch)
      if (dispatch) {
        if (type === 'mainImage') {
          dispatch({
            type: 'UPDATE_MEDIA',
            payload: { mainImage: updatedMediaData.mainImage },
          });
        } else {
          dispatch({
            type: 'UPDATE_MEDIA',
            payload: { [type]: updatedMediaData[type] },
          });
        }
      }
    }
  };

  // URL validation regex
  const urlRegex =
    /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/;

  // Validate URL format
  const validateUrl = (url) => {
    if (!url?.trim()) return 'URL is required';
    if (!urlRegex.test(url.trim())) return 'Please enter a valid URL';
    return null;
  };

  // Handle link addition
  const handleLinkAdd = async (linkUrl, linkTitle = '') => {
    if (!linkUrl?.trim()) {
      setUploadErrors({ links: ['URL is required'] });
      return;
    }

    // Validate URL format
    const urlError = validateUrl(linkUrl);
    if (urlError) {
      setUploadErrors({ links: [urlError] });
      return;
    }

    const currentLinks = mediaData?.links || [];
    if (currentLinks?.length >= 7) {
      setUploadErrors({ links: ['Maximum 7 links allowed'] });
      return;
    }

    // Clear previous errors
    setUploadErrors({});
    inputRef.current.value = '';

    const linkData = {
      id: Date.now() + Math.random(),
      url: linkUrl.trim(),
      title: linkTitle.trim() || linkUrl.trim(),
      type: 'link',
      addedAt: new Date(),
    };

    const updatedMediaData = {
      ...mediaData,
      links: [...currentLinks, linkData],
    };

    // Update local state immediately
    setLocalMediaData(updatedMediaData);

    // Validate updated media data
    await validateMediaData(updatedMediaData);

    // Dispatch to parent
    if (dispatch) {
      dispatch({
        type: 'UPDATE_MEDIA',
        payload: { links: updatedMediaData.links },
      });
    }
  };

  const handleDragOver = (e, type) => {
    e?.preventDefault?.();
    setDragOver(type);
  };

  const handleDragLeave = (e) => {
    e?.preventDefault?.();
    setDragOver(null);
  };

  const handleDrop = (e, type) => {
    e?.preventDefault?.();
    setDragOver(null);
    const files = e?.dataTransfer?.files;
    if (files) {
      handleFileUpload(type, files);
    }
  };

  const removeFile = async (type, fileId) => {
    let updatedMediaData = { ...mediaData };

    if (type === 'mainImage') {
      updatedMediaData.mainImage = null;
    } else {
      const currentFiles = mediaData?.[type] || [];
      const updatedFiles =
        currentFiles?.filter?.((file) => file?.id !== fileId) || [];
      updatedMediaData[type] = updatedFiles;
    }

    // Update local state immediately for UI responsiveness
    setLocalMediaData(updatedMediaData);

    // Validate updated media data
    await validateMediaData(updatedMediaData);

    // Dispatch to parent
    if (dispatch) {
      if (type === 'mainImage') {
        dispatch({
          type: 'UPDATE_MEDIA',
          payload: { mainImage: null },
        });
      } else {
        dispatch({
          type: 'UPDATE_MEDIA',
          payload: { [type]: updatedMediaData[type] },
        });
      }
    }
  };

  const FileUploadZone = ({
    type,
    title,
    description,
    maxFiles,
    currentFiles = [],
    accept,
    icon,
  }) => (
    <div className="media-upload-section__upload-zone">
      <div className="media-upload-section__zone-header">
        <h3 className="media-upload-section__zone-title">{title}</h3>
        <span className="media-upload-section__zone-counter">
          {currentFiles?.length || 0}/{maxFiles} files
        </span>
      </div>

      <div
        onDragOver={(e) => handleDragOver(e, type)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, type)}
        className={`media-upload-section__drop-area ${
          dragOver === type ? 'media-upload-section__drop-area--drag-over' : ''
        } ${
          (currentFiles?.length || 0) >= maxFiles
            ? 'media-upload-section__drop-area--disabled'
            : ''
        }`}
      >
        <input
          type="file"
          multiple={true}
          accept={accept}
          onChange={(e) => handleFileUpload(type, e?.target?.files)}
          className="media-upload-section__drop-input"
          disabled={(currentFiles?.length || 0) >= maxFiles}
        />

        <div className="media-upload-section__drop-content">
          <Icon
            name={icon}
            size={32}
            className="media-upload-section__drop-icon"
          />
          <div className="media-upload-section__drop-text">
            <p className="media-upload-section__drop-title">
              {dragOver === type
                ? 'Drop files here'
                : 'Drag & drop files or click to browse'}
            </p>
            <p className="media-upload-section__drop-description">
              {description}
            </p>
          </div>
        </div>
      </div>

      {/* File Preview Grid */}
      {(currentFiles?.length || 0) > 0 && (
        <div className="media-upload-section__preview-grid">
          {currentFiles?.map?.((file) => (
            <div key={file?.id} className="media-upload-section__preview-item">
              <div className="media-upload-section__preview-container">
                {file?.type?.startsWith?.('image/') ? (
                  <Image
                    src={file?.url}
                    alt={file?.name || 'Preview'}
                    className="media-upload-section__preview-image"
                  />
                ) : (
                  <div className="media-upload-section__preview-placeholder">
                    {(() => {
                      const iconData = getFileTypeIcon(file);
                      return (
                        <Icon
                          name={iconData.name}
                          size={24}
                          color={iconData.color}
                        />
                      );
                    })()}
                  </div>
                )}
              </div>

              <Icon
                name="X"
                size={16}
                className="media-upload-section__preview-remove"
                onClick={() => removeFile(type, file?.id)}
              />

              <div className="media-upload-section__preview-info">
                <p
                  className="media-upload-section__preview-name"
                  title={file?.name}
                >
                  {file?.name || 'Unknown file'}
                </p>
                <p className="media-upload-section__preview-size">
                  {((file?.size || 0) / 1024 / 1024)?.toFixed?.(1) || '0.0'} MB
                </p>
              </div>
            </div>
          )) || []}
        </div>
      )}

      {/* Error Display */}
      {uploadErrors?.[type] && (
        <div className="media-upload-section__error">
          <Icon name="AlertCircle" size={16} />
          <span className="other-field-error-text">
            {Array.isArray(uploadErrors[type])
              ? uploadErrors[type].join(', ')
              : uploadErrors[type]}
          </span>
        </div>
      )}
    </div>
  );

  return (
    <div className="media-upload-section">
      {/* Main Recipe Image */}
      <div className="media-upload-section__main-image">
        <h3 className="media-upload-section__main-image-title">
          Main Recipe Image{' '}
          <span className="media-upload-section__required"> * </span>
        </h3>

        {mediaData?.mainImage ? (
          <div className="media-upload-section__main-image-container">
            <div className="media-upload-section__main-image-preview">
              <Image
                src={mediaData?.mainImage?.url}
                alt={mediaData?.mainImage?.name || 'Main recipe image'}
                className="media-upload-section__main-image-img"
              />
            </div>

            <Icon
              name="X"
              size={16}
              className="media-upload-section__remove-button"
              onClick={() => removeFile('mainImage', mediaData?.mainImage?.id)}
            />
          </div>
        ) : (
          <div className="media-upload-section__main-image-upload-container">
            <div
              onDragOver={(e) => handleDragOver(e, 'mainImage')}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, 'mainImage')}
              className={`media-upload-section__main-image-drop ${
                dragOver === 'mainImage'
                  ? 'media-upload-section__main-image-drop--drag-over'
                  : ''
              }`}
            >
              <input
                type="file"
                accept="image/*"
                onChange={(e) =>
                  handleFileUpload('mainImage', e?.target?.files)
                }
                className="media-upload-section__drop-input"
              />

              <div className="media-upload-section__drop-content">
                <Icon
                  name="ImagePlus"
                  size={24}
                  className="media-upload-section__drop-icon"
                />
                <div className="media-upload-section__drop-text">
                  <p className="media-upload-section__drop-title">
                    {dragOver === 'mainImage'
                      ? 'Drop image here'
                      : 'Add main recipe image'}
                  </p>
                  <p className="media-upload-section__drop-description">
                    PNG, JPG up to 10MB
                  </p>
                </div>
              </div>
            </div>

            {/* Main Image Error Display */}
            {validationErrors?.mainImage ? (
              <div className="media-upload-section__error">
                <Icon name="AlertCircle" size={16} />
                <span className="other-field-error-text">
                  {validationErrors.mainImage}
                </span>
              </div>
            ) : (
              uploadErrors?.mainImage && (
                <div className="media-upload-section__error">
                  <Icon name="AlertCircle" size={16} />
                  <span className="other-field-error-text">
                    {uploadErrors.mainImage.join(', ')}
                  </span>
                </div>
              )
            )}
          </div>
        )}
      </div>

      {/* Media Files (Images & Videos Combined) */}
      <FileUploadZone
        type="additionalImages"
        title="Additional Images & Videos (Optional)"
        description="Images: PNG, JPG up to 5MB each | Videos: MP4, MOV up to 50MB each"
        maxFiles={7}
        currentFiles={mediaData?.additionalImages || []}
        accept="image/*,video/*"
        icon="FileImage"
      />

      {/* Documents */}
      <FileUploadZone
        type="documents"
        title="Documents & PDFs (Optional)"
        description="PDF, DOC up to 25MB each"
        maxFiles={5}
        currentFiles={mediaData?.documents || []}
        accept=".pdf,.doc,.docx"
        icon="FileText"
      />

      {/* Audio Files */}
      <FileUploadZone
        type="audio"
        title="Audio Files (Optional)"
        description="MP3, WAV up to 25MB each"
        maxFiles={3}
        currentFiles={mediaData?.audio || []}
        accept=".mp3,.wav,.m4a"
        icon="Music"
      />

      {/* Links Section */}
      <div className="media-upload-section__links">
        <div className="media-upload-section__zone-header">
          <h3 className="media-upload-section__zone-title">
            External Links (Optional)
          </h3>
          <span className="media-upload-section__zone-counter">
            {mediaData?.links?.length || 0}/7 links
          </span>
        </div>

        <div className="media-upload-section__link-input">
          <CustomTextField
            inputRef={inputRef}
            placeholder="Enter URL (e.g., YouTube, blog post, recipe source)"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleLinkAdd(e.target.value);
              }
            }}
            fullWidth
          />
          <CustomButton
            onClick={() => {
              if (inputRef.current) {
                handleLinkAdd(inputRef.current.value);
              }
            }}
            className="media-upload-section__link-add-button"
            disabled={(mediaData?.links?.length || 0) >= 7}
            title="Add Link"
            startIcon={<Icon name="Plus" size={16} />}
          />
        </div>

        {/* Links Error Display */}
        {uploadErrors?.links && (
          <div className="media-upload-section__error">
            <Icon name="AlertCircle" size={16} />
            <span className="other-field-error-text">
              {Array.isArray(uploadErrors.links)
                ? uploadErrors.links.join(', ')
                : uploadErrors.links}
            </span>
          </div>
        )}

        {/* Links Preview */}
        {(mediaData?.links?.length || 0) > 0 && (
          <div className="media-upload-section__links-grid">
            {mediaData?.links?.map?.((link) => (
              <div key={link?.id} className="media-upload-section__link-item">
                <div className="media-upload-section__link-icon">
                  <Icon name="ExternalLink" size={16} />
                </div>
                <div className="media-upload-section__link-content">
                  <a
                    href={link?.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="media-upload-section__link-url"
                  >
                    <p
                      className="media-upload-section__link-title"
                      title={link?.url}
                    >
                      {link?.url}
                    </p>
                  </a>
                </div>

                <Icon
                  name="X"
                  size={16}
                  className="media-upload-section__link-remove"
                  onClick={() => removeFile('links', link?.id)}
                />
              </div>
            )) || []}
          </div>
        )}
      </div>

      {/* Media Summary */}
      <div className="media-upload-section__summary">
        <h4 className="media-upload-section__summary-title">Media Summary</h4>
        <div className="media-upload-section__summary-grid">
          <div className="media-upload-section__summary-item">
            <div className="media-upload-section__summary-count">
              {mediaData?.mainImage ? 1 : 0}/1
            </div>
            <div className="media-upload-section__summary-label">
              Main Image
            </div>
          </div>
          <div className="media-upload-section__summary-item">
            <div className="media-upload-section__summary-count">
              {mediaData?.additionalImages?.length || 0}/7
            </div>
            <div className="media-upload-section__summary-label">
              Images & Videos
            </div>
          </div>
          <div className="media-upload-section__summary-item">
            <div className="media-upload-section__summary-count">
              {mediaData?.documents?.length || 0}/5
            </div>
            <div className="media-upload-section__summary-label">Documents</div>
          </div>
          <div className="media-upload-section__summary-item">
            <div className="media-upload-section__summary-count">
              {mediaData?.audio?.length || 0}/3
            </div>
            <div className="media-upload-section__summary-label">Audio</div>
          </div>
          <div className="media-upload-section__summary-item">
            <div className="media-upload-section__summary-count">
              {mediaData?.links?.length || 0}/7
            </div>
            <div className="media-upload-section__summary-label">Links</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaUploadSection;
