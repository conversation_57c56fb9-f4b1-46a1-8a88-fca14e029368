import Axios from 'axios';
import {
  clearStorage,
  errorHandler,
  fetchFromStorage,
  // removeFromStorage,
} from '../context';
import { Config } from '../context/config';
import { identifiers } from '@/helper/constants/identifier';

const getBaseURL = () => {
  if (typeof window === 'undefined') return `${Config.baseURL}/api`;

  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);

  const isNormalLogin =
    authdata?.organizationId || authdata?.organizationStatus;

  return !isNormalLogin
    ? `${Config.baseURL}/api`
    : `${Config.baseURL}/backend-api`;

  // return `${Config.baseURL}:3443/backend-api`;
};

// Create an instance of Axios
const axiosInstance = Axios.create({
  // baseURL: getBaseURL(),
  headers: {
    'Content-Type': 'application/json',
    'Platform-Type': 'web',
    'ngrok-skip-browser-warning': true,
  },
});

// const axiosInstance = Axios.create({
//   baseURL: Config.baseURL ,
//   headers: {
//     'Content-Type': 'application/json',
//     'Platform-Type': 'web',
//     'ngrok-skip-browser-warning': true,
//   },
// });

// Add a request interceptor
axiosInstance.interceptors.request.use((config) => {
  config.baseURL = getBaseURL();
  // Fetch the token from storage
  const token = fetchFromStorage(identifiers.AUTH_DATA)?.token;
  const deviceData = fetchFromStorage(identifiers?.DEVICEDATA);
  // Clone the request config to avoid modifying the original config
  const clonedConfig = { ...config };
  // Set common headers, including authorization if token is available
  if (token) {
    clonedConfig.headers = {
      ...clonedConfig.headers,
      Authorization: `Bearer ${token}`,
      'ngrok-skip-browser-warning': true,
      'accept-language': 'en',
      'Platform-Type': 'web',
      'Ip-Address': deviceData && deviceData?.ip ? deviceData?.ip : '',
      Address: deviceData && deviceData?.address ? deviceData?.address : '',
      Location: deviceData && deviceData?.location ? deviceData?.location : '',
    };
  } else {
    clonedConfig.headers = {
      ...clonedConfig.headers,
      'accept-language': 'en',
      'Platform-Type': 'web',
      'ngrok-skip-browser-warning': true,
      'Ip-Address': deviceData && deviceData?.ip ? deviceData?.ip : '',
      Address: deviceData && deviceData?.address ? deviceData?.address : '',
      Location: deviceData && deviceData?.location ? deviceData?.location : '',
    };
  }

  return clonedConfig;
});

// Add a response interceptor
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error?.response?.status === 401) {
      // removeFromStorage(identifiers?.AUTH_DATA);
      // removeFromStorage(identifiers?.USER_DATA);
      // window.location.reload();
      setTimeout(() => {
        clearStorage();
        const link = document.createElement('a');
        link.href = '/org/login';
        document.body.appendChild(link);
        link.click();
      }, []);
    }
    // Handle errors and call the errorHandler function
    errorHandler(error);
    return Promise.reject(error);
  }
);

export default axiosInstance;
