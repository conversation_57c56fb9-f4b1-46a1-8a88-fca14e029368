{"name": "namaste-village-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "local": "cross-env NEXT_NODE_ENV=development node ssr-server.js", "build": "next build", "start": "next start", "lint": "eslint", "lint:fix": "eslint --fix", "format": "prettier --write .", "test": "lint-staged", "start:staging": "next start -p 3001", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint", "prettier --write"]}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.11.5", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/multimonth": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/resource": "^6.1.15", "@fullcalendar/resource-timeline": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@mui/icons-material": "^5.15.14", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.14", "@mui/x-data-grid": "^7.1.1", "@mui/x-date-pickers": "^7.2.0", "@reduxjs/toolkit": "^2.2.3", "@splidejs/react-splide": "^0.7.12", "@splidejs/splide": "^4.1.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^7.1.0", "ag-charts-community": "^10.3.3", "ag-charts-react": "^10.3.3", "axios": "^1.6.8", "date-fns": "^3.6.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.10", "dotenv": "^16.4.5", "formik": "^2.4.5", "i": "^0.3.7", "intergalactic": "^15.117.0", "jodit-react": "^4.1.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.513.0", "moment": "^2.30.1", "next": "14.1.4", "next-redux-wrapper": "^8.1.0", "npm": "^10.6.0", "prop-types": "^15.8.1", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-beautiful-dnd-grid": "^0.1.3-alpha", "react-color": "^2.19.3", "react-d3-speedometer": "^2.2.1", "react-datepicker": "^7.4.0", "react-dom": "^18", "react-dropzone": "^14.2.3", "react-gauge-chart": "^0.5.1", "react-google-charts": "^5.2.1", "react-helmet": "^6.1.0", "react-infinite-scroll-component": "^6.1.0", "react-lazy-load-image-component": "^1.6.0", "react-load-script": "^0.0.6", "react-multi-date-picker": "^4.5.2", "react-onesignal": "^3.0.1", "react-otp-input": "^3.1.1", "react-pdf": "^8.0.0", "react-player": "^2.16.0", "react-redux": "^9.1.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-signature-canvas": "^1.0.6", "react-slick": "^0.30.2", "react-sortablejs": "^6.1.4", "react-to-print": "^2.15.1", "react-toastify": "^10.0.5", "recharts": "^2.12.7", "redux": "^5.0.1", "redux-saga": "^1.3.0", "sass": "^1.72.0", "sharp": "^0.33.3", "slick-carousel": "^1.8.1", "sortablejs": "^1.15.3", "stripe": "^18.0.0", "suneditor": "^2.47.0", "suneditor-react": "^3.6.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/eslint-parser": "^7.27.1", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@next/eslint-plugin-next": "^15.1.7", "eslint": "^9.20.1", "eslint-config-next": "^15.1.7", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.1"}}