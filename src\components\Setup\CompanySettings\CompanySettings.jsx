'use client';

import React, { useEffect, useState, useRef, useContext } from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useDropzone } from 'react-dropzone';
import CancelIcon from '@mui/icons-material/Cancel';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CollectionsIcon from '@mui/icons-material/Collections';
import { monthsOption, setApiMessage } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import PreLoader from '@/components/UI/Loader';
import { URLS } from '@/helper/constants/urls';
import HeaderImage from '@/components/UI/ImageSecurity';
import InfoIcon from '@mui/icons-material/Info';
import _ from 'lodash';
import CustomTextField from '../../UI/CustomTextField';
import CustomSelect from '../../UI/CustomSelect';
import CustomButton from '../../UI/CustomButton';
import CustomRadio from '../../UI/CustomRadio';
import './setting.scss';

export default function CompanySettings() {
  const { authState, setAuthState } = useContext(AuthContext);
  const formikRef = useRef(null);
  const [acceptFiles, setAcceptedFiles] = useState();
  const [imagePreview, setImagePreview] = useState('');
  const [emptymedia, setEmptymedia] = useState(false);
  const [generalSettings, setGeneralSettings] = useState();
  const [loader, setLoader] = useState(true);
  const [isLoader, setIsLoader] = useState(false);
  const [currenciesList, setCurrenciesList] = useState([
    {
      label: 'British pound (£)',
      value: {
        currency: 'GBP',
        name: 'British pound',
        symbol: '£',
      },
    },
  ]);
  const logoSize = 1;
  const logoResolution = 256;

  // Get general settings data
  const getSettingsDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_GENERAL_SETTINGS
      );

      if (status === 200) {
        setGeneralSettings(data?.data);
        setAuthState({
          ...authState,
          generalSeetings: data?.data,
        });
        setEmptymedia(false);
        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/jpeg': [],
      'image/jpg': [],
      'image/png': [],
    },
    multiple: false,
    onDrop: (acceptedFile) => {
      const file = acceptedFile[0];
      const maxSize = logoSize * 1024 * 1024; // logoSize MB in bytes

      if (!file) {
        setApiMessage('error', 'Please upload JPG, JPEG, or PNG files only.');
        return;
      }

      if (file.size > maxSize) {
        setApiMessage(
          'error',
          'File size should be less than or equal to 1MB.'
        );
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          if (img.width !== logoResolution || img.height !== logoResolution) {
            setApiMessage(
              'error',
              `Logo resolution must be exactly ${logoResolution}x${logoResolution} pixels.`
            );
          } else {
            // File is valid
            formikRef.current.setFieldValue('filename', file);
            setAcceptedFiles(file);
            setImagePreview(URL.createObjectURL(file));
          }
        };
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);

      // const largeFiles = acceptedFile.filter((file) => file?.size > maxSize);

      // if (rejectedFiles.length > 0) {
      //   setAcceptedFiles();
      // } else if (largeFiles.length > 0) {
      //   setApiMessage('error', 'File size should be less than 5MB.');
      // } else if (rejectedFiles.length > 0) {
      //   setApiMessage('error', 'Please upload PNG files only.');
      // } else {
      //   formikRef.current.setFieldValue('filename', acceptedFile[0]);
      //   setAcceptedFiles(acceptedFile[0]);
      //   setImagePreview(URL.createObjectURL(acceptedFile[0]));
      // }
    },
  });

  const getCurrencyList = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        `https://restcountries.com/v3.1/all?fields=currencies`
      );
      if (status === 200) {
        setLoader(false);

        let currencies = data
          .flatMap((country) =>
            Object.entries(country.currencies || {}).map(
              ([currencyCode, currency]) => ({
                value: {
                  currency: currencyCode,
                  name: currency?.name,
                  symbol: currency?.symbol,
                },
                label: `${currency?.name} (${currency?.symbol})`,
              })
            )
          )
          .filter(
            (value, index, self) =>
              index ===
              self.findIndex(
                (t) =>
                  t.value.currency === value.value.currency &&
                  t.value.name === value.value.name &&
                  t.value.symbol === value.value.symbol
              )
          );
        const sortbyname = _.orderBy(currencies, 'label', ['asc']);
        setCurrenciesList(sortbyname);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    if (generalSettings?.brand_logo) {
      setAcceptedFiles({
        name: generalSettings?.brand_logo,
        link: generalSettings?.brand_logo_link,
        type: 'image',
        isAdded: true,
      });
      setImagePreview(generalSettings?.brand_logo_link);
      // formikRef.current.setFieldValue('filename', generalSettings?.brand_logo);
      setEmptymedia(false);
    }
  }, [generalSettings, generalSettings?.brand_logo]);

  useEffect(() => {
    // if (
    //   authState?.UserPermission?.setting === 1 ||
    //   authState?.UserPermission?.setting === 2
    // ) {
    getSettingsDetails();
    getCurrencyList();
    // }
  }, [authState?.UserPermission?.setting]);

  function getFinancialYear(startMonth, isSmall) {
    const currentYear = new Date().getFullYear();
    const startMonthIndex = new Date(
      `${startMonth} 1, ${currentYear}`
    ).getMonth();

    const endMonth = new Date(
      currentYear,
      startMonthIndex + 11,
      1
    ).toLocaleString('en-US', { month: 'long' });
    let capitalizedMonth =
      startMonth.charAt(0).toUpperCase() + startMonth.slice(1);
    if (isSmall) {
      return `${startMonth} - ${endMonth.toLowerCase()}`;
    }
    return `${capitalizedMonth} - ${endMonth}`;
  }

  return (
    <>
      {loader && <PreLoader />}
      <Box className="general-settings">
        <Formik
          innerRef={formikRef}
          initialValues={{
            name: generalSettings?.name || '',
            phoneno: generalSettings?.phoneno || '',
            email: generalSettings?.email || '',
            address: generalSettings?.address || '',
            months: generalSettings?.financial_month?.split(' - ')?.[0] || '',
            currency:
              generalSettings && generalSettings?.currency
                ? JSON.parse(generalSettings?.currency)
                : {
                    currency: 'GBP',
                    name: 'British pound',
                    symbol: '£',
                  },
            base_leave: generalSettings?.base_leave || 5.6,
            working_hours_per_day: generalSettings?.working_hours_per_day || 7,
            max_limit_per_week: generalSettings?.max_limit_per_week || 35,
            LeaveType: generalSettings?.leave_period_type || 'day',
            deduction: generalSettings?.leave_calculation_type || 'auto',
            manageleave: '',
            filename: generalSettings?.brand_logo || '',
          }}
          enableReinitialize={true}
          validationSchema={Yup.object().shape({
            name: Yup.string().trim().required('This field is required'),
            email: Yup.string()
              .required('This field is required')
              .matches(
                /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i,
                'Please enter valid email'
              ),
            phoneno: Yup.string()
              .trim()
              .required('This field is required')
              .matches(/^[0-9]{10,11}$/, 'Invalid phone number'),
            address: Yup.string().trim().required('This field is required'),
            filename: Yup.string().trim().required('This field is required'),
            LeaveType: Yup.string().trim().required('This field is required'),
            deduction: Yup.string().trim().required('This field is required'),
            currency: Yup.object().shape({
              currency: Yup.string().trim().required('This field is required'),
              name: Yup.string().trim().required('This field is required'),
              symbol: Yup.string().trim().required('This field is required'),
            }),
            base_leave: Yup.number()
              .typeError('Base Leave must be a number')
              .required('This field is required'),
            months: Yup.string().trim().required('This field is required'),
            working_hours_per_day: Yup.number()
              .typeError('Working hours per Day must be a number')
              .required('This field is required'),
            max_limit_per_week: Yup.number()
              .typeError('Max Limit per Week must be a number')
              .required('This field is required'),
            manageleave: Yup.string()
              .trim()
              .test(
                'required-when-hour',
                'This field is required',
                function (value) {
                  const { LeaveType } = this.parent;
                  if (!generalSettings?.leave_period_type) {
                    return true;
                  }
                  if (LeaveType !== generalSettings?.leave_period_type) {
                    return !!value?.trim(); // Ensure it's not empty or just whitespace
                  }
                  return true; // Pass validation for other leave types
                }
              ),
          })}
          onSubmit={async (requestData) => {
            setIsLoader(true);
            const body = new FormData();
            requestData?.name && body.append('name', requestData?.name);
            requestData?.email && body.append('email', requestData?.email);
            requestData?.phoneno &&
              body.append('phoneno', requestData?.phoneno.toString());
            requestData?.address &&
              body.append('address', requestData?.address);
            requestData?.base_leave &&
              body.append('base_leave', requestData?.base_leave);
            requestData?.months &&
              body.append(
                'financial_month',
                getFinancialYear(requestData?.months, true)
              );
            requestData?.working_hours_per_day &&
              body.append(
                'working_hours_per_day',
                requestData?.working_hours_per_day
              );
            requestData?.max_limit_per_week &&
              body.append(
                'max_limit_per_week',
                requestData?.max_limit_per_week
              );
            requestData?.deduction &&
              body.append('leave_calculation_type', requestData?.deduction);
            requestData?.LeaveType &&
              body.append('leave_period_type', requestData?.LeaveType);
            requestData?.LeaveType !== generalSettings?.leave_period_type &&
              requestData?.manageleave &&
              body.append('leave_modify_options', requestData?.manageleave);
            if (requestData?.currency) {
              body.append('currency', JSON.stringify(requestData.currency));
            }
            acceptFiles &&
              !acceptFiles?.isAdded &&
              body.append('brand_logo', acceptFiles);
            const config = {
              headers: {
                'Content-Type': 'multipart/form-data',
              },
            };

            try {
              const { status, data } = await axiosInstance.post(
                URLS.GENERAL_SETTINGS,
                body,
                config
              );

              if (status === 200) {
                if (data?.status) {
                  setApiMessage('success', data?.message);
                  setEmptymedia(false);
                  getSettingsDetails();
                } else {
                  setApiMessage('error', data?.message);
                }
                setIsLoader(false);
              }
            } catch (error) {
              setIsLoader(false);
              setApiMessage('error', error?.response?.data?.message);
            }
          }}
        >
          {({
            errors,
            touched,
            handleBlur,
            setFieldValue,
            values,
            handleSubmit,
            handleChange,
            dirty,
            isValid,
          }) => {
            return (
              <Form onSubmit={handleSubmit}>
                <Box className="d-flex align-center gap-sm pb8">
                  <Typography className="other-field-label">
                    Brand Logo<span className="required">*</span>
                  </Typography>
                  <Tooltip
                    arrow
                    title={
                      <Typography className="sub-title-text">
                        Logo resolution should be {logoResolution}*
                        {logoResolution} with {logoSize} MB size only.
                        <br />
                        Supported file formats: JPG,JPEG,PNG.
                        <br />
                        This will appear on all your email templates, salary
                        slip and portal page.
                      </Typography>
                    }
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <InfoIcon className="info-icon" />
                  </Tooltip>
                </Box>
                {imagePreview ||
                (acceptFiles?.link && acceptFiles?.isAdded === true) ? (
                  <Box className="logo-section">
                    <HeaderImage
                      imageUrl={imagePreview || acceptFiles?.link}
                      alt="not found"
                      draggable="false"
                      className="cursor-pointer"
                      type="lazyload"
                    />
                    {authState?.UserPermission?.setting === 2 && (
                      <CancelIcon
                        className="cancel-icon cursor-pointer"
                        onClick={() => {
                          setAcceptedFiles();
                          setImagePreview('');
                          setEmptymedia(true);
                          formikRef.current.setFieldValue('filename', '');
                        }}
                      />
                    )}
                  </Box>
                ) : (
                  <Box
                    className={`upload-sec text-align setting-brand-logo-placeholder ${
                      authState?.UserPermission?.setting === 2
                        ? ' cursor-pointer'
                        : ''
                    }`}
                  >
                    {authState?.UserPermission?.setting === 2 ? (
                      <Box
                        {...getRootProps({ className: 'dropzone' })}
                        className="upload-area"
                      >
                        <CollectionsIcon />
                        <input {...getInputProps()} />
                        <Typography className="title-text upload-text">
                          <span className="blue-text">Browse</span> or Drop your
                          image here
                          <br />
                          Max {logoResolution}*{logoResolution}, 1MB
                        </Typography>
                      </Box>
                    ) : (
                      <Box className="upload-area">
                        <CollectionsIcon />
                        <Typography className="title-text upload-text">
                          <span className="blue-text">Browse</span> or Drop your
                          image here
                        </Typography>
                      </Box>
                    )}
                  </Box>
                )}
                {emptymedia && !acceptFiles && (
                  <Typography className="other-field-error-text">
                    This field is required
                  </Typography>
                )}
                <Box className="display-grid pt16">
                  <Box className="">
                    <CustomTextField
                      fullWidth
                      id="name"
                      name="name"
                      label="Name"
                      placeholder="Enter Name"
                      value={values?.name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.name && errors.name)}
                      helperText={touched.name && errors.name}
                      required
                      disabled={authState?.UserPermission?.setting !== 2}
                    />
                  </Box>
                  <Box className="">
                    <CustomTextField
                      fullWidth
                      id="email"
                      name="email"
                      label="Email address"
                      placeholder="Enter email address"
                      value={values?.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.email && errors.email)}
                      helperText={touched.email && errors.email}
                      required
                      disabled={authState?.UserPermission?.setting !== 2}
                    />
                  </Box>
                  <Box className="address-section">
                    <CustomTextField
                      fullWidth
                      id="address"
                      name="address"
                      label="Address"
                      placeholder="Enter Address"
                      value={values?.address}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.address && errors.address)}
                      helperText={touched.address && errors.address}
                      required
                      disabled={authState?.UserPermission?.setting !== 2}
                    />
                  </Box>
                </Box>
                <Box className="display-grid pt16">
                  <Box>
                    <CustomTextField
                      fullWidth
                      id="phoneno"
                      name="phoneno"
                      label="Phone number"
                      placeholder="Enter Phone number"
                      value={values?.phoneno}
                      onChange={(e) => {
                        if (
                          e.target.value === '' ||
                          e.target.value?.length < 12
                        ) {
                          handleChange(e);
                        }
                      }}
                      onBlur={handleBlur}
                      error={Boolean(touched.phoneno && errors.phoneno)}
                      helperText={touched.phoneno && errors.phoneno}
                      required
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9]/g, ''); // Remove non-numeric characters
                      }}
                      disabled={authState?.UserPermission?.setting !== 2}
                    />
                  </Box>
                  <Box>
                    <CustomSelect
                      id="currency"
                      name="currency"
                      label="Currency"
                      placeholder="Select Currency"
                      options={currenciesList}
                      value={
                        currenciesList?.find((opt) => {
                          return (
                            opt?.value?.currency === values?.currency?.currency
                          );
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('currency', e?.value);
                      }}
                      error={touched.currency && errors.currency}
                      helperText={touched?.currency && errors?.currency}
                      required
                      isClearable={false}
                    />
                  </Box>
                </Box>
                <Typography className="sub-header-text pt32">
                  Financial Settings
                </Typography>
                <Box className="months-section gap-sm pt16">
                  <Typography className="title-text">
                    Financial Year Starts From
                    <span className="other-field-label">
                      <span className="required">*</span>
                    </span>
                  </Typography>
                  <Box className="month-text-field">
                    <CustomSelect
                      fullWidth
                      id="months"
                      name="months"
                      label=""
                      placeholder="Month"
                      options={monthsOption}
                      value={
                        monthsOption?.find((opt) => {
                          return (
                            opt?.label?.toLowerCase() ===
                            values?.months?.toLowerCase()
                          );
                        }) || ''
                      }
                      onChange={(e) => {
                        setFieldValue('months', e?.label?.toLowerCase());
                      }}
                      error={Boolean(touched.months && errors.months)}
                      helperText={touched?.months && errors?.months}
                      isClearable={false}
                    />
                  </Box>
                </Box>
                <Box className="pt16">
                  {values?.months ? (
                    <Typography className="title-text">
                      Financial Year: {getFinancialYear(values?.months)}
                    </Typography>
                  ) : (
                    <></>
                  )}
                </Box>
                <Typography className="sub-header-text pt32">
                  Leave Type Settings
                </Typography>
                <Box className="display-grid pt16">
                  <Box className="">
                    <CustomTextField
                      fullWidth
                      id="base_leave"
                      name="base_leave"
                      label="Base Leave"
                      placeholder="Enter Base Leave"
                      value={values?.base_leave}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(touched.base_leave && errors.base_leave)}
                      helperText={touched.base_leave && errors.base_leave}
                      required
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                      }}
                    />
                  </Box>
                  <Box className="">
                    <CustomTextField
                      fullWidth
                      id="working_hours_per_day"
                      name="working_hours_per_day"
                      label="Working hours per Day"
                      placeholder="Enter Working hours per Day"
                      value={values?.working_hours_per_day}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        touched.working_hours_per_day &&
                          errors.working_hours_per_day
                      )}
                      helperText={
                        touched.working_hours_per_day &&
                        errors.working_hours_per_day
                      }
                      required
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                      }}
                    />
                  </Box>
                  <Box className="">
                    <CustomTextField
                      fullWidth
                      id="max_limit_per_week"
                      name="max_limit_per_week"
                      label=" Max Limit per Week"
                      placeholder="Enter Max Limit per Week"
                      value={values?.max_limit_per_week}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        touched.max_limit_per_week && errors.max_limit_per_week
                      )}
                      helperText={
                        touched.max_limit_per_week && errors.max_limit_per_week
                      }
                      required
                      onInput={(e) => {
                        e.target.value = e.target.value.replace(/[^0-9.]/g, '');
                      }}
                    />
                  </Box>
                </Box>
                <Box className="pt32">
                  <Typography className="sub-content-text">
                    Choose how leave is managed for your organization:
                  </Typography>
                  <Box className="pt8">
                    <Box>
                      <CustomRadio
                        name="LeaveType"
                        value="no"
                        checked={values?.LeaveType === 'day'}
                        onChange={() => setFieldValue('LeaveType', 'day')}
                        disableRipple
                        label={
                          <Typography className="sub-title-text">
                            Day - Based Leave Only
                          </Typography>
                        }
                      />
                    </Box>
                    <Box>
                      <CustomRadio
                        name="LeaveType"
                        value="yes"
                        checked={values?.LeaveType === 'hour'}
                        onChange={() => setFieldValue('LeaveType', 'hour')}
                        disableRipple
                        label={
                          <Typography className="sub-title-text">
                            Hour - Based Leave (Includes Both Hour & Day-Based
                            Leave)
                          </Typography>
                        }
                      />
                    </Box>
                  </Box>
                </Box>
                {values?.LeaveType !== generalSettings?.leave_period_type &&
                generalSettings?.leave_period_type ? (
                  <>
                    <Box className="ml24 pt16">
                      <Typography className="sub-content-text">
                        Please choose how you would like to handle any existing
                        future leave requests
                        <span className="other-field-label">
                          <span className="required">* </span>
                        </span>
                        :
                      </Typography>
                      <Box className="">
                        <Box>
                          <CustomRadio
                            className={
                              values?.manageleave === 'convert' &&
                              'convert-checkbox'
                            }
                            name="manageleave"
                            value="no"
                            checked={values?.manageleave === 'convert'}
                            onChange={() =>
                              setFieldValue('manageleave', 'convert')
                            }
                            disableRipple
                            label={
                              <Box className="d-flex align-center gap-5">
                                <Typography className="sub-title-text">
                                  Convert existing leaves to{' '}
                                  {values?.LeaveType === 'hour'
                                    ? 'hours'
                                    : 'days'}
                                </Typography>
                                <Tooltip
                                  arrow
                                  title={
                                    <Typography className="sub-title-text">
                                      Automatically convert all future leave
                                      requests from days into the appropriate
                                      number of hours based on your new leave
                                      settings.
                                    </Typography>
                                  }
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <InfoIcon
                                    className={`info-icon cursor-pointer ${
                                      values?.manageleave === 'convert' &&
                                      'convert-checkbox'
                                    }`}
                                  />
                                </Tooltip>
                              </Box>
                            }
                          />
                        </Box>
                        <Box>
                          <CustomRadio
                            className={
                              values?.manageleave === 'cancel' &&
                              'cancel-checkbox'
                            }
                            name="LeaveType"
                            value="yes"
                            checked={values?.manageleave === 'cancel'}
                            onChange={() =>
                              setFieldValue('manageleave', 'cancel')
                            }
                            disableRipple
                            label={
                              <Box className="d-flex align-center gap-5">
                                <Typography className="sub-title-text">
                                  Cancel all existing future leaves
                                </Typography>
                                <Tooltip
                                  arrow
                                  title={
                                    <Typography className="sub-title-text">
                                      Cancel all future leave requests.
                                      Employees will need to reapply for leave
                                      based on the new leave management rules.
                                    </Typography>
                                  }
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <InfoIcon
                                    className={`info-icon cursor-pointer ${
                                      values?.manageleave === 'cancel' &&
                                      'cancel-checkbox'
                                    }`}
                                  />
                                </Tooltip>
                              </Box>
                            }
                          />
                        </Box>
                      </Box>
                      {touched.manageleave && errors.manageleave && (
                        <Typography className="other-field-error-text">
                          {errors.manageleave}
                        </Typography>
                      )}
                    </Box>
                  </>
                ) : (
                  <></>
                )}
                <Box className="pt16">
                  <Typography className="sub-content-text" variant="h6">
                    Choose how leave deductions should be processed:
                  </Typography>
                  <Box className="">
                    <Box>
                      <CustomRadio
                        className=""
                        name="deduction"
                        value="manual"
                        checked={values?.deduction === 'manual'}
                        onChange={() => setFieldValue('deduction', 'manual')}
                        disableRipple
                        label={
                          <Box className="d-flex align-center gap-5">
                            <Typography
                              component="p"
                              className="sub-title-text"
                            >
                              Manual
                            </Typography>
                            <Tooltip
                              arrow
                              title={
                                <Typography className="sub-title-text">
                                  HR/Admin manually adjusts leave balance.
                                </Typography>
                              }
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <InfoIcon className="info-icon cursor-pointer" />
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Box>
                    <Box>
                      <CustomRadio
                        className=""
                        name="deduction"
                        value="auto"
                        checked={values?.deduction === 'auto'}
                        onChange={() => setFieldValue('deduction', 'auto')}
                        disableRipple
                        label={
                          <Box className="d-flex align-center gap-5">
                            <Typography className="sub-title-text">
                              Automatic
                            </Typography>
                            <Tooltip
                              arrow
                              title={
                                <Typography className="sub-title-text">
                                  System auto-calculates and deducts leave based
                                  on usage.
                                </Typography>
                              }
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <InfoIcon className="info-icon cursor-pointer" />
                            </Tooltip>
                          </Box>
                        }
                      />
                    </Box>
                  </Box>
                </Box>

                <Box className="form-actions-btn">
                  <CustomButton
                    type="submit"
                    variant="contained"
                    title={isLoader ? 'Saving...' : 'Save'}
                    disabled={
                      authState?.UserPermission?.setting !== 2 ||
                      !dirty ||
                      !isValid
                    }
                    //  disabled={!(dirty && isValid)}
                    // startIcon={<SaveIcon />}
                    onClick={() => {
                      setEmptymedia(true);
                    }}
                  />
                </Box>
              </Form>
            );
          }}
        </Formik>
      </Box>
    </>
  );
}
