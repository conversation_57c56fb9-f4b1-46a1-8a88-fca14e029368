import { useState, useEffect } from 'react';
import { getRecipeMeasuresList } from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';

export const useRecipeMeasures = () => {
  const [measuresData, setMeasuresData] = useState([
    { label: 'g', value: 'g', id: 1 },
  ]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchRecipeMeasures = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getRecipeMeasuresList();

      if (response?.measures && response.measures.length > 0) {
        // Transform API data to match expected format
        const transformedMeasures = response?.measures?.map((measure) => ({
          label: measure?.unit_title,
          value: measure?.unit_title,
          id: measure?.id,
        }));

        setMeasuresData(transformedMeasures);
        return transformedMeasures;
      } else {
        // If API returns empty data, add "g" as fallback
        const fallbackMeasure = [{ label: 'g', value: 'g', id: 1 }];
        setMeasuresData(fallbackMeasure);
        return fallbackMeasure;
      }
    } catch (error) {
      setError(error);
      setApiMessage('error', error?.response?.data?.message);

      // Add "g" as fallback on error
      const fallbackMeasure = [{ label: 'g', value: 'g', id: 1 }];
      setMeasuresData(fallbackMeasure);
      return fallbackMeasure;
    } finally {
      setLoading(false);
    }
  };

  // Utility function to find measure by value
  const findMeasureByValue = (value) => {
    if (!value || !measuresData) return null;
    return measuresData.find((measure) => measure?.value === value);
  };

  // Utility function to find measure by id
  const findMeasureById = (id) => {
    if (!id || !measuresData) return null;
    return measuresData.find((measure) => measure?.id === id);
  };

  // Utility function to get measure label by value
  const getMeasureLabel = (value) => {
    const measure = findMeasureByValue(value);
    return measure?.label || value || '';
  };

  // Utility function to get measure value by id
  const getMeasureValue = (id) => {
    const measure = findMeasureById(id);
    return measure?.value || '';
  };

  // Auto-fetch on component mount
  useEffect(() => {
    fetchRecipeMeasures();
  }, []);

  return {
    measuresData,
    loading,
    error,
    fetchRecipeMeasures,
    findMeasureByValue,
    findMeasureById,
    getMeasureLabel,
    getMeasureValue,
    // Alias for backward compatibility
    unitsOfMeasureOptions: measuresData,
  };
};
