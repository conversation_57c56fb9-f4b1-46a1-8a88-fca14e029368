'use client';

import React, { useState, useContext } from 'react';
import {
  Box,
  Tooltip,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Popover,
  Divider,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { useRouter, useSearchParams } from 'next/navigation';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import {
  generateYearsFromJoiningDate,
  setApiMessage,
  DateFormat,
} from '@/helper/common/commonFunctions';
// import SearchBar from '@/components/UI/SearchBar';
// import CustomSelect from '@/components/UI/selectbox';
// import CustomButton from '@/components/UI/button';
import { identifiers } from '@/helper/constants/identifier';
import AuthContext from '@/helper/authcontext';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import InfoIcon from '@mui/icons-material/Info';
import RightDrawer from '@/components/UI/RightDrawer';
import AssignEmployee from '@/components/Leave/AssignEmployee';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import ToggleOffIcon from '@mui/icons-material/ToggleOff';
import ToggleOnIcon from '@mui/icons-material/ToggleOn';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import AddIcon from '@mui/icons-material/Add';
import CheckIcon from '@mui/icons-material/Check';
import DialogBox from '@/components/UI/Modalbox';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import NoDataView from '@/components/UI/NoDataView';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import DeleteModal from '@/components/UI/DeleteModal';
import AddEditHoliday from './AddHoliday';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import UploadHoliday from './UploadHoliday';
import ContentLoader from '@/components/UI/ContentLoader';
import './holiday.scss';

export default function Holiday({
  getHolidayList,
  leaveFilterData,
  setLeaveFilterData,
  holidayAppliedFil,
  setHolidayAppliedFil,
  loader,
  setLeavePage,
  holidayList,
  setCreateModal,
  setEditType,
  leavePage,
}) {
  const { authState } = useContext(AuthContext);
  const router = useRouter();
  const searchParams = useSearchParams();
  const typeId = searchParams.get('type');
  const isEdit = searchParams.get('is_edit');
  const holidayType = searchParams.get('holidayType');
  const holidayid = searchParams.get('id');
  const [holidayExpand, setHolidayExpand] = useState();
  const [assignEmployee, setAssignEmployee] = useState();
  const [anchorEl2, setAnchorEl2] = useState(null);
  const [holiday, setHoliday] = useState(null);
  const [holidayInactives, setHolidayInactives] = useState(false);
  const [holidayDelete, setHolidayDelete] = useState(false);
  const [holidayTypeDelete, setHolidayTypeDelete] = useState(false);
  const [selectedHolidayId, setSelectedHolidayId] = useState(null);
  const queryParams = new URLSearchParams(searchParams);

  const open2 = Boolean(anchorEl2);

  const id2 = open2 ? 'simple-popper' : undefined;

  const handleClick2 = (event) => {
    setAnchorEl2(anchorEl2 ? null : event.currentTarget);
  };
  const handleClose2 = () => {
    setAnchorEl2(null);
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      setLeavePage(1);
      handleFilterData('apply');
    }
  };
  const handleDeleteClick = (id) => {
    setSelectedHolidayId(id);
    setHolidayDelete(true);
  };

  const handleDeleteClose = () => {
    setHolidayDelete(false);
    setSelectedHolidayId(null);
  };

  const handleConfirmDelete = async () => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_HOLIDAY_TYPE + `${selectedHolidayId}`
      );

      if (status === 200) {
        setApiMessage(data.status ? 'success' : 'error', data?.message);
        if (data.status) {
          getHolidayList(
            1,
            holidayAppliedFil?.searchValue,
            holidayAppliedFil,
            '',
            true
          );
        }
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }

    handleDeleteClose();
  };

  const handleClose = () => {
    setHolidayInactives(false);
  };
  // handle filter
  const handleFilterData = (type) => {
    if (type === 'apply') {
      getHolidayList(
        1,
        leaveFilterData?.searchValue,
        leaveFilterData,
        '',
        true
      );
      setHolidayAppliedFil(leaveFilterData);
    } else {
      const clearFilter = {
        searchValue: '',
        year: '',
        status: '',
      };
      setLeaveFilterData(clearFilter);
      setHolidayAppliedFil(clearFilter);
      getHolidayList(1, '', clearFilter, '', true);
    }
  };

  // handle delete holiday policy
  const handleDeleteHoliday = async () => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_HOLIDAY_POLICY + `${selectedHolidayId}`
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          getHolidayList(
            1,
            holidayAppliedFil?.searchValue,
            holidayAppliedFil,
            '',
            true
          );
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
    setHolidayTypeDelete(false);
    setSelectedHolidayId(null);
  };

  // handle active/inactive holiday
  const handleInactiveHoliday = async () => {
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.INACTIVE_ACTIVE_HOLIDAY,
        {
          holiday_type_id: holidayInactives?.id,
          holidayTypeStatus:
            holidayInactives?.holiday_type_status === 'active'
              ? 'inactive'
              : 'active',
        }
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          setHolidayInactives(false);
          getHolidayList(
            1,
            holidayAppliedFil?.searchValue,
            holidayAppliedFil,
            '',
            true
          );
        } else {
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  const columns = [
    {
      field: 'id',
      headerName: 'ID',
      width: 80,
      minWidth: 80,
      flex: 0,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">{params?.row?.id}</Typography>
          </Box>
        );
      },
    },
    {
      field: 'user_full_name',
      headerName: 'Holiday Name',
      width: 180,
      minWidth: 180,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap Leave-name">
            <Typography className="title-text text-ellipsis">
              {params?.row?.holiday_policy_name}
            </Typography>
            {params?.row?.holiday_policy_description && (
              <Tooltip
                title={
                  <Typography>
                    {params?.row?.holiday_policy_description}
                  </Typography>
                }
                placement="right"
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <InfoIcon className="info-icon cursor-pointer" />
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: 'holiday_policy_start_date',
      headerName: 'Date',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'start',
      align: 'start',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-start h100 gap-sm flex-wrap">
            <Typography className="title-text">
              {DateFormat(params?.value, 'dates') ===
              DateFormat(params?.row?.holiday_policy_end_date, 'dates')
                ? DateFormat(params?.row?.holiday_policy_end_date, 'dates')
                : `${DateFormat(params?.value, 'dates')} - ${DateFormat(params?.row?.holiday_policy_end_date, 'dates')}`}
            </Typography>{' '}
          </Box>
        );
      },
    },
    {
      field: 'holiday_policy_user_count',
      headerName: 'Assign Employees',
      width: 150,
      minWidth: 150,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center h100 text-capital">
            <Typography
              className="title-text cursor-pointer"
              onClick={() => {
                setAssignEmployee(params?.row);
              }}
            >
              {(params?.value ? params?.value : 0) + ' Employees'}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 100,
      minWidth: 100,
      flex: 1,
      sortable: false,
      headerAlign: 'center',
      align: 'center',
      renderCell: (params) => {
        return (
          <Box className="d-flex align-center justify-center actions p0 h100">
            <Tooltip
              title={<Typography>Assign Employees</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <div className="action-icon assign-icons d-flex">
                <PersonAddIcon
                  className="cursor-pointer"
                  onClick={() => {
                    setAssignEmployee(params?.row);
                  }}
                />
              </div>
            </Tooltip>
            <Tooltip
              title={<Typography>Edit</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box>
                <EditIcon
                  onClick={() => {
                    // router.push(
                    //   `/org/setup?is_setup=4&type=${params?.row?.id}&holidayType=${params?.row?.holiday_type_id}&is_edit=false`
                    // );
                    queryParams.set('type', params?.row?.id);
                    queryParams.set(
                      'holidayType',
                      params?.row?.holiday_type_id
                    );
                    queryParams.set('is_edit', 'false');
                    router.push(`?${queryParams.toString()}`);
                  }}
                />
              </Box>
            </Tooltip>

            <Tooltip
              title={<Typography>Delete</Typography>}
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Box>
                <DeleteIcon
                  onClick={() => {
                    setHolidayTypeDelete(true);
                    setSelectedHolidayId(params?.row?.id);
                  }}
                />
              </Box>
            </Tooltip>
          </Box>
        );
      },
    },
  ];

  const AssignEmployees = async (user_ids, assignId) => {
    const requestData = {
      user_ids: user_ids,
      policy_ids: assignId,
      policy_type: 'holiday',
    };
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.ASSIGN_EMPLOYEE,
        requestData
      );
      if (status === 200) {
        if (data.status) {
          setApiMessage('success', data?.message);
          setAssignEmployee(false);
          getHolidayList(
            1,
            holidayAppliedFil?.searchValue,
            holidayAppliedFil,
            '',
            true
          );
        } else {
          setApiMessage('error', data?.message);
          getHolidayList(
            1,
            holidayAppliedFil?.searchValue,
            holidayAppliedFil,
            '',
            true
          );
        }
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  return (
    <>
      {holidayid ? (
        <>
          <Box className="d-flex align-center pb8">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                router.back();
              }}
            />
            <Typography className="body-text fw600 pr8">
              Import Holiday
            </Typography>
          </Box>
          <Divider className="" />
          <UploadHoliday holidayid={holidayid} />
        </>
      ) : isEdit === 'true' || isEdit === 'false' ? (
        <>
          <Box className="d-flex align-center pb8">
            <ArrowBackIosIcon
              className="cursor-pointer"
              onClick={() => {
                router.back();
              }}
            />
            <Typography className="body-text fw600 pr8">
              {isEdit === 'true' ? 'Add Holiday' : 'Edit Holiday'}
            </Typography>
          </Box>
          <Divider className="mb8" />
          <AddEditHoliday
            isEdit={isEdit}
            typeId={typeId}
            holidayType={holidayType}
            getHolidayList={() =>
              getHolidayList(
                leavePage,
                holidayAppliedFil?.searchValue,
                holidayAppliedFil,
                '',
                true
              )
            }
          />
        </>
      ) : (
        <Box className="leave-holiday-wrap">
          <Box className="search-section-wrap">
            <Box className="search-section-fields">
              <CustomSearch
                setSearchValue={(e) => {
                  setLeaveFilterData({
                    ...leaveFilterData,
                    searchValue: e,
                  });
                }}
                searchValue={leaveFilterData?.searchValue}
                onKeyPress={handleKeyPress}
              />
            </Box>
            <Box className="search-section-fields">
              <CustomSelect
                fullWidth
                id="select_year"
                name="select_year"
                label=""
                placeholder="Select Year"
                options={generateYearsFromJoiningDate(
                  authState?.user_joining_date
                    ? authState?.user_joining_date
                    : authState?.createdAt
                      ? authState?.createdAt
                      : ''
                )}
                value={
                  generateYearsFromJoiningDate(
                    authState?.user_joining_date
                      ? authState?.user_joining_date
                      : authState?.createdAt
                        ? authState?.createdAt
                        : ''
                  )?.find((opt) => {
                    return opt?.value === leaveFilterData?.year;
                  }) || ''
                }
                onChange={(e) => {
                  setLeaveFilterData({
                    ...leaveFilterData,
                    year: e?.value,
                  });
                }}
                isClearable={false}
              />
            </Box>
            <Box className="search-section-fields">
              <CustomSelect
                fullWidth
                id="holiday_status"
                name="holiday_status"
                label=""
                placeholder="Holiday Status"
                options={identifiers?.HOLIDAY_STATUS}
                value={
                  identifiers?.HOLIDAY_STATUS?.find((opt) => {
                    return opt?.value === leaveFilterData?.status;
                  }) || ''
                }
                onChange={(e) => {
                  setLeaveFilterData({
                    ...leaveFilterData,
                    status: e?.value,
                  });
                }}
                isClearable={false}
              />
            </Box>
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <CheckIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setLeavePage(1);
                  handleFilterData('apply');
                }}
              />
            </Box>
            <Box>
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Clear Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <ClearOutlinedIcon />
                  </Tooltip>
                }
                onClick={() => {
                  handleFilterData('cancel');
                }}
              />
            </Box>
            <Box>
              <CustomButton
                title="Create Holiday"
                startIcon={<AddIcon />}
                onClick={() => {
                  setCreateModal(true);
                }}
              />
            </Box>
          </Box>
          <Box className="table-container table-border-wrap holiday-table">
            {loader ? (
              <ContentLoader />
            ) : (
              <>
                {holidayList && holidayList?.length === 0 ? (
                  <Box className="no-data d-flex align-center justify-center">
                    <NoDataView
                      title="No Holiday Found"
                      description="There is no holiday available at the moment."
                    />
                  </Box>
                ) : (
                  holidayList &&
                  holidayList?.length > 0 &&
                  holidayList?.map((holiday, i) => {
                    return (
                      <Accordion
                        key={i}
                        elevation={0}
                        className="holiday-accordion cursor-default"
                        expanded={holidayExpand === holiday?.id}
                      >
                        <AccordionSummary
                          expandIcon={
                            <KeyboardArrowDownIcon
                              className="cursor-pointer exp-icon"
                              onClick={() => {
                                setHolidayExpand(
                                  holidayExpand === holiday?.id
                                    ? null
                                    : holiday?.id
                                );
                              }}
                            />
                          }
                          className="accordion-summary cursor-default"
                        >
                          <Box className="d-flex align-center justify-space-between  w100">
                            <Typography
                              className="body-text fw600 w100 cursor-pointer holiday-name"
                              onClick={() => {
                                setHolidayExpand(
                                  holidayExpand === holiday?.id
                                    ? null
                                    : holiday?.id
                                );
                              }}
                            >
                              {holiday?.holiday_type_name}
                              {holiday?.has_holiday_type_default && (
                                <Typography
                                  component="span"
                                  className="sub-title-text default-text ml16"
                                >
                                  Default
                                </Typography>
                              )}
                            </Typography>
                            <Box className="d-flex align-center justify-center actions holiday-actions p0 h100 ">
                              <Tooltip
                                title={<Typography>Edit</Typography>}
                                arrow
                                classes={{
                                  tooltip: 'info-tooltip-container',
                                }}
                              >
                                <Box>
                                  <EditIcon
                                    onClick={() => {
                                      setCreateModal(true);
                                      setEditType(holiday);
                                    }}
                                  />
                                </Box>
                              </Tooltip>
                              {holiday?.holiday_type_status === 'active' ? (
                                <Tooltip
                                  title={<Typography>Active</Typography>}
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <ToggleOnIcon
                                    className="toggle-icon active ml0"
                                    onClick={() => {
                                      setHolidayInactives(holiday);
                                    }}
                                  />
                                </Tooltip>
                              ) : (
                                <Tooltip
                                  title={<Typography>In-Active</Typography>}
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <ToggleOffIcon
                                    className="toggle-icon inactive ml0"
                                    onClick={() => {
                                      setHolidayInactives(holiday);
                                    }}
                                  />
                                </Tooltip>
                              )}
                              <Tooltip
                                title={<Typography>Delete</Typography>}
                                arrow
                                classes={{
                                  tooltip: 'info-tooltip-container',
                                }}
                              >
                                <Box>
                                  <DeleteIcon
                                    onClick={() =>
                                      handleDeleteClick(holiday?.id)
                                    }
                                  />
                                </Box>
                              </Tooltip>
                            </Box>
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails className="">
                          {holiday?.holiday_type &&
                          holiday?.holiday_type?.length > 0 ? (
                            <>
                              <DataGrid
                                rows={holiday?.holiday_type}
                                columns={columns}
                                pageSize={''}
                                checkboxSelection={false}
                                disableSelectionOnClick
                              />
                              <Box className="pt8">
                                <Typography
                                  className="title-text fw600 primary-link-text"
                                  onClick={(e) => {
                                    setHoliday(holiday?.id);
                                    handleClick2(e);
                                  }}
                                >
                                  + Add Holiday
                                </Typography>
                              </Box>
                            </>
                          ) : (
                            <Box className="">
                              <Typography
                                className="title-text fw600 primary-link-text"
                                onClick={(e) => {
                                  setHoliday(holiday?.id);
                                  handleClick2(e);
                                }}
                              >
                                + Add Holiday
                              </Typography>
                            </Box>
                          )}
                        </AccordionDetails>
                      </Accordion>
                    );
                  })
                )}
              </>
            )}
            <RightDrawer
              anchor={'right'}
              open={assignEmployee}
              onClose={() => {
                setAssignEmployee(false);
              }}
              title="Assign Employee"
              subTitle="Holidays Management"
              className="assign-employee-drawer leave-assign-drawer"
              content={
                <>
                  <AssignEmployee
                    setClose={setAssignEmployee}
                    AssignEmployees={AssignEmployees}
                    assignId={[assignEmployee?.id]}
                    SelectedID={assignEmployee?.user_policy_ids}
                  />
                </>
              }
            />
          </Box>
          <Popover
            className="add-holiday-popover"
            id={id2}
            open={open2}
            anchorEl={anchorEl2}
            onClose={handleClose2}
            anchorOrigin={{
              vertical: 'bottom',
              horizontal: 'left',
            }}
          >
            <Box className="export-option">
              <Box
                className="pb8 cursor-pointer d-flex holiday-option align-start "
                onClick={() => {
                  // router.push(
                  //   `/org/setup?is_setup=4&type=${holiday}&is_edit=true`
                  // );
                  queryParams.set('type', holiday);
                  queryParams.set('is_edit', 'true');
                  router.push(`?${queryParams.toString()}`);
                  handleClose2(null);
                }}
                // onClick={() => setAddHoliday(true)}
              >
                <AddCircleIcon />
                <Box>
                  <Typography className="title-text fw600">
                    Add Holidays
                  </Typography>
                  <Typography className="content-text fw500 color-gray">
                    Add individual Holiday for each Holiday list.
                  </Typography>
                </Box>
              </Box>
              <Box
                className="pb8 cursor-pointer d-flex holiday-option align-start "
                onClick={() => {
                  // router.push(`/org/setup?is_setup=4&id=${holiday}`);
                  queryParams.set('id', holiday);
                  router.push(`?${queryParams.toString()}`);
                  handleClose2(null);
                }}
              >
                <SaveAltIcon />
                <Box>
                  <Typography className="title-text fw600">
                    Import Holidays
                  </Typography>
                  <Typography className="content-text fw500 color-gray">
                    Add multiple holiday in a single list at same time.
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Popover>
          <DialogBox
            open={holidayInactives}
            handleClose={() => {
              handleClose();
            }}
            className="confirmation-modal"
            dividerClass="confirmation-modal-divider"
            title="Confirmation"
            content={
              <>
                <ConfirmationModal
                  handleCancel={handleClose}
                  handleConfirm={handleInactiveHoliday}
                  text={`Are you sure? You want to ${holidayInactives?.holiday_type_status === 'active' ? 'In-Active' : 'Active'} Holiday List?`}
                />
              </>
            }
          />
          <DialogBox
            open={holidayDelete}
            handleClose={handleDeleteClose}
            className="delete-modal"
            dividerClass="delete-modal-divider"
            title="Confirmation"
            content={
              <DeleteModal
                handleCancel={handleDeleteClose}
                handleConfirm={handleConfirmDelete}
                text="Are you sure you want to delete this holiday type?"
              />
            }
          />
          <DialogBox
            open={holidayTypeDelete}
            handleClose={() => setHolidayTypeDelete(false)}
            className="delete-modal"
            dividerClass="delete-modal-divider"
            title="Confirmation"
            content={
              <DeleteModal
                handleCancel={() => setHolidayTypeDelete(false)}
                handleConfirm={handleDeleteHoliday}
                text="Are you sure you want to delete this holiday?"
              />
            }
          />
        </Box>
      )}
    </>
  );
}
