@import '@/styles/variable.scss';


.ticket-wrap {
    width: 100%;
    max-width: 300px;
    box-shadow: 0px 0px 2px $color-Dark-50;
    padding: 12px 15px;
    border-radius: 8px;
    position: relative;

    .id-name-wrap {
        gap: 20px;
        cursor: pointer;
        padding: 10px 0px;

        .id-wrap {
            font-size: 15px;
            color: $color-Dark-50;
            border-bottom: 1px dashed $color-Dark-20;
            line-height: 18px;
            font-family: "Poppins", sans-serif !important;

            &:hover {
                border-bottom: 1px dashed black;
                color: black;
            }
        }
    }

    .heading-text-wrap {
        font-family: "Poppins", sans-serif !important;
        font-size: 15px;
        font-weight: 500;
    }

    .name-wrap {
        font-family: "Poppins", sans-serif !important;
        color: $color-Dark-50;
        max-width: 155px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &:hover {
            color: $color-blue;
        }
    }

    .time-wrap {
        color: $color-Dark-50;

        .timer-icon {
            height: 15px;
            width: 15px;
            fill: $color-primary;
        }

        .time-text {
            font-family: "Poppins", sans-serif !important;
        }
    }

    .profile-wrap {
        position: absolute;
        top: 12px;
        right: 15px;
        line-height: 0px;

        .profile-image {
            height: 35px;
            width: 35px;
            border-radius: 50px;
        }
    }

    .ticket-status-select {
        padding-top: 10px;

        .MuiFormControl-root {
            width: auto;

            .MuiSvgIcon-root {
                height: 20px;
                width: 20px;
                margin-top: 3px;
            }
        }

        .MuiInputBase-root {
            font-family: "Poppins", sans-serif !important;
        }

        .MuiSelect-select {
            padding: 1px 0px 0px 10px;
            font-size: 14px !important;
        }

        fieldset {
            height: 25px !important;
            border-radius: 5px;
        }
    }

}