.change-request-history-details {
  .old-new-info-box {
    gap: var(--spacing-xl);
    .old-info-box {
      width: 50%;
    }
    .new-info-box {
      width: 50%;
    }
    @media (max-width: 767px) {
      flex-direction: column;
      gap: var(--spacing-none);
      .cr-history-divider {
        display: none;
      }
    }
  }
  .file-grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    column-gap: var(--spacing-xl);
    row-gap: var(--spacing-xs);
    .file-grid-item {
      border: var(--normal-sec-border);
      border-radius: var(--border-radius-sm);
      padding: var(--spacing-sm);
    }
    @media (max-width: 991px) {
      grid-template-columns: repeat(2, 1fr);
    }
    @media (max-width: 575px) {
      grid-template-columns: repeat(1, 1fr);
    }
  }
  .res-remark-icon {
    font-size: var(--font-size-md);
    vertical-align: middle;
    margin-right: var(--spacing-sm);
  }
}
