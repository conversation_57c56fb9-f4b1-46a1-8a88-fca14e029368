import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import {
  getRecipeCategoryList,
  getDietaryList,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import Image from '@/components/UI/AppImage/AppImage';
import './BasicInfoSection.scss';

const BasicInfoSection = ({ formData, dispatch, validationErrors }) => {
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showDietaryDropdown, setShowDietaryDropdown] = useState(false);

  // Dynamic state for API-driven options
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [dietaryOptions, setDietaryOptions] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);

  // API function to get recipe categories
  const fetchCategoryOptions = async () => {
    try {
      const response = await getRecipeCategoryList(
        '',
        '',
        { status: 'active' },
        '',
        ''
      );

      // Transform categories to match expected format
      const transformedCategories =
        response?.categories?.map((category) => ({
          id: category?.id,
          label: category?.category_name,
          icon: category?.iconItem?.iconName || 'ChefHat', // Default icon if none provided
          icon_url: category?.iconItem?.iconUrl,
        })) || [];

      setCategoryOptions(transformedCategories);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setCategoryOptions([]);
    }
  };

  // API function to get dietary options
  const fetchDietaryOptions = async () => {
    try {
      const response = await getDietaryList(
        '',
        '',
        { status: 'active' },
        '',
        ''
      );

      // Transform dietary options to match expected format
      const transformedDietary =
        response?.dietary?.map((item) => ({
          id: item?.id,
          label: item?.attribute_title,
          icon: item?.iconItem?.iconName || 'Leaf', // Default icon if none provided
          icon_url: item?.iconItem?.iconUrl,
        })) || [];

      setDietaryOptions(transformedDietary);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setDietaryOptions([]);
    }
  };

  // Fetch options on component mount
  useEffect(() => {
    const fetchOptions = async () => {
      setOptionsLoading(true);
      await Promise.all([fetchCategoryOptions(), fetchDietaryOptions()]);
      setOptionsLoading(false);
    };

    fetchOptions();
  }, []);

  const handleInputChange = (field, value) => {
    dispatch({
      type: 'UPDATE_BASIC_INFO',
      payload: { [field]: value },
    });
  };

  const handleCategoryToggle = (categoryId) => {
    const currentCategories = formData.basicInfo?.categories || [];
    const updatedCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter((id) => id !== categoryId)
      : [...currentCategories, categoryId];

    handleInputChange('categories', updatedCategories);
  };

  const handleDietaryToggle = (dietaryId) => {
    const currentDietary = formData.basicInfo?.dietaries || [];
    const updatedDietary = currentDietary.includes(dietaryId)
      ? currentDietary.filter((id) => id !== dietaryId)
      : [...currentDietary, dietaryId];

    handleInputChange('dietaries', updatedDietary);
  };

  // const handleVisibilityToggle = (visibilityName) => {
  //   const currentCategories = formData.basicInfo?.visibility || [];
  //   const updatedCategories = currentCategories.includes(visibilityName)
  //     ? currentCategories.filter((id) => id !== visibilityName)
  //     : [...currentCategories, visibilityName];

  //   handleInputChange('visibility', updatedCategories);
  // };

  const handleVisibilityToggle = (visibilityName) => {
    const currentCategories = formData.basicInfo?.visibility || [];

    const isSelected = currentCategories.includes(visibilityName);
    const updatedCategories = isSelected
      ? currentCategories.filter((item) => item !== visibilityName)
      : [...currentCategories, visibilityName];

    // Prevent visibility array from becoming empty
    if (updatedCategories.length === 0) return;

    handleInputChange('visibility', updatedCategories);
  };

  const basicInfo = formData.basicInfo || {};

  return (
    <div className="basic-info-section">
      {/* Recipe Names */}
      <div className="basic-info-section__names-section">
        <div className="">
          <CustomTextField
            label="Recipe Name"
            required
            value={basicInfo.recipeName || ''}
            onChange={(e) => handleInputChange('recipeName', e.target.value)}
            placeholder="Enter the internal recipe name"
            error={!!validationErrors.recipeName}
            helperText={validationErrors.recipeName}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Public Display Name"
            value={basicInfo.publicDisplayName || ''}
            onChange={(e) =>
              handleInputChange('publicDisplayName', e.target.value)
            }
            error={!!validationErrors.publicDisplayName}
            placeholder="Name shown to customers (optional)"
            helperText="Leave empty to use the recipe name"
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Description"
            value={basicInfo.recipeDescription || ''}
            onChange={(e) =>
              handleInputChange('recipeDescription', e.target.value)
            }
            error={!!validationErrors.recipeDescription}
            placeholder="Enter a description of the recipe"
            fullWidth
            multiline
            rows={3}
          />
        </div>
      </div>

      {/* Categories */}
      <div className="basic-info-section__dropdown-section">
        <p className="other-field-label">Categories</p>
        <div>
          <button
            onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
            className={`basic-info-section__dropdown-button ${basicInfo.categories?.length > 0 ? 'basic-info-section__dropdown-button__selected' : ''}`}
            disabled={optionsLoading}
          >
            <span>
              {optionsLoading
                ? 'Loading categories...'
                : basicInfo.categories?.length > 0
                  ? `${basicInfo.categories.length} categories selected`
                  : 'Select categories'}
            </span>
            <Icon
              name={showCategoryDropdown ? 'ChevronUp' : 'ChevronDown'}
              size={16}
              color="currentColor"
            />
          </button>

          {showCategoryDropdown && (
            <div className="basic-info-section__dropdown-menu">
              {categoryOptions?.map((category) => (
                <button
                  key={category?.id}
                  onClick={() => handleCategoryToggle(category?.id)}
                  className={`basic-info-section__dropdown-item ${
                    basicInfo.categories?.includes(category?.id)
                      ? 'basic-info-section__dropdown-item--selected'
                      : ''
                  }`}
                >
                  {/* <Icon name={category?.icon} size={16} color="currentColor" /> */}

                  {category?.icon_url && (
                    <Image
                      src={category?.icon_url}
                      alt={category?.category_name}
                      className="basic-info-section__dropdown-item-icon"
                    />
                  )}

                  <span className="basic-info-section__dropdown-item-text">
                    {category?.label}
                  </span>
                  {basicInfo.categories?.includes(category?.id) && (
                    <Icon
                      name="Check"
                      size={16}
                      color="currentColor"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Selected Categories Display */}
        {basicInfo.categories?.length > 0 && (
          <div className="basic-info-section__selected-items">
            {basicInfo.categories.map((categoryId) => {
              const category = categoryOptions?.find(
                (c) => c?.id === categoryId
              );
              return (
                <span
                  key={categoryId}
                  className="basic-info-section__selected-tag basic-info-section__selected-tag--category"
                >
                  {/* <Icon name={category?.icon} size={12} color="currentColor" /> */}
                  {category?.icon_url && (
                    <Image
                      src={category?.icon_url}
                      alt={category?.category_name}
                      className="basic-info-section__selected-tag-icon"
                    />
                  )}
                  <span>{category?.label}</span>
                  <button
                    onClick={() => handleCategoryToggle(categoryId)}
                    className="basic-info-section__tag-remove"
                  >
                    <Icon name="X" size={12} color="currentColor" />
                  </button>
                </span>
              );
            })}
          </div>
        )}
      </div>

      {/* Dietary Options */}
      <div className="basic-info-section__dropdown-section">
        <p className="other-field-label">Dietary Suitability</p>
        <div>
          <button
            onClick={() => setShowDietaryDropdown(!showDietaryDropdown)}
            className={`basic-info-section__dropdown-button ${basicInfo.dietaries?.length > 0 ? 'basic-info-section__dropdown-button__selected' : ''}`}
            disabled={optionsLoading}
          >
            <span>
              {optionsLoading
                ? 'Loading dietary options...'
                : basicInfo.dietaries?.length > 0
                  ? `${basicInfo.dietaries.length} dietary options selected`
                  : 'Select dietary options'}
            </span>
            <Icon
              name={showDietaryDropdown ? 'ChevronUp' : 'ChevronDown'}
              size={16}
              color="currentColor"
            />
          </button>

          {showDietaryDropdown && (
            <div className="basic-info-section__dropdown-menu">
              {dietaryOptions?.map((option) => (
                <button
                  key={option?.id}
                  onClick={() => handleDietaryToggle(option?.id)}
                  className={`basic-info-section__dropdown-item ${
                    basicInfo.dietaries?.includes(option?.id)
                      ? 'basic-info-section__dropdown-item--dietary-selected'
                      : ''
                  }`}
                >
                  {option?.icon_url && (
                    <Image
                      src={option?.icon_url}
                      alt={option?.category_name}
                      className="basic-info-section__dropdown-item-icon"
                    />
                  )}

                  {/* <Icon name={option.icon} size={16} color="currentColor" /> */}
                  <span className="basic-info-section__dropdown-item-text">
                    {option?.label}
                  </span>
                  {basicInfo?.dietaries?.includes(option?.id) && (
                    <Icon
                      name="Check"
                      size={16}
                      color="currentColor"
                      style={{ marginLeft: 'auto' }}
                    />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Selected Dietary Options Display */}
        {basicInfo.dietaries?.length > 0 && (
          <div className="basic-info-section__selected-items">
            {basicInfo.dietaries?.map((optionId) => {
              const option = dietaryOptions.find((o) => o.id === optionId);
              return (
                <span
                  key={optionId}
                  className="basic-info-section__selected-tag basic-info-section__selected-tag--dietary"
                >
                  {option?.icon_url && (
                    <Image
                      src={option?.icon_url}
                      alt={option?.category_name}
                      className="basic-info-section__selected-tag-icon"
                    />
                  )}
                  {/* <Icon name={option?.icon} size={12} color="currentColor" /> */}
                  <span>{option?.label}</span>
                  <button
                    onClick={() => handleDietaryToggle(optionId)}
                    className="basic-info-section__tag-remove"
                  >
                    <Icon name="X" size={12} color="currentColor" />
                  </button>
                </span>
              );
            })}
          </div>
        )}
      </div>

      {/* Timing */}
      <div className="basic-info-section__timing-grid">
        <div className="">
          <CustomTextField
            label="Prep Time (minutes)"
            type="number"
            value={basicInfo.prepTime || ''}
            onChange={(e) =>
              handleInputChange('prepTime', parseInt(e.target.value) || 0)
            }
            placeholder="0"
            inputProps={{ min: 0 }}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Cook Time (minutes)"
            type="number"
            value={basicInfo.cookTime || ''}
            onChange={(e) =>
              handleInputChange('cookTime', parseInt(e.target.value) || 0)
            }
            placeholder="0"
            inputProps={{ min: 0 }}
            fullWidth
          />
        </div>

        <div className="">
          <CustomTextField
            label="Total Time"
            value={
              (basicInfo.prepTime || 0) +
                (basicInfo.cookTime || 0) +
                ' minutes' || ''
            }
            onChange={(e) =>
              handleInputChange('cookTime', parseInt(e.target.value) || 0)
            }
            placeholder="0 minutes"
            fullWidth
            disabled
          />
        </div>
      </div>

      {/* Visibility */}
      <div>
        <p className="other-field-label">Recipe Visibility</p>
        <div className="basic-info-section__visibility-options mt8">
          <button
            onClick={() => handleVisibilityToggle('private')}
            className={`basic-info-section__visibility-button ${
              basicInfo.visibility?.includes('private')
                ? 'basic-info-section__visibility-button--active'
                : 'basic-info-section__visibility-button--inactive'
            }`}
          >
            <Icon name="Lock" size={16} color="currentColor" />
            <span className="basic-info-section__visibility-text">Private</span>
          </button>
          <button
            onClick={() => handleVisibilityToggle('public')}
            className={`basic-info-section__visibility-button ${
              basicInfo.visibility?.includes('public')
                ? 'basic-info-section__visibility-button--active'
                : 'basic-info-section__visibility-button--inactive'
            }`}
          >
            <Icon name="Globe" size={16} color="currentColor" />
            <span className="basic-info-section__visibility-text">Public</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoSection;
