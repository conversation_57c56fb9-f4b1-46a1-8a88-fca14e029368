'use client';
import React, { useState, useMemo } from 'react';
import { Text } from 'intergalactic/typography';
import { Box } from 'intergalactic/flex-box';
import Select from 'intergalactic/select';
import { FilterTrigger } from 'intergalactic/base-trigger';
import Ellipsis from 'intergalactic/ellipsis';
// import ExpandMoreOutlinedIcon from '@mui/icons-material/ExpandMoreOutlined';
// import ClearIcon from '@mui/icons-material/Clear';
import { Tooltip, Typography } from '@mui/material';
import { DropdownCloseIcon, DropdownIcon } from '@/helper/common/images';
import './filter.scss';

export default function MultipleFilter({
  selected,
  setSelected,
  List,
  placeholder,
  disabled = false,
}) {
  const [visible, setVisible] = useState(false);
  const [filter] = useState('');
  const [loading] = useState(false);

  // Create branch data once instead of inside useMemo for performance
  const branchData = List?.map((feature) => ({
    label: feature?.label,
    value: feature?.value,
  }));

  // Filtered options memoized to avoid unnecessary recalculations
  const filteredOptions = useMemo(() => {
    if (filter) {
      return branchData.filter((branch) =>
        branch.label.toLowerCase().includes(filter.toLowerCase())
      );
    }
    return branchData;
  }, [filter, branchData]);

  // Handle visibility and loading
  const handleChangeVisible = (visible) => {
    setVisible(visible);
    // if (visible) {
    //   setLoading(true);
    //   setTimeout(() => setLoading(false), 1000);
    // }
  };

  // Select/Deselect individual branch
  const handleSelectBranch = (branchId) => {
    setSelected((prev) =>
      prev.includes(branchId)
        ? prev.filter((id) => id !== branchId)
        : [...prev, branchId]
    );
  };

  // Select/Deselect all branches
  const handleSelectAll = () => {
    setSelected(filteredOptions?.map((branch) => branch?.value));
  };

  const handleDeselectAll = () => {
    setSelected([]);
  };

  const isAllSelected = selected?.length === filteredOptions?.length;

  const handleClearSelection = () => {
    setSelected([]);
  };
  const truncateText = (text, maxLength) =>
    text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  return (
    <>
      <Select
        placeholder={placeholder}
        multiselect
        value={selected}
        onVisibleChange={disabled ? '' : handleChangeVisible}
        visible={visible}
        onChange={setSelected}
        disabled={disabled}
      >
        <Select.Trigger
          className="intergalactic-multiselect"
          tag={FilterTrigger}
        >
          {selected?.length === 1 ? (
            <Box className="d-flex justify-space-between gap-sm">
              <Box className="d-flex align-center gap-5">
                <span className="branches-wrap">{placeholder + ' :'}</span>
                <Tooltip
                  arrow
                  title={
                    <Typography>
                      {branchData.find(
                        (branch) => branch?.value === selected[0]
                      )?.label || ''}
                    </Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container ',
                  }}
                >
                  <span className="label-wrap">
                    {truncateText(
                      branchData.find((branch) => branch?.value === selected[0])
                        ?.label || '',
                      14
                    )}
                  </span>
                </Tooltip>
              </Box>
              <Box className="d-flex align-center gap-5">
                {!disabled && (
                  <Box className="d-flex">
                    <DropdownCloseIcon
                      // fontSize="14px"
                      onClick={handleClearSelection}
                      className="clear-icon-wrap"
                    />
                  </Box>
                )}
                <Box className="d-flex">
                  <DropdownIcon className="down-arrow-wrap" />
                </Box>
              </Box>
            </Box>
          ) : selected?.length > 1 ? (
            <Box className="multiple-branch-wrap">
              <span className="branches-wrap">{placeholder}</span>
              <span className="selected-count">{selected?.length}</span>{' '}
              <span className="selected-wrap">selected</span>
              {!disabled && (
                <Box className="d-flex">
                  <DropdownCloseIcon
                    // fontSize="14px"
                    onClick={handleClearSelection}
                    className="clear-icon-wrap"
                  />
                </Box>
              )}
              <Box className="d-flex">
                <DropdownIcon className="down-arrow-wrap" />
              </Box>
            </Box>
          ) : (
            `Select ${placeholder}`
          )}
        </Select.Trigger>
        <Select.Popper
          className="select-input-wrap"
          aria-label="Options with search"
        >
          {loading && (
            <Text tag="div" m="10px 8px" size={200} use="secondary">
              Loading...
            </Text>
          )}
          {!loading && (
            <>
              <Box>
                <Typography
                  className="select-all-wrap"
                  onClick={
                    !disabled
                      ? isAllSelected
                        ? handleDeselectAll
                        : handleSelectAll
                      : undefined
                  }
                >
                  {isAllSelected ? 'Deselect All' : 'Select All'}
                </Typography>
              </Box>
              <Select.List id="search-list">
                {filteredOptions?.map((option, index) => (
                  <Select.Option
                    className="option-wrap"
                    key={option?.value}
                    value={option?.value}
                    id={`option-${index}`}
                    onClick={
                      !disabled
                        ? () => handleSelectBranch(option?.value)
                        : undefined
                    }
                    aria-selected={selected?.includes(option?.value)}
                  >
                    <Select.Option.Checkbox
                      className="checkbox-wrap"
                      checked={selected?.includes(option?.value)}
                    />
                    <Ellipsis placement="right">
                      <Ellipsis.Content>
                        <span style={{ display: 'flex', alignItems: 'center' }}>
                          {option.label}
                        </span>
                      </Ellipsis.Content>
                    </Ellipsis>
                  </Select.Option>
                ))}
                {!filteredOptions?.length && (
                  <Select.OptionHint>No data found</Select.OptionHint>
                )}
              </Select.List>
              {/* <Box m="8px">
                <Button
                  className="apply-btn"
                  use="primary"
                  w="100%"
                  onClick={() => setVisible(false)}>
                  Apply
                </Button>
              </Box> */}
            </>
          )}
        </Select.Popper>
      </Select>
    </>
  );
}
