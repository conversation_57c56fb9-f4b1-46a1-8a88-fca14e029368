@import '@/styles/variable.scss';

.show-tickets-wrap {
  position: relative;
  min-height: calc(100vh - 130px - var(--banner-height));

  .filter-wrap {
    padding: 15px 0px;
    border-bottom: 1px solid #ededed;

    .filter-btn-wrap {
      .filter-btn {
        color: #2f4b56 !important;
        width: 100%;
        padding: 2px 10px !important;
        cursor: pointer;
        max-width: 105px;
        border: 1px solid $color-primary;

        .MuiButton-icon {
          .MuiSvgIcon-root {
            fill: #2f4b56 !important;
            width: 18px;
            height: 18px;
          }
        }

        &:hover {
          background-color: white !important;
          box-shadow: none !important;
        }
      }

      @media (max-width: 767px) {
        width: 100%;
      }
    }

    .filter-search-wrap {
      .search-filter-wrap {
        .filter-search {
          width: 100%;
          max-width: 300px !important;
          height: 31px;

          .MuiInputBase-root {
            .MuiInputBase-input {
              padding: 5.5px 0px 5.5px 16px !important;
            }

            fieldset {
              border-width: 1px;
            }
          }

          @media (max-width: 767px) {
            max-width: 100% !important;
          }
        }

        .search-btn {
          max-width: 75px;
          background-color: $color-primary !important;
          color: white !important;
          border: none !important;
          padding: 3px 20px !important;

          &:hover {
            background-color: $color-primary !important;
            color: white !important;
          }
        }

        @media (max-width: 767px) {
          width: 100%;
        }
      }

      .sorting-wrap {
        line-height: 0px;

        .sorting-icon-wrap {
          fill: $color-primary;
        }
      }

      @media (max-width: 767px) {
        width: 100%;
      }
    }

    @media (max-width: 767px) {
      flex-direction: column;
      row-gap: 15px;
    }
  }

  .ticket-container {
    display: flex;
    transition: margin-left 0.3s ease;
    width: 100%;
    gap: 20px;

    .tickets {
      width: 100%;
      max-width: 300px;

      @media (max-width: 991px) {
        display: none;
      }
    }

    .menu-icon-wrap {
      display: none;

      @media (max-width: 991px) {
        display: block;
      }
    }

    .drawer-toggle-icon {
      cursor: pointer;
    }

    .tickets-details {
      position: relative;
      z-index: 1;
      width: 75%;

      @media (max-width: 1199px) {
        width: 66%;
      }

      @media (max-width: 991px) {
        width: 93%;
      }

      @media (max-width: 767px) {
        width: 91%;
      }

      @media (max-width: 575px) {
        width: 87%;
      }
    }

    @media (max-width: 374px) {
      gap: 10px;
    }
  }

  .support-ticket-container {
    display: flex;
    display: none;

    .drawer {
      position: absolute;
      top: 0;
      left: -40px;
      width: 0;
      height: 100%;
      background-color: #fff;
      overflow: hidden;
      transition: width 0.3s ease;
      box-shadow: 0px 0px 1px #53545c;
      z-index: 1000;

      @media (max-width: 767px) {
        left: -15px;
      }
    }

    .drawer.open {
      width: 300px;

      .drawer-content {
        padding: 35px 20px;
        position: relative;

        .close-icon {
          position: absolute;
          top: 0px;
          right: 0px;
          cursor: pointer;
        }
      }

      @media (max-width: 575px) {
        width: 286px;
      }
    }

    @media (max-width: 991px) {
      display: block;
    }
  }
}

.ticket-filter-drawer {
  z-index: 1300 !important;
}

.sorting-popover {
  .MuiPaper-root {
    z-index: 99999 !important;
    width: auto !important;
    margin-top: 8px;
    padding: 10px 20px;
  }

  .sort-option {
    .options {
      cursor: pointer;
      padding: 10px 0px;
      text-align: left;
    }
  }
}
