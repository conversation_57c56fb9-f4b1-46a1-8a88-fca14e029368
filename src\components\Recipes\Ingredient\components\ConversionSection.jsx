import React from 'react';
import { Box, Typography, Tooltip } from '@mui/material';
import { FieldArray } from 'formik';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import InfoIcon from '@mui/icons-material/Info';
import { allowedOnlyNumbers } from '@/helper/common/commonFunctions';

const ConversionSection = ({
  values,
  errors,
  touched,
  handleBlur,
  handleChange,
  setFieldValue,
  isDefault,
  unitsOfMeasureOptions,
}) => {
  return (
    <>
      <Box>
        <FieldArray name="conversions">
          {({ push, remove }) => (
            <>
              <Box className="d-flex align-center justify-space-between gap-sm pt16 pb8">
                <Box className="d-flex align-center gap-sm">
                  <Typography className="sub-header-text">
                    Conversions
                  </Typography>
                  <Tooltip
                    arrow
                    title={
                      <Typography className="sub-title-text">
                        Allow measurements in the 'from' quantity and unit to be
                        converted to the 'to' quantity and unit. The default
                        conversion of volume to mass are made in 1:1 proportion
                        (i.e. 1l = 1kg). For example, an egg weighs 60g, so
                        you'd enter: From: 1ea, To: 60g. Or if you know that a
                        box of 12 eggs weighs 720g, you can enter From: 12ea,
                        To: 720g.
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                  >
                    <InfoIcon className="info-icon cursor-pointer" />
                  </Tooltip>
                </Box>
                <CustomButton
                  type="button"
                  className="mt8"
                  onClick={() =>
                    push({
                      from_value: '',
                      from_unit: '',
                      to_value: '',
                      to_unit: '',
                    })
                  }
                  disabled={isDefault}
                >
                  + Add conversion
                </CustomButton>
              </Box>
              {values.conversions.map((conversion, index) => (
                <Box
                  key={index}
                  className="ingredient-conversion-main-grid-container"
                >
                  <Box className="ingredient-conversion-grid-container">
                    <CustomTextField
                      name={`conversions[${index}].from_value`}
                      value={conversion.from_value}
                      placeholder="Quantity"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        touched?.conversions?.[index]?.from_value &&
                          errors?.conversions?.[index]?.from_value
                      )}
                      helperText={
                        touched?.conversions?.[index]?.from_value &&
                        errors?.conversions?.[index]?.from_value
                      }
                      disabled={isDefault}
                      onInput={(e) => allowedOnlyNumbers(e)}
                    />

                    <CustomSelect
                      name={`conversions[${index}].from_unit`}
                      options={unitsOfMeasureOptions}
                      value={unitsOfMeasureOptions.find((opt) => {
                        return opt.value === conversion.from_unit;
                      })}
                      onChange={(option) =>
                        setFieldValue(
                          `conversions[${index}].from_unit`,
                          option?.value
                        )
                      }
                      placeholder="Unit"
                      isDisabled={isDefault}
                      isClearable={false}
                    />
                  </Box>
                  <Box>
                    <Typography className="title-text">To</Typography>
                  </Box>
                  <Box className="ingredient-conversion-grid-container">
                    <CustomTextField
                      name={`conversions[${index}].to_value`}
                      value={conversion.to_value}
                      placeholder="Quantity"
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        touched?.conversions?.[index]?.to_value &&
                          errors?.conversions?.[index]?.to_value
                      )}
                      helperText={
                        touched?.conversions?.[index]?.to_value &&
                        errors?.conversions?.[index]?.to_value
                      }
                      disabled={isDefault}
                      onInput={(e) => allowedOnlyNumbers(e)}
                    />

                    <CustomSelect
                      name={`conversions[${index}].to_unit`}
                      options={unitsOfMeasureOptions}
                      value={unitsOfMeasureOptions.find(
                        (opt) => opt.value === conversion.to_unit
                      )}
                      onChange={(option) =>
                        setFieldValue(
                          `conversions[${index}].to_unit`,
                          option?.value
                        )
                      }
                      placeholder="Unit"
                      isDisabled={isDefault}
                      isClearable={false}
                    />
                  </Box>

                  <Box
                    className="cursor-pointer"
                    style={{ display: isDefault ? 'none' : 'flex' }}
                    onClick={() => (isDefault ? null : remove(index))}
                  >
                    <DeleteIcon />
                  </Box>
                </Box>
              ))}

              {/* <CustomButton
                type="button"
                className="mt8"
                onClick={() =>
                  push({
                    from_value: '',
                    from_unit: '',
                    to_value: '',
                    to_unit: '',
                  })
                }
                disabled={isDefault}
              >
                + Add conversion
              </CustomButton> */}
            </>
          )}
        </FieldArray>
      </Box>
    </>
  );
};

export default ConversionSection;
