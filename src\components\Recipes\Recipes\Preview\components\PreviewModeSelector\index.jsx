'use client';
import React, { useState } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './PreviewModeSelector.scss';

const PreviewModeSelector = () => {
  const [selectedMode, setSelectedMode] = useState('head-chef');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const previewModes = [
    {
      id: 'head-chef',
      label: 'Head Chef',
      description: 'Operational data with cost calculations and specifications',
      icon: 'ChefHat',
      color: 'primary',
    },
    {
      id: 'user',
      label: 'User',
      description: 'Dietary information and visual recipe content',
      icon: 'User',
      color: 'secondary',
    },
    {
      id: 'organizational',
      label: 'Organizational',
      description: 'Standardized processes and portion management',
      icon: 'Building2',
      color: 'accent',
    },
  ];

  const currentMode = previewModes.find((mode) => mode.id === selectedMode);

  const handleModeChange = (modeId) => {
    setSelectedMode(modeId);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  return (
    <div className="preview-mode-selector">
      {/* Desktop: Horizontal Button Group */}
      <div className="preview-mode-selector__desktop-group">
        {previewModes.map((mode) => (
          <button
            key={mode.id}
            onClick={() => handleModeChange(mode.id)}
            className={`preview-mode-selector__button ${
              selectedMode === mode.id
                ? `preview-mode-selector__button--active preview-mode-selector__button--${mode.color}`
                : ''
            }`}
            title={mode.description}
          >
            <Icon
              name={mode.icon}
              size={16}
              color={selectedMode === mode.id ? 'white' : 'currentColor'}
            />
            <span className="preview-mode-selector__button-text preview-mode-selector__button-text--desktop">
              {mode.label}
            </span>
            <span className="preview-mode-selector__button-text preview-mode-selector__button-text--mobile">
              {mode.label.split(' ')[0]}
            </span>
          </button>
        ))}
      </div>

      {/* Mobile: Dropdown Selector */}
      <div className="preview-mode-selector__mobile-dropdown">
        <button
          onClick={toggleDropdown}
          className="preview-mode-selector__dropdown-trigger"
        >
          <div className="preview-mode-selector__dropdown-trigger-content">
            <Icon name={currentMode.icon} size={16} color="currentColor" />
            <span>{currentMode.label}</span>
          </div>
          <Icon
            name={isDropdownOpen ? 'ChevronUp' : 'ChevronDown'}
            size={16}
            color="currentColor"
          />
        </button>

        {isDropdownOpen && (
          <div className="preview-mode-selector__dropdown-menu">
            {previewModes.map((mode, index) => (
              <button
                key={mode.id}
                onClick={() => handleModeChange(mode.id)}
                className={`preview-mode-selector__dropdown-item ${
                  selectedMode === mode.id
                    ? 'preview-mode-selector__dropdown-item--active'
                    : ''
                } ${
                  index === 0
                    ? 'preview-mode-selector__dropdown-item--first'
                    : ''
                } ${
                  index === previewModes.length - 1
                    ? 'preview-mode-selector__dropdown-item--last'
                    : ''
                }`}
              >
                <Icon
                  name={mode.icon}
                  size={16}
                  color={
                    selectedMode === mode.id
                      ? 'var(--color-primary)'
                      : 'currentColor'
                  }
                />
                <div className="preview-mode-selector__dropdown-item-content">
                  <span className="preview-mode-selector__dropdown-item-label">
                    {mode.label}
                  </span>
                  <span className="preview-mode-selector__dropdown-item-description">
                    {mode.description}
                  </span>
                </div>
                {selectedMode === mode.id && (
                  <Icon
                    name="Check"
                    size={16}
                    color="var(--color-primary)"
                    className="preview-mode-selector__dropdown-item-check"
                  />
                )}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Mode Description (Desktop only) */}
      <div className="preview-mode-selector__description">
        <Icon name="Info" size={14} color="currentColor" />
        <span className="preview-mode-selector__description-text">
          {currentMode.description}
        </span>
      </div>
    </div>
  );
};

export default PreviewModeSelector;
